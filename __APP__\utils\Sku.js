Object.defineProperty(exports,"__esModule",{value:!0});var t=function(){function t(t,s){for(var e=0;e<s.length;e++){var i=s[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(s,e,i){return e&&t(s.prototype,e),i&&t(s,i),s}}();var s=function(){function s(t){!function(t,s){if(!(t instanceof s))throw new TypeError("Cannot call a class as a function")}(this,s),this.exists=!0,this.goods=t,this.labels={},this.display=!1,this.selected={},this.detail={},this.isReady=!1,this.num=1,this.stock=0,this.skuText="",this.skuKeys="",this.skuValues="",this.action="",this.skuStocks=t.goodsStocks,this.next=!1,this.disabledSkuValues={},this.init()}return t(s,[{key:"init",value:function(){for(var t in this.labels=this.goods.labels,(!this.labels||this.labels.length<1)&&(this.exists=!1),this.labels){var s=this.labels[t].key;this.selected[s]=null,this.skuKeys+=s+" "}this.exists||(this.stock=this.skuStocks[0].stock),this.disabledSkuValues=this.grepDisabledskuValues()}},{key:"select",value:function(t,s){var e=this.selected[t];this.selected[t]=e==s?null:s,this.isReady=this.joinSkuText(),this.isReady?(this.fetchSelectedSkuDetail(),this.setSkuStock(this.skuText),this.num=1):this.detail={},this.disabledSkuValues=this.grepDisabledskuValues()}},{key:"grepDisabledskuValues",value:function(){var t=this.selected,s={};for(var e in t){var i=this.getSkuKeysCondition(e),u=this.getRemainSkuValues(e),n=this.grepRemainSkuStocks(i),a=function(t){var e=u[t];null==n.filter((function(t){return-1!=t.sku.indexOf(e)})).find((function(t){return 0!=t.stock}))&&(s[e]=!0)};for(var l in u)a(l)}return s}},{key:"getSkuKeysCondition",value:function(t){var s={},e=this.selected;for(var i in e)i!=t&&(s[i]=e[i]);return s}},{key:"grepRemainSkuStocks",value:function(t){var s=t,e=this.skuStocks,i=function(t){var i=s[t];null!=i&&(e=e.filter((function(t){return-1!=t.sku.indexOf(i)})))};for(var u in s)i(u);return e}},{key:"getRemainSkuValues",value:function(t){var s=[],e=this.labels.find((function(s){return s.key==t})).value,i=this.selected[t];for(var u in e){var n=e[u];n!=i&&s.push(n)}return s}},{key:"setNum",value:function(t){this.num=t}},{key:"export",value:function(){return{num:this.num,isReady:this.isReady,detail:this.detail,selected:this.selected,labels:this.labels,display:this.display,exists:this.exists,stock:this.stock,stockText:this.buildStockText(),action:this.action,skuKeys:this.skuKeys,skuText:this.skuText,skuValues:this.skuValues,next:this.buildNextFlag(),disabledSkuValues:this.disabledSkuValues}}},{key:"buildNextFlag",value:function(){return!!(this.exists&&this.isReady&&this.stock>0)||!this.exists&&this.stock>0}},{key:"buildStockText",value:function(){return this.exists?this.isReady?"剩余"+this.stock+"件":"(请选择规格)":"剩余"+this.stock+"件"}},{key:"joinSkuText",value:function(){var t=!0,s="";for(var e in this.selected){var i=this.selected[e];if(null==i){t=!1;break}s+=i+":"}return t&&(s=s.substring(0,s.length-1),this.skuText=s,this.skuValues=s.replace(/:/g," ")),t}},{key:"setSkuStock",value:function(t){var s=this.skuStocks;for(var e in s){var i=s[e];i.sku==t&&(this.stock=i.stock)}}},{key:"fetchSelectedSkuDetail",value:function(){var t=this.goods.goodsSkuInfo.goodsSkuDetails;for(var s in t){var e=t[s];if(e.sku==this.skuText){this.detail=e.goodsSkuDetailBase;break}}}}]),s}();exports.default=s;