Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),r=require("./Http.js"),n=(t=r)&&t.__esModule?t:{default:t};var i=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.url=e,this.list=[],this.start=0,this.count=10,this.processFunc=r,this.loading=!1,this.params=[],this.reachBottom=!1,this.empty=!0,this.toClear=!1}var r,i;return e(t,[{key:"next",value:(r=regeneratorRuntime.mark((function t(e){var r,i;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r={from:this.start,limit:this.count},!this.loading){t.next=4;break}return console.warn("page loading!"),t.abrupt("return",this);case 4:return this.loading=!0,t.prev=5,Object.assign(r,e),t.next=9,n.default.get(this.url,r);case 9:if(!(null===(i=t.sent)||i.length<1)){t.next=13;break}return this.toClear?this.clear():this.reachBottom=!0,t.abrupt("return",this);case 13:return this.empty=!1,this._processData(i),this.toClear?(this.list=i,this.toClear=!1):this.list=this.list.concat(i),this.start+=this.count,i.length<this.count&&(this.reachBottom=!0),t.abrupt("return",this);case 19:return t.prev=19,this.loading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[5,,19,22]])})),i=function(){var t=r.apply(this,arguments);return new Promise((function(e,r){return function n(i,s){try{var a=t[i](s),o=a.value}catch(t){return void r(t)}if(!a.done)return Promise.resolve(o).then((function(t){n("next",t)}),(function(t){n("throw",t)}));e(o)}("next")}))},function(t){return i.apply(this,arguments)})},{key:"reset",value:function(){this.empty=!0,this.toClear=!0,this.start=0,this.reachBottom=!1}},{key:"clear",value:function(){this.toClear=!1,this.start=0,this.list=[]}},{key:"_processData",value:function(t){if(this.processFunc)for(var e in t){var r=this.processFunc(t[e]);r&&(t[e]=r)}}}]),t}();exports.default=i;