Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./../../npm/wepy/lib/wepy.js")),r=(n(require("./../../api/auth.js")),n(require("./../../mixins/base.js")));function n(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(n){function a(){var e,t,n;i(this,a);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return t=n=o(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(c))),n.data={init:!1,detailimg:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(2).jpg","http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(3).jpg"],stepsecend:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(4).jpg"],stepthird:["http://wap.exijiu.cn/Public/Qrcode/images/productCodeDetail.jpg"],checkstyleone:["http://wap.exijiu.cn/Public/Qrcode/images/telQuery.jpg"],checkstyletwo:["http://wap.exijiu.cn/Public/Qrcode/images/messageQuery.jpg"],checkstylethree:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(8).jpg","http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(9).jpg"],checkstylefour:["http://wap.exijiu.cn/Public/Qrcode/images/guide1.jpg","http://wap.exijiu.cn/Public/Qrcode/images/guide2.jpg"],reportenter:["http://wap.exijiu.cn/Public/Qrcode/images/feedback-entry.jpg"],page:{list:[]},commentInit:!1,isShow:!0},n.methods={previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})}},n.computed={},n.components={},n.mixins=[r.default],n.config={navigationBarTitleText:"会员升级攻略"},o(n,t)}var u,c;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default.page),e(a,[{key:"onLoad",value:(u=regeneratorRuntime.mark((function e(r){return r.goodsId,regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.default.setNavigationBarColor({backgroundColor:"#299192",frontColor:"#ffffff"}),this.loaded();case 2:case"end":return e.stop()}}),e,this)})),c=function(){var e=u.apply(this,arguments);return new Promise((function(t,r){return function n(i,o){try{var a=e[i](o),u=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(u)}("next")}))},function(e){return c.apply(this,arguments)})},{key:"onUnload",value:function(){}}]),a}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(a,"pages/customer/upgrade_strategy"));