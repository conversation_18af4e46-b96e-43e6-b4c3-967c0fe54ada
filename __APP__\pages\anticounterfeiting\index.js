Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),t=p(require("./../../npm/wepy/lib/wepy.js")),a=require("./../../config.js"),r=(p(require("./../../store/utils.js")),p(require("./../../api/auth.js"))),n=p(require("./../../api/product.js")),i=p(require("./../../mixins/base.js")),o=p(require("./../../utils/Tips.js")),s=p(require("./../../utils/WxUtils.js")),u=p(require("./../../components/common/copyright.js")),c=p(require("./../../components/template/swiper_bar.js")),d=p(require("./../../components/common/loading.js")),l=p(require("./../../api/member.js")),f=p(require("./../../api/garden.js"));p(require("./../../api/riskControl.js"));function p(e){return e&&e.__esModule?e:{default:e}}function h(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,a){return function r(n,i){try{var o=t[n](i),s=o.value}catch(e){return void a(e)}if(!o.done)return Promise.resolve(s).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(s)}("next")}))}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var y=function(p){function y(){var e,a,r;g(this,y);for(var f=arguments.length,p=Array(f),v=0;v<f;v++)p[v]=arguments[v];return a=r=m(this,(e=y.__proto__||Object.getPrototypeOf(y)).call.apply(e,[this].concat(p))),r.data={qdt:{title:"防伪查询首页"},IntegrationScore:0,isStopClickRaffle:!0,isSticky:!1,isGoBack:!1,isGoToRaffle:!1,isPointSuccess:!1,donateBlessing:"",navHeight:"",navTop:"",init:!1,queryResult:"真",queryTips:"该产品为真品，请放心饮用！",qrCode:null,showqrCode:null,productInfo:null,jifenShopProds:{},redirect:!1,imgalist:["http://wap.exijiu.cn/Public/Qrcode/default/img/ewm.jpg"],detailimg:["http://wap.exijiu.cn/Public/Qrcode/images/fwcx.jpg"],validategetscore:{},dataMsCenterUser:!1,code:"",isRedpacket:!1,wineCount:0,integration:0,isAgreeToRaffle:!1,isBoxDonate:!1,happySeasonPrize:{},isRedirectRedpacket:!1,inActiveTime:!1,roleType:1,isMd:!1,isQualityInspector:!1,swiperInfo:[],os:"",phone_model:"",brower:"",networkType:"",platform:"",userInfo:!1,type:4,visible:!1,temporaryActivity:!1,activityMaskShow:!1,raffleResultShow:!1,jphJiuqian:[],jphJiuzhong:[],jphJiuhou:[],alertBoxBanners:[],alertBoxBannersIsHidden:!0,jiaocangGiveWineActUrl:!1,agreeRule:!0,ruleImg:"https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/rule.png?v=1.0",giftTopImg:"https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/get-gift.png?v=1.0",donateImg:"https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/donate.png?v=1.0",subscribeTmpId:"0Q3ZllIL1xC_6s3aLlisoOT4s0Wmo-0OwMurKWap8S0",hasBottleInnerQrcode:!1},r.watch={raffleResultShow:function(e,t){!e&&this.jiaocangGiveWineActUrl&&wx.navigateTo({url:this.jiaocangGiveWineActUrl})}},r.methods={theLast4DigitsInput:function(e){console.log(e.detail.value,"theLast4DigitsInput"),this.validategetscore.theLast4Digits=e.detail.value,e.detail.value?this.isStopClickRaffle=!1:this.isStopClickRaffle=!0,this.$apply()},closePointConfirm:function(){this.isPointSuccess=!1,this.raffleResultShow=!1,this.$apply()},viewScroll:function(e){e.detail.scrollTop>this.navHeight?this.isSticky=!0:this.isSticky=!1,this.$apply()},bindDonateBlessing:function(e){console.log(e.detail.value,"bindDonateBlessing"),this.donateBlessing=e.detail.value,this.$apply()},closeRuleModal:function(){this.activityMaskShow=!1,this.$apply()},openRuleModal:function(){this.activityMaskShow=!0,this.$apply()},agreeToRaffle:function(){var e=this;return h(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(e.agreeRule){a.next=4;break}return o.default.toasts("请先阅读并同意用户服务协议和隐私保护指引"),t.default.setStorageSync("agreeToRaffle",""),a.abrupt("return");case 4:if(t.default.setStorageSync("agreeToRaffle","true"),e.activityMaskShow=!1,!e.isGoToRaffle){a.next=27;break}return a.prev=7,o.default.longLoading("抽奖中....."),a.next=11,n.default.validateBottleInnerCodeAndGetScore(e.validategetscore);case 11:r=a.sent,e.happySeasonPrize=r.happySeasonPrize,e.raffleResultShow=!0,e.jiaocangGiveWineActUrl=r.jiaocangGiveWineActUrl,e.$apply(),console.log(e.happySeasonPrize,"-----扫码欢乐季抽奖结果-----"),o.default.loaded(),a.next=26;break;case 20:return a.prev=20,a.t0=a.catch(7),t.default.removeStorage({key:"userLocation"}),o.default.loaded(),a.next=26,o.default.confirm(a.t0.message);case 26:e.isGoToRaffle=!1;case 27:l.default.saveLastAuth(),e.$apply();case 29:case"end":return a.stop()}}),a,e,[[7,20]])})))()},goToDonateRule:function(){var e="/pages/web/webView?url="+t.default.$instance.globalData.xijiuweixinUrl+"%2FXjhyjlb%2FE20220415%2Fwlak_intro";this.$navigate(e)},viewRaffleRecords:function(e){"gift"===e&&(this.raffleResultShow=!1,this.$apply());var a="/pages/web/webView?url="+t.default.$instance.globalData.xijiuweixinUrl+"%2FXjhyjlb%2FE20220415%2Fjiangpin%3Fid%3D"+this.data.happySeasonPrize.id;this.$navigate(a)},cancelBoxDonate:function(){this.isBoxDonate=!1,this.$apply()},confirmDonate:function(){var e=this;return h(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.data.donateBlessing){t.next=3;break}return o.default.confirm("捐赠语不能为空"),t.abrupt("return");case 3:return o.default.longLoading("捐赠中..."),t.prev=4,t.next=7,n.default.confirmDonate(e.data.happySeasonPrize.id,e.data.donateBlessing);case 7:t.sent,e.raffleResultShow=!1,t.next=17;break;case 11:return t.prev=11,t.t0=t.catch(4),o.default.loaded(),t.next=16,o.default.confirm(t.t0.message);case 16:e.raffleResultShow=!1;case 17:e.$apply(),o.default.loaded();case 19:case"end":return t.stop()}}),t,e,[[4,11]])})))()},getPoints:function(){var e=this;return h(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o.default.longLoading("获取中..."),t.prev=1,t.next=4,n.default.updateToRedBox(e.data.happySeasonPrize.id,0);case 4:t.sent,e.raffleResultShow=!1,t.next=14;break;case 8:return t.prev=8,t.t0=t.catch(1),o.default.loaded(),t.next=13,o.default.confirm(t.t0.message);case 13:e.raffleResultShow=!1;case 14:e.$apply(),o.default.loaded();case 16:case"end":return t.stop()}}),t,e,[[1,8]])})))()},updateToRedBox:function(){var e=this;return h(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o.default.longLoading("升级中..."),t.prev=1,t.next=4,n.default.updateToRedBox(e.data.happySeasonPrize.id,1);case 4:if(!(a=t.sent).upgradeStatus){t.next=10;break}console.log(a,"升级红包"),e.happySeasonPrize=a,t.next=13;break;case 10:return t.next=12,o.default.confirm("很遗憾升级失败，保留积分");case 12:e.raffleResultShow=!1;case 13:t.next=21;break;case 15:return t.prev=15,t.t0=t.catch(1),o.default.loaded(),t.next=20,o.default.confirm(t.t0.message);case 20:e.raffleResultShow=!1;case 21:e.$apply(),o.default.loaded();case 23:case"end":return t.stop()}}),t,e,[[1,15]])})))()},boxDonate:function(){this.isBoxDonate=!0,this.$apply()},getRedBox:function(){var e=this;return h(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o.default.longLoading("领取中..."),t.prev=1,t.next=4,n.default.getRedBox(e.data.happySeasonPrize.id);case 4:return t.sent,t.next=7,o.default.confirm("该红包已经通过微信零钱的方式发放，请稍后查看“微信支付”消息通知！");case 7:e.raffleResultShow=!1,t.next=16;break;case 10:return t.prev=10,t.t0=t.catch(1),o.default.loaded(),t.next=15,o.default.confirm(t.t0.message);case 15:e.raffleResultShow=!1;case 16:e.$apply(),o.default.loaded();case 18:case"end":return t.stop()}}),t,e,[[1,10]])})))()},drawAddress:function(){this.raffleResultShow=!1,this.$apply();var e="/pages/web/webView?url="+t.default.$instance.globalData.xijiuweixinUrl+"%2FXjhyjlb%2FE20220415%2Faddress%3Fid%3D"+this.data.happySeasonPrize.id+"%26source%3D1";this.$navigate(e)},gotoExchange:function(){this.raffleResultShow=!1,this.$apply();var e="/pages/web/webView?url=https%3A%2F%2Fwap.exijiu.com%2Findex.php%2FXjhyjlb%2FE20220415%2Fexchange%3Fid%3D"+this.data.happySeasonPrize.id;this.$navigate(e)},visible:function(){this.visible=!this.visible,this.$apply()},handleCancel:function(){this.visible=!1,this.$apply()},changeAgreeRule:function(){this.agreeRule=!this.agreeRule},goRules:function(e){var a="",r=t.default.$instance.globalData.jifenShopUrl;if("user"==e?a=encodeURIComponent(r+"/#/pages/notice/detail?id=3"):"privacy"==e&&(a=encodeURIComponent(r+"/#/pages/notice/detail?id=4")),a){var n="/pages/web/webView?url="+a+"&nologin=true";console.log(n),this.$navigate(n)}},closeAlertBoxBanners:function(){t.default.setStorageSync("closeInfoAlertBoxBannersDate",(new Date).getTime()),this.alertBoxBannersIsHidden=!0,this.$apply()},getPhoneNumber:function(e){var t=this;return h(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return r={code:t.code,iv:e.detail.iv,encryptedData:e.detail.encryptedData,regScene:2},a.next=3,l.default.createDataMsCenterUser(r).then((function(e){t.dataMsCenterUser=!0,t.$apply()})).catch((function(e){o.default.confirm(e.message),console.log("err",e)}));case 3:case"end":return a.stop()}}),a,t)})))()},goToValidate:function(){l.default.buryingPoint(67,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType);var e="/pages/anticounterfeiting/validate?qrCode="+this.qrCode+"&isFwInvisibleCode="+this.productInfo.isFwInvisibleCode;this.$navigate(e)},goToYouZanShop:function(){l.default.buryingPoint(71,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),wx.navigateToMiniProgram({appId:"wx8d41cdc44c8aeaab",path:"pages/home/<USER>",success:function(e){console.log(e)}})},goto:function(e){console.log(e),1==e.currentTarget.dataset.id&&l.default.buryingPoint(68,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType);var t=e.currentTarget.dataset.url;t&&this.$navigate(t)},goBack:function(){this.isGoBack?wx.navigateBack({delta:1}):wx.navigateTo({url:"/pages/customer/index"})},gotoWithLogined:function(e){var t=this;return h(regeneratorRuntime.mark((function a(){var r,n,i,u;return regeneratorRuntime.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(r=e.currentTarget.dataset.url,console.log(r),"/pages/customer/goToUmall?pagePath=%2fpages%2findex%2fmy_v2"!=r){a.next=5;break}return a.next=5,l.default.buryingPoint(69,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 5:if("/pages/customer/goToJph"!=r){a.next=8;break}return a.next=8,l.default.buryingPoint(70,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 8:if("/pages/plant/map"!=r){a.next=11;break}return a.next=11,l.default.buryingPoint(76,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 11:if("/pages/common/blank-page/index?weappSharePath=pages%2Fhome%2Fdashboard%2Findex%3Fkdt_id%3D18912739&shopAutoEnter=1&kdt_id=18912739"==r&&(l.default.buryingPoint(71,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType),wx.navigateToMiniProgram({appId:"wxf6bcaec7ff7caac7",success:function(e){console.log(e)}})),"/pages/customer/goToUmall?pagePath=%2fpages%2fclassification%2fclassification"!=r){a.next=15;break}return a.next=15,l.default.buryingPoint(78,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 15:if("/pages/customer/goToUmall?pagePath=%2fpages%2findex%2fgoods_detail%3fid%3d5702"!=r){a.next=18;break}return a.next=18,l.default.buryingPoint(78,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType,e.currentTarget.dataset.id);case 18:if("/pages/customer/goToUmall?pagePath=%2fpages%2findex%2fgoods_detail%3fid%3d5722"!=r){a.next=21;break}return a.next=21,l.default.buryingPoint(78,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType,e.currentTarget.dataset.id);case 21:if(3!=e.currentTarget.dataset.id&&4!=e.currentTarget.dataset.id){a.next=24;break}return a.next=24,l.default.buryingPoint(73,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 24:if(t.dataMsCenterUser){a.next=27;break}return o.default.confirm("请先注册为习酒会员").then((function(e){t.$navigate("/pages/customer/index")})),a.abrupt("return",!1);case 27:if((n=e.currentTarget.dataset.event)&&n>0&&l.default.buryingPoint(n,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType),0!==r.indexOf("wx.navigateToMiniProgram")){a.next=34;break}return(i=s.default.getUrlkey(r)).path=decodeURIComponent(i.path),console.log("res",i),a.abrupt("return",wx.navigateToMiniProgram({appId:i.appId,path:i.path||""}));case 34:if(!(r.indexOf("/pages/customer/goToJph")>-1)){a.next=39;break}return u="",void 0!==e.currentTarget.dataset.to&&(u=e.currentTarget.dataset.to),l.default.checkBindAndGotoJifenShop(u),a.abrupt("return",!0);case 39:return a.next=41,l.default.getAuthData();case 41:t.$navigate(r);case 42:case"end":return a.stop()}}),a,t)})))()},temporaryActivityBoxClose:function(){this.temporaryActivity=!1,this.$apply()},temporaryActivityGoTo:function(){var e=this;return h(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l.default.getAuthData();case 2:e.$redirect(e.temporaryActivity.miniProgram);case 3:case"end":return t.stop()}}),t,e)})))()},scan:function(){var e=this;return h(regeneratorRuntime.mark((function a(){var r,n,i,u,c,d,l;return regeneratorRuntime.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.default.scanCode();case 3:r=a.sent,n=r.result,i=void 0,u="",c=void 0,o.default.loading("查询中"),a.t0=r.scanType,a.next="QR_CODE"===a.t0?12:"CODE_128"===a.t0?19:24;break;case 12:return d=s.default.parseUrl(n),l=s.default.getUrlkey(d.query),"wap.exijiu.cn"==d.host&&(i=l.q,l.n&&(u=l.n)),"fw.gzxijiu.cn"==d.host&&(i=l.qrcode),c="/pages/anticounterfeiting/index?qrCode="+i+"&num="+u,e.$navigate(c),a.abrupt("break",25);case 19:return"0"==(i=n)[0]&&(i=n.slice(1)),c="/pages/anticounterfeiting/index?qrCode="+i,e.$navigate(c),a.abrupt("break",25);case 24:o.default.modal("请重新扫码正确的防伪码！");case 25:a.next=29;break;case 27:a.prev=27,a.t1=a.catch(0);case 29:case"end":return a.stop()}}),a,e,[[0,27]])})))()},inputFourTips:function(){this.hasBottleInnerQrcode=!1}},r.events={},r.$repeat={},r.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},r.$events={},r.components={Copyright:u.default,SwiperBar:c.default,Loading:d.default},r.mixins=[i.default],r.config={navigationBarTitleText:"",navigationStyle:"custom"},m(r,a)}var v,b,x,w,k;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(y,t.default.page),e(y,[{key:"getSwiperData",value:function(){return{data:[{url:"http://wap.exijiu.cn/Public/Qrcode/images/fwcx.jpg"}]}}},{key:"onLoad",value:(k=h(regeneratorRuntime.mark((function e(a){var n,i,o,u,c,d,p,h=a.qrCode,g=a.q,m=(a.productNum,a.theLast4Digits,a.num);return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("num",m),this.navHeight=t.default.$instance.globalData.navHeight,this.navTop=t.default.$instance.globalData.navTop,this.userInfo=t.default.getStorageSync("userInfo"),g&&(n=decodeURIComponent(g),i=s.default.parseUrl(n),o=s.default.getUrlkey(i.query),"wap.exijiu.cn"==i.host&&(h=o.q,o.n&&(m=o.n)),"fw.gzxijiu.cn"==i.host&&(h=o.qrcode)),this.qrCode=h,this.showqrCode=h.replace(/\s/g,"").replace(/(.{4})/g,"$1 "),e.next=9,r.default.login();case 9:if(e.sent){e.next=12;break}return e.abrupt("return");case 12:return e.next=14,l.default.hasDataMsCenterUser();case 14:if(!(u=e.sent)){e.next=19;break}this.dataMsCenterUser=u,e.next=24;break;case 19:return e.next=21,t.default.login();case 21:c=e.sent,d=c.code,this.code=d;case 24:return this.validategetscore.qrCode=h,m&&(this.validategetscore.theLast4Digits=m),l.default.getAuthData(),this.getUserSystemInfo(),this.getNetwork(),e.next=32,f.default.banners(this.type);case 32:return this.swiperInfo=e.sent,e.next=35,f.default.banners(11);case 35:return this.jphJiuqian=e.sent,e.next=38,f.default.banners(12);case 38:return this.jphJiuzhong=e.sent,e.next=41,f.default.banners(13);case 41:return this.jphJiuhou=e.sent,e.next=44,f.default.banners(10);case 44:this.alertBoxBanners=e.sent,this.userInfo=t.default.getStorageSync("userInfo"),this.alertBoxBanners.length>0&&this.userInfo&&(console.log(this.alertBoxBanners),this.alertBoxBannersIsHidden=!1,(p=t.default.getStorageSync("closeInfoAlertBoxBannersDate"))&&(new Date-p)/1e3<86400&&(this.alertBoxBannersIsHidden=!0)),this.$apply();case 48:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"getUserSystemInfo",value:function(){var e=this;wx.getSystemInfo({success:function(t){console.log(t),e.os=t.system?t.system:"",e.phone_model=t.model?t.model:"",e.brower="iPhone"==t.model?"safari":"chrome",e.$apply()}})}},{key:"getNetwork",value:function(){var e=this;wx.getNetworkType({success:function(t){var a=t.networkType?t.networkType:"";e.networkType=a,e.$apply()}})}},{key:"getSubscribe",value:function(){var e=this;t.default.getSetting({withSubscriptions:!0}).then((function(a){if(console.log(a),a.subscriptionsSetting.mainSwitch)return t.default.requestSubscribeMessage({tmplIds:[e.subscribeTmpId]})})).then((function(e){console.log(e)})).catch((function(e){console.log(e)}))}},{key:"getIntegration",value:(w=h(regeneratorRuntime.mark((function e(){var t,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this,0!=this.data.validategetscore.theLast4Digits.length){e.next=5;break}o.default.confirm("您好！防伪码不能为空。"),e.next=46;break;case 5:if(!(this.data.validategetscore.theLast4Digits.length<4)){e.next=9;break}o.default.confirm("您好！防伪码不能少于4位!"),e.next=46;break;case 9:if(!(this.data.validategetscore.theLast4Digits.length>=5)){e.next=13;break}o.default.confirm("您好！您输入的防伪码超过4位，请您核对!"),e.next=46;break;case 13:if(/^[0-9]*$/.test(this.data.validategetscore.theLast4Digits)){e.next=17;break}o.default.confirm("您好！您输入的防伪码格式不正确，请您核对!"),e.next=46;break;case 17:return o.default.longLoading("验证中..."),e.prev=18,e.next=21,n.default.validateBottleInnerCodeAndGetScore(this.validategetscore);case 21:if(!((a=e.sent).loadCount>0)){e.next=26;break}return o.default.longLoading("验证中....."),setTimeout((function(){if(o.default.loaded(),a.temporaryActivity)return t.temporaryActivity=a.temporaryActivity,t.temporaryActivity.miniProgram&&t.temporaryActivity.redirect?(t.$redirect(t.temporaryActivity.miniProgram),!1):(t.$apply(),!1);this.IntegrationScore=a.score,this.raffleResultShow=!0,this.jiaocangGiveWineActUrl=a.jiaocangGiveWineActUrl,this.isPointSuccess=!0,this.$apply()}),1e3*a.loadCount),e.abrupt("return",!1);case 26:if(o.default.loaded(),!a.temporaryActivity){e.next=34;break}if(this.temporaryActivity=a.temporaryActivity,!this.temporaryActivity.miniProgram||!this.temporaryActivity.redirect){e.next=32;break}return this.$redirect(this.temporaryActivity.miniProgram),e.abrupt("return",!1);case 32:return this.$apply(),e.abrupt("return",!1);case 34:this.IntegrationScore=a.score,this.raffleResultShow=!0,this.jiaocangGiveWineActUrl=a.jiaocangGiveWineActUrl,this.isPointSuccess=!0,this.$apply(),e.next=46;break;case 41:return e.prev=41,e.t0=e.catch(18),o.default.loaded(),e.next=46,o.default.confirm(e.t0.message);case 46:case"end":return e.stop()}}),e,this,[[18,41]])}))),function(){return w.apply(this,arguments)})},{key:"getPointAndRaffle",value:(x=h(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=this.data.validategetscore.theLast4Digits.length){e.next=4;break}o.default.confirm("您好！防伪码不能为空。"),e.next=40;break;case 4:if(!(this.data.validategetscore.theLast4Digits.length<4)){e.next=8;break}o.default.confirm("您好！防伪码不能少于4位!"),e.next=40;break;case 8:if(!(this.data.validategetscore.theLast4Digits.length>=5)){e.next=12;break}o.default.confirm("您好！您输入的防伪码超过4位，请您核对!"),e.next=40;break;case 12:if(/^[0-9]*$/.test(this.data.validategetscore.theLast4Digits)){e.next=16;break}o.default.confirm("您好！您输入的防伪码格式不正确，请您核对!"),e.next=40;break;case 16:if(this.getSubscribe(),!t.default.getStorageSync("agreeToRaffle")){e.next=38;break}return e.prev=18,o.default.longLoading("抽奖中....."),e.next=22,n.default.validateBottleInnerCodeAndGetScore(this.validategetscore);case 22:a=e.sent,this.happySeasonPrize=a.happySeasonPrize,this.raffleResultShow=!0,this.jiaocangGiveWineActUrl=a.jiaocangGiveWineActUrl,this.$apply(),console.log(this.happySeasonPrize,"-----扫码欢乐季抽奖结果-----"),o.default.loaded(),e.next=37;break;case 31:return e.prev=31,e.t0=e.catch(18),t.default.removeStorage({key:"userLocation"}),o.default.loaded(),e.next=37,o.default.confirm(e.t0.message);case 37:return e.abrupt("return");case 38:this.isGoToRaffle=!0,this.activityMaskShow=!0;case 40:case"end":return e.stop()}}),e,this,[[18,31]])}))),function(){return x.apply(this,arguments)})},{key:"getOnlineLegouChange",value:(b=h(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=this.data.validategetscore.theLast4Digits.length){e.next=4;break}o.default.confirm("您好！防伪码不能为空。"),e.next=38;break;case 4:if(!(this.data.validategetscore.theLast4Digits.length<4)){e.next=8;break}o.default.confirm("您好！防伪码不能少于4位!"),e.next=38;break;case 8:if(!(this.data.validategetscore.theLast4Digits.length>=5)){e.next=12;break}o.default.confirm("您好！您输入的防伪码超过4位，请您核对!"),e.next=38;break;case 12:if(/^[0-9]*$/.test(this.data.validategetscore.theLast4Digits)){e.next=16;break}o.default.confirm("您好！您输入的防伪码格式不正确，请您核对!"),e.next=38;break;case 16:return o.default.longLoading("验证中..."),e.prev=17,e.next=20,n.default.validateBottleInnerCodeAndGetChance(this.validategetscore);case 20:if(t=e.sent,o.default.loaded(),!t.temporaryActivity){e.next=29;break}if(this.temporaryActivity=t.temporaryActivity,!this.temporaryActivity.miniProgram||!this.temporaryActivity.redirect){e.next=27;break}return this.$redirect(this.temporaryActivity.miniProgram),e.abrupt("return",!1);case 27:return this.$apply(),e.abrupt("return",!1);case 29:return e.next=31,o.default.confirm(t.tips);case 31:e.next=38;break;case 33:return e.prev=33,e.t0=e.catch(17),o.default.loaded(),e.next=38,o.default.confirm(e.t0.message);case 38:case"end":return e.stop()}}),e,this,[[17,33]])}))),function(){return b.apply(this,arguments)})},{key:"submitForm",value:function(){this.isStopClickRaffle=!1,this.$apply(),this.dataMsCenterUser&&(this.productInfo.isHappySeason&&"boxCode"!=this.productInfo.codeType?this.getPointAndRaffle():this.productInfo.isHappySeason||!this.productInfo.isJifenProduct||this.inActiveTime||"boxCode"==this.productInfo.codeType?this.productInfo.isLegouActive&&!this.inActiveTime&&"boxCode"!=this.productInfo.codeType&&this.getOnlineLegouChange():this.getIntegration())}},{key:"gokefu",value:function(){this.$navigate("/pages/customer/customer_service")}},{key:"goToRedpacket",value:function(){var e="/pages/web/redpacket?qrCode="+this.qrCode;this.$redirect(e)}},{key:"goToMdascription",value:function(){var e="pages/home?boxCode="+this.productInfo.boxCode;"boxCode"!=this.productInfo.codeType&&(e="pages/home?bottleCode="+this.productInfo.fangWeiCode),console.log(e),wx.navigateToMiniProgram({appId:"wxfec2bfa2faefe51c",path:e})}},{key:"goToInspect",value:function(){var e="/pages/inspect/wuliu?wuliuqrCode="+this.qrCode;this.$redirect(e)}},{key:"gotoRule",value:function(){this.$redirect("/pages/anticounterfeiting/active_rule")}},{key:"onShow",value:(v=h(regeneratorRuntime.mark((function e(){var i,u,c,d,f,p,h,g,m,y=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.init){e.next=2;break}return e.abrupt("return",!0);case 2:return console.log(">_< onShow init",this.init),e.next=6,r.default.login();case 6:if(e.sent){e.next=9;break}return e.abrupt("return");case 9:return i=this,e.next=12,l.default.getRoleType();case 12:if(u=e.sent,c=u.roleType,this.roleType=c,c!=a.roleType.inspector.value){e.next=20;break}return this.goToInspect(),e.abrupt("return",!1);case 20:c==a.roleType.store.value?this.isMd=!0:c==a.roleType.quality_inspector.value&&(this.isQualityInspector=!0);case 21:if(d=t.default.getStorageSync("userLocation"),console.log(d),d.DateGetTime&&d.latitude&&!(((new Date).getTime()-d.DateGetTime)/1e3>300)){e.next=29;break}return e.next=26,t.default.getSetting().then((function(e){if(!e.authSetting["scope.record"])return t.default.authorize({scope:"scope.userLocation"})})).then((function(e){return t.default.getLocation({type:"gcj02"})})).then((function(e){return d={latitude:e.latitude,longitude:e.longitude,speed:e.speed,accuracy:e.accuracy,DateGetTime:(new Date).getTime()},t.default.setStorage({key:"userLocation",data:d}),e})).catch((function(e){return s.default.backOrNavigate("/pages/auth/setting"),!1}));case 26:if(e.sent){e.next=29;break}return e.abrupt("return");case 29:if(d.latitude&&d.longitude||o.default.confirm("未获取到您的位置信息 （请确认您的手机已开启定位）").then((function(e){return s.default.backOrNavigate("/pages/auth/setting"),!1})),this.validategetscore.lat=d.latitude,this.validategetscore.lng=d.longitude,this.productInfo=t.default.getStorageSync("productInfo."+this.qrCode),this.productInfo&&this.productInfo.DateGetTime&&!(((new Date).getTime()-this.productInfo.DateGetTime)/1e3>86400)){e.next=50;break}return e.prev=34,f={qrCode:this.qrCode,lat:d.latitude,lng:d.longitude},e.next=38,n.default.infoWithLocation(f);case 38:this.productInfo=e.sent,this.productInfo.DateGetTime=(new Date).getTime(),console.log(this.productInfo,"产品信息"),this.productInfo.productCode&&(this.productInfo.productCode=(this.productInfo.productCode+"XXXX").replace(/\s/g,"").replace(/(.{4})/g,"$1 ")),e.next=49;break;case 44:return e.prev=44,e.t0=e.catch(34),t.default.removeStorage({key:"userLocation"}),o.default.confirm(e.t0.message).then((function(e){y.$navigate("/pages/home/<USER>")})),e.abrupt("return");case 49:t.default.setStorage({key:"productInfo."+this.qrCode,data:this.productInfo});case 50:return getCurrentPages().forEach((function(e){"pages/home/<USER>"===e.route&&(y.isGoBack=!0)})),console.log(this.isGoBack,"是否返回"),e.next=55,n.default.getThisYearWineData();case 55:return p=e.sent,this.wineCount=p.count,console.log(p.count,"饮酒量"),e.next=60,l.default.getMemberInfo();case 60:if(h=e.sent,this.integration=h.integration,console.log(h,"会员信息"),(this.productInfo.isHappySeason||this.productInfo.activeInfo&&this.productInfo.activeInfo.name)&&"boxCode"!=this.productInfo.codeType&&(this.activityMaskShow=!0),(this.productInfo.productCode||12!=this.qrCode.length&&14!=this.qrCode.length)&&"bottleCode"!=this.productInfo.codeType){e.next=79;break}return e.prev=65,e.next=68,n.default.isRedpacket(this.qrCode);case 68:if(g=e.sent,this.isRedpacket=g.isRedpacket,this.isRedirectRedpacket=g.isRedirect,this.inActiveTime=g.inActiveTime,!g.isRedirect){e.next=74;break}return e.abrupt("return",this.goToRedpacket());case 74:e.next=78;break;case 76:e.prev=76,e.t1=e.catch(65);case 78:console.log("isRedpacket finish");case 79:if(this.productInfo){e.next=81;break}return e.abrupt("return",!1);case 81:this.productInfo.productionDate2=this.productInfo.productionDate,this.productInfo.productionDate=this.productInfo.lastQueryDate&&this.productInfo.productionDate.substring(0,10),this.productInfo.lastQueryDate=this.productInfo.lastQueryDate&&this.productInfo.lastQueryDate.substring(0,10),m=this.productInfo.productCode?this.productInfo.productCode.length:0,this.productInfo.productCode=22==m?this.productInfo.productCode.substring(0,18):20==m?this.productInfo.productCode.substring(0,16):this.productInfo.productCode,this.productInfo.productName?this.productInfo.queryTimes>5&&this.productInfo.queryTimes<50?this.queryResult="慎":this.productInfo.queryTimes>50&&(this.queryResult="疑"):this.queryResult="假",this.productInfo.hasBottleInnerQrcode&&!this.validategetscore.theLast4Digits&&(this.hasBottleInnerQrcode=!0),t.default.$instance.globalData.qDTracker.track("用户扫码进入防伪页面",{fpbh:this.productInfo.swptOrder,fxbh:this.productInfo.fxbh,khbh:this.productInfo.jxs_id,khmc:this.productInfo.jxsName,spbh:this.productInfo.productErpNo,spmc:this.productInfo.productName,md_id:this.productInfo.md_id,bottle_code:this.productInfo.bottleCode,box_code:this.productInfo.boxCode,fang_wei_code:this.productInfo.fangWeiCode,production_date:this.productInfo.productionDate,latitude:this.validategetscore.lat,longitude:this.validategetscore.lng,jxs_pianqu:this.productInfo.pianqu,code_type:this.productInfo.codeType,first_query_date:this.productInfo.firstQueryDate,last_query_date:this.productInfo.lastQueryDate,query_code:this.productInfo.qrCode,query_times:this.productInfo.queryTimes,query_load_count:this.productInfo.loadCount,has_bottle_inner_qrcode:this.productInfo.hasBottleInnerQrcode?"有":"无",is_fw_invisible_code:this.productInfo.isFwInvisibleCode?"可以":"不可以",product_box_code:this.productInfo.productCode,is_happy_season:this.productInfo.isHappySeason?"是":"否",is_jifen_product:this.productInfo.isJifenProduct?"是":"否",is_legou_active:this.productInfo.isLegouActive?"是":"否",query_result:this.queryResult}),this.productInfo.loadCount>0?(o.default.longLoading("加载中...."),setTimeout((function(){i.loaded(),i.validategetscore.theLast4Digits&&i.submitForm()}),1e3*this.productInfo.loadCount)):(i.loaded(),i.validategetscore.theLast4Digits&&i.submitForm());case 90:case"end":return e.stop()}}),e,this,[[34,44],[65,76]])}))),function(){return v.apply(this,arguments)})}]),y}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(y,"pages/anticounterfeiting/index"));