Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,i=function(){function t(t,i){for(var e=0;e<i.length;e++){var s=i[e];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(i,e,s){return e&&t(i.prototype,e),s&&t(i,s),i}}(),e=require("./../../npm/wepy/lib/wepy.js"),s=(t=e)&&t.__esModule?t:{default:t};function a(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}function o(t,i){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!i||"object"!=typeof i&&"function"!=typeof i?t:i}var h=function(t){function e(){var t,i,s;a(this,e);for(var h=arguments.length,n=Array(h),r=0;r<h;r++)n[r]=arguments[r];return i=s=o(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(n))),s.props={tabs:{type:Array,default:null}},s.data={stv:{windowWidth:0,lineWidth:0,offset:0,tStart:!1},activeTab:0,lastActiveTab:0,startX:0,isMove:!1,tabsCount:0,offset:0,scrollLeft:0,topStartX:0,maxCount:5},s.methods={touchStart:function(t){this.isMove=!1;var i=t.touches[0],e=i.clientX,s=i.clientY;this.startX=e,this.tapStartX=e,this.tapStartY=s,this.tapStartTime=t.timeStamp,this.stv.tStart=!0},touchMove:function(t){this.isMove=!0;var i=t.touches[0].clientX,e=this.startX-i;this.startX=i,this.stv.offset+=e,this.stv.offset<=0?this.stv.offset=0:this.stv.offset>=this.stv.windowWidth*(this.tabs.length-1)&&(this.stv.offset=this.stv.windowWidth*(this.tabs.length-1))},touchCancel:function(t){this._updateScrollView(t)},touchEnd:function(t){this._updateScrollView(t)},handlerTabTap:function(t,i){this.activeTab!==t&&(this.activeTab=t,this.stv.offset=this.stv.windowWidth*t,this._updateTopScrollView(i))},TopViewTouchStart:function(t){var i=t.touches[0].clientX;this.topStartX=i,this.stv.tStart=!0},TopViewTouchMove:function(t){var i=t.touches[0].clientX,e=this.topStartX-i;this.topStartX=i,this.scrollLeft+=e;var s=this.stv.lineWidth*(this.tabs.length-this.maxCount);this.scrollLeft<=0?this.scrollLeft=0:this.scrollLeft>=s&&(this.scrollLeft=s)},TopViewTouchEnd:function(){this.stv.tStart=!1}},o(s,i)}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function, not "+typeof i);t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(t,i):t.__proto__=i)}(e,s.default.component),i(e,[{key:"onLoad",value:function(){var t=this;wx.getSystemInfo({success:function(i){t.stv.windowWidth=i.windowWidth,t.tabsCount=t.tabs.length,t.tabsCount>t.maxCount&&(t.tabsCount=t.maxCount),t.stv.lineWidth=t.stv.windowWidth/t.tabsCount,t.$apply()}})}},{key:"_updateScrollView",value:function(t){if(this.isMove){var i=t.changedTouches[0],e=i.clientX,s=i.clientY,a=t.timeStamp,o=this.stv,h=o.offset,n=o.windowWidth;if(a-this.tapStartTime<=300&&Math.abs(this.tapStartY-s)<50)this.tapStartX-e>5&&this.activeTab<this.tabs.length-1?this.activeTab++:this.activeTab>0&&this.activeTab--,this.stv.offset=this.stv.windowWidth*this.activeTab;else{var r=Math.round(h/n);this.activeTab=r,this.stv.offset=this.stv.windowWidth*r}this._updateTopScrollView(),this.stv.tStart=!1}}},{key:"_updateTopScrollView",value:function(t){if(this.lastActiveTab!==this.activeTab){var i=this.activeTab*this.stv.lineWidth;if(i>=this.scrollLeft+this.stv.lineWidth&&i<=this.scrollLeft+3*this.stv.lineWidth)this.lastActiveTab=this.activeTab;else{this.lastActiveTab-this.activeTab>0?this.scrollLeft=(this.activeTab-1)*this.stv.lineWidth:this.scrollLeft=(this.activeTab-this.maxCount+2)*this.stv.lineWidth;var e=(this.tabs.length-this.tabsCount)*this.stv.lineWidth;this.scrollLeft<0?this.scrollLeft=0:this.scrollLeft>e&&(this.scrollLeft=e),this.lastActiveTab=this.activeTab}}}}]),e}();exports.default=h;