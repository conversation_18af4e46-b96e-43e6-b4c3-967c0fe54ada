Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=u(require("./../../npm/wepy/lib/wepy.js")),r=u(require("./../../mixins/base.js")),o=require("./../../npm/wepy-redux/lib/index.js"),i=u(require("./../../store/utils.js"));function u(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=(0,o.connect)({shop:i.default.get("shop")})(e=function(e){function o(){var e,t,i;a(this,o);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return t=i=s(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(c))),i.data={init:!1},i.methods={image:function(e){n.default.previewImage({current:e,urls:this.shop.images.map((function(e){return e.url}))})},phone:function(){n.default.makePhoneCall({phoneNumber:this.shop.phone})},location:function(){var e=this.shop.longitude,t=this.shop.latitude;n.default.openLocation({latitude:Number(t),longitude:Number(e),name:this.shop.name,address:this.shop.describe})}},i.components={},i.mixins=[r.default],s(i,t)}var i,u;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:(i=regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)})),u=function(){var e=i.apply(this,arguments);return new Promise((function(t,n){return function r(o,i){try{var u=e[o](i),a=u.value}catch(e){return void n(e)}if(!u.done)return Promise.resolve(a).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(a)}("next")}))},function(){return u.apply(this,arguments)})}]),o}())||e;exports.default=c;