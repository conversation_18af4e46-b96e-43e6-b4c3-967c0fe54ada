/**
 * 积分CRM API分析器
 * 专门分析jifenCrmCreateJwt API的参数需求和调用方式
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class JifenCrmAPIAnalyzer {
    constructor() {
        // 已知认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 从JWT中提取的关键信息
        this.memberID = 6865357;
        this.unionID = 'oA4oD1fddsj8tqw_UU2Z52eWTSpg';
        
        // 目标API
        this.targetAPI = 'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt';
        
        // 从之前的错误信息分析得出的参数需求
        this.errorAnalysis = {
            missingTokenError: '请求中缺少用户 token 参数',
            invalidTokenError: '非法的用户 token 参数',
            requiredParams: ['token', 'user_token', 'crmtoken']
        };
        
        console.log('🔧 积分CRM API分析器初始化完成');
        console.log('🎯 专门分析jifenCrmCreateJwt API');
        console.log(`📍 目标API: ${this.targetAPI}`);
    }

    /**
     * 分析API的参数需求
     */
    analyzeAPIParameterRequirements() {
        console.log('\n🔍 分析API的参数需求...');
        
        console.log('📋 基于错误信息的分析:');
        console.log(`- 错误1: "${this.errorAnalysis.missingTokenError}"`);
        console.log(`- 错误2: "${this.errorAnalysis.invalidTokenError}"`);
        console.log('- 结论: API需要一个名为"token"的参数');
        
        console.log('\n💡 可能的token参数格式:');
        console.log('1. 使用现有的Authorization token');
        console.log('2. 使用login_code');
        console.log('3. 使用会员ID');
        console.log('4. 使用unionID');
        console.log('5. 使用特定格式的CRM token');
        
        console.log('\n🎯 参数推测:');
        console.log('基于"jifenCrm"命名，这个API可能是:');
        console.log('- 积分系统的CRM接口');
        console.log('- 需要会员身份验证');
        console.log('- 生成积分系统专用的JWT');
        
        return {
            requiredParam: 'token',
            possibleFormats: ['authorization', 'login_code', 'member_id', 'union_id', 'crm_token'],
            apiPurpose: 'generate_jifen_crm_jwt'
        };
    }

    /**
     * 系统性测试API参数
     */
    async systematicParameterTesting() {
        console.log('\n🧪 开始系统性参数测试...');
        
        // 基于算法分析的精确参数组合
        const parameterSets = [
            // 1. 直接使用Authorization作为token
            {
                name: 'Authorization作为token',
                params: { token: this.validAuth },
                method: 'POST'
            },
            
            // 2. 使用login_code作为token
            {
                name: 'login_code作为token',
                params: { token: this.loginCode },
                method: 'POST'
            },
            
            // 3. 使用会员ID作为token
            {
                name: '会员ID作为token',
                params: { token: this.memberID.toString() },
                method: 'POST'
            },
            
            // 4. 使用unionID作为token
            {
                name: 'unionID作为token',
                params: { token: this.unionID },
                method: 'POST'
            },
            
            // 5. 组合参数 - Authorization + login_code
            {
                name: '组合参数1',
                params: { 
                    token: this.validAuth,
                    login_code: this.loginCode 
                },
                method: 'POST'
            },
            
            // 6. 组合参数 - 会员信息
            {
                name: '组合参数2',
                params: { 
                    token: this.validAuth,
                    member_id: this.memberID,
                    unionid: this.unionID
                },
                method: 'POST'
            },
            
            // 7. CRM特定格式
            {
                name: 'CRM格式1',
                params: { 
                    user_token: this.validAuth,
                    crm_member_id: this.memberID
                },
                method: 'POST'
            },
            
            // 8. 查询参数格式
            {
                name: 'GET查询参数',
                params: { token: this.validAuth },
                method: 'GET'
            },
            
            // 9. 请求头格式
            {
                name: '请求头认证',
                params: {},
                method: 'POST',
                headers: {
                    'Authorization': this.validAuth,
                    'X-Token': this.validAuth,
                    'X-Member-Token': this.validAuth
                }
            },
            
            // 10. 复杂组合
            {
                name: '复杂组合',
                params: {
                    token: this.validAuth,
                    user_token: this.loginCode,
                    member_id: this.memberID,
                    unionid: this.unionID,
                    platform: 'wechat',
                    source: 'miniprogram'
                },
                method: 'POST'
            }
        ];
        
        const results = [];
        
        for (let i = 0; i < parameterSets.length; i++) {
            const testSet = parameterSets[i];
            console.log(`\n🧪 [${i+1}/${parameterSets.length}] 测试: ${testSet.name}`);
            
            try {
                const result = await this.testAPIWithParameters(testSet);
                results.push(result);
                
                if (result.success) {
                    console.log('✅ 测试成功！');
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    
                    // 如果成功，立即验证返回的token
                    if (result.data && (result.data.jwt || result.data.token || result.data.auth)) {
                        const newToken = result.data.jwt || result.data.token || result.data.auth;
                        console.log('🔑 发现新Token，立即验证...');
                        const isValid = await this.validateToken(newToken);
                        
                        if (isValid) {
                            console.log('🎉 新Token验证成功！');
                            return {
                                success: true,
                                newToken: newToken,
                                successfulParams: testSet,
                                allResults: results
                            };
                        }
                    }
                } else {
                    console.log('❌ 测试失败');
                    if (result.data && result.data.msg) {
                        console.log(`📄 错误信息: ${result.data.msg}`);
                        
                        // 分析错误信息的变化
                        if (!result.data.msg.includes('缺少') && !result.data.msg.includes('非法')) {
                            console.log('💡 错误信息有变化，参数可能有效');
                        }
                    }
                }
                
            } catch (error) {
                console.log(`💥 测试异常: ${error.message}`);
                results.push({
                    testSet: testSet,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return {
            success: false,
            message: '所有参数组合都失败了',
            allResults: results
        };
    }

    /**
     * 使用参数测试API
     */
    async testAPIWithParameters(testSet) {
        const { params, method, headers } = testSet;
        
        let requestHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'login_code': this.loginCode,
            ...headers
        };
        
        let url = this.targetAPI;
        let requestData = null;
        
        if (method === 'GET' && Object.keys(params).length > 0) {
            // GET请求，参数作为查询字符串
            const queryString = Object.keys(params)
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
            url = `${this.targetAPI}?${queryString}`;
        } else if (method === 'POST') {
            // POST请求，参数作为请求体
            requestData = params;
        }
        
        const result = await this.makeRequest(url, method, requestData, requestHeaders);
        
        return {
            testSet: testSet,
            success: result.success,
            data: result.data,
            status: result.status
        };
    }

    /**
     * 验证Token
     */
    async validateToken(token) {
        try {
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                {
                    'Content-Type': 'application/json',
                    'Authorization': token,
                    'login_code': this.loginCode,
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
                }
            );
            
            return result.success && result.data && !result.data.err;
        } catch (error) {
            return false;
        }
    }

    /**
     * 分析API响应模式
     */
    analyzeAPIResponsePatterns(results) {
        console.log('\n📊 分析API响应模式...');
        
        const errorMessages = {};
        const statusCodes = {};
        
        results.forEach(result => {
            if (result.data && result.data.msg) {
                const msg = result.data.msg;
                errorMessages[msg] = (errorMessages[msg] || 0) + 1;
            }
            
            const status = result.status || 0;
            statusCodes[status] = (statusCodes[status] || 0) + 1;
        });
        
        console.log('📋 错误信息统计:');
        Object.entries(errorMessages).forEach(([msg, count]) => {
            console.log(`  "${msg}": ${count}次`);
        });
        
        console.log('\n📋 状态码统计:');
        Object.entries(statusCodes).forEach(([code, count]) => {
            console.log(`  ${code}: ${count}次`);
        });
        
        // 分析模式
        console.log('\n💡 模式分析:');
        if (errorMessages['请求中缺少用户 token 参数']) {
            console.log('- API确实需要token参数');
        }
        if (errorMessages['非法的用户 token 参数']) {
            console.log('- API能识别token参数，但格式不正确');
        }
        
        return {
            errorMessages: errorMessages,
            statusCodes: statusCodes,
            patterns: {
                needsToken: !!errorMessages['请求中缺少用户 token 参数'],
                recognizesToken: !!errorMessages['非法的用户 token 参数']
            }
        };
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的积分CRM API分析
     */
    async runCompleteJifenCrmAnalysis() {
        console.log('🚀 开始完整的积分CRM API分析...');
        console.log('🎯 专门分析jifenCrmCreateJwt API');
        
        try {
            // 1. 分析参数需求
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一部分: API参数需求分析');
            console.log('='.repeat(60));
            const paramAnalysis = this.analyzeAPIParameterRequirements();
            
            // 2. 系统性参数测试
            console.log('\n' + '='.repeat(60));
            console.log('🧪 第二部分: 系统性参数测试');
            console.log('='.repeat(60));
            const testResults = await this.systematicParameterTesting();
            
            // 3. 响应模式分析
            console.log('\n' + '='.repeat(60));
            console.log('📊 第三部分: 响应模式分析');
            console.log('='.repeat(60));
            const patternAnalysis = this.analyzeAPIResponsePatterns(testResults.allResults || []);
            
            // 输出最终结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 积分CRM API分析结果');
            console.log('='.repeat(60));
            
            if (testResults.success) {
                console.log('\n🎉 分析成功！发现了有效的API调用方式！');
                console.log(`🔑 新Token: ${testResults.newToken}`);
                console.log(`🎯 成功参数: ${JSON.stringify(testResults.successfulParams.params)}`);
                console.log(`📋 请求方法: ${testResults.successfulParams.method}`);
                
                return {
                    success: true,
                    newToken: testResults.newToken,
                    successfulParams: testResults.successfulParams,
                    analysis: {
                        paramAnalysis: paramAnalysis,
                        patternAnalysis: patternAnalysis
                    }
                };
            } else {
                console.log('\n🤔 未找到有效的API调用方式');
                console.log('💡 但获得了重要的分析信息:');
                
                if (patternAnalysis.patterns.needsToken) {
                    console.log('✅ 确认API需要token参数');
                }
                if (patternAnalysis.patterns.recognizesToken) {
                    console.log('✅ 确认API能识别token参数');
                }
                
                console.log('\n💡 下一步建议:');
                console.log('1. 研究CRM系统的特定token格式');
                console.log('2. 分析其他积分相关的API');
                console.log('3. 寻找CRM token的生成方法');
                
                return {
                    success: false,
                    analysis: {
                        paramAnalysis: paramAnalysis,
                        patternAnalysis: patternAnalysis,
                        testResults: testResults.allResults
                    }
                };
            }
            
        } catch (error) {
            console.log('\n❌ 积分CRM API分析失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = JifenCrmAPIAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new JifenCrmAPIAnalyzer();
    
    console.log('🔍 积分CRM API分析器');
    console.log('🎯 专门分析jifenCrmCreateJwt API的参数需求');
    console.log('🔑 基于错误信息进行精确的参数测试');
    console.log('');
    
    // 运行完整的积分CRM API分析
    analyzer.runCompleteJifenCrmAnalysis().then(result => {
        if (result.success) {
            console.log('\n🎉 积分CRM API分析成功！');
            console.log(`🔑 获得新Token: ${result.newToken}`);
        } else {
            console.log('\n🤔 积分CRM API分析未获得新Token');
            console.log('💡 但获得了重要的API分析信息');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
