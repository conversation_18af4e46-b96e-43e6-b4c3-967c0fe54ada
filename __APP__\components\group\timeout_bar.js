Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=n(require("./../../npm/wepy/lib/wepy.js")),t=n(require("./../../mixins/countdown.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(n){function i(){var e,n,u;o(this,i);for(var a=arguments.length,c=Array(a),f=0;f<a;f++)c[f]=arguments[f];return n=u=r(this,(e=i.__proto__||Object.getPrototypeOf(i)).call.apply(e,[this].concat(c))),u.props={endTime:{default:""},endText:{default:"已结束"},describe:{default:"限时火拼·超值底价"},title:{default:"拼团"}},u.data={},u.methods={},u.watch={endTime:function(e){this.countdowm(e.replace(/-/g,"/"),"endTime")}},u.mixins=[t.default],r(u,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,e.default.component),i}();exports.default=i;