Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./../../npm/wepy/lib/wepy.js")),r=n(require("./../../utils/Tips.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(n){function s(){var e,t,n;o(this,s);for(var u=arguments.length,a=Array(u),f=0;f<u;f++)a[f]=arguments[f];return t=n=i(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(a))),n.props={trade:{},address:{},delilveries:{},selectedDelivery:{}},n.data={confirmStatus:!1},n.methods={confirm:function(e){var t=e.detail;console.info("form_id:",t.formId),this.confirmStatus?this.$emit("confirm",t.formId):r.default.alert(this.confirmText)}},n.computed={confirmText:function(){return this.confirmStatus=!1,this.address?!this.delilveries||this.delilveries.length<1?"暂不支持配送":this.selectedDelivery?(this.confirmStatus=!0,"立即下单"):"选择配送方式":"请选择地址"}},n.events={},i(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,t.default.component),e(s,[{key:"onLoad",value:function(){}}]),s}();exports.default=s;