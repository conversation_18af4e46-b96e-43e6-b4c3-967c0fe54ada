Object.defineProperty(exports,"__esModule",{value:!0});var e=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},r=require("./../../npm/redux-actions/lib/index.js"),n=require("./../types/cache.js");function l(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}exports.default=(0,r.handleActions)(l({},n.SAVE,(function(r,n){var t=n.payload,u=t.key,a=t.value;return e({},r,l({},u,a))})),{member:null,card:null,reduce:null,shop:null,status:null,notices:null,categories:null,limit:null});