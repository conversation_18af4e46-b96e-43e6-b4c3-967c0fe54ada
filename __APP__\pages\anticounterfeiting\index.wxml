<scroll-view scrollY bindscroll="viewScroll" enhanced="enhanced" style="width:100vw;height:100vh;">
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="body" wx:if="{{init}}">
        <view class="rule-activity-box-mask" wx:if="{{activityMaskShow}}">
            <view class="rule-activity-box">
                <view class="rule-activity-box-top">
                    <image bindtap="closeRuleModal" class="rule-activity-box-close-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/rule-close.png"></image>
                </view>
                <view class="rule-activity-content">
                    <view class="rule-activity-content-img-wrapper">
                        <image class="rule-activity-content-img" mode="widthFix" src="https://wap.exijiu.com/Public/Xjhyjlb/E20220415/ldimg/202501/{{productInfo.activeInfo.name?productInfo.activeInfo.name:'happy'}}-rule.png?v=1.0"></image>
                        <view class="agree-box">
                            <checkbox-group bindchange="changeAgreeRule">
                                <checkbox checked="{{agreeRule}}" class="agree">我已阅读并同意</checkbox>
                            </checkbox-group>
                            <view bindtap="goRules" class="agree-a" data-wpygorules-a="user">《用户服务协议》</view>和<view bindtap="goRules" class="agree-a" data-wpygorules-a="privacy">《隐私保护指引》</view>
                        </view>
                    </view>
                    <view class="rule-activity-box-bottom">
                        <button bindtap="agreeToRaffle" class="rule-activity-box-bottom-btn" wx:if="{{isGoToRaffle}}">已同意，去抽奖</button>
                        <button bindtap="agreeToRaffle" class="rule-activity-box-bottom-btn" wx:else>已同意</button>
                    </view>
                </view>
            </view>
        </view>
        <view class="raffle-result-box-mask" wx:if="{{raffleResultShow}}">
            <view class="raffle-result-box">
                <view class="raffle-result-gift" style="background:url({{happySeasonPrize.image}}) no-repeat,url('https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/fire-flower.gif') no-repeat;background-size:100% 100%;" wx:if="{{happySeasonPrize.type===1||happySeasonPrize.type===5}}">
                    <image class="raffle-result-gift-top" src="{{giftTopImg}}"></image>
                    <view class="raffle-result-gift-middle">
                        <text class="raffle-result-gift-middle-name">{{happySeasonPrize.name}}</text>
                    </view>
                    <view class="raffle-result-gift-bottom">
                        <button bindtap="viewRaffleRecords" class="raffle-result-gift-bottom-records" data-wpyviewrafflerecords-a="gift">查看记录</button>
                        <button bindtap="drawAddress" class="raffle-result-gift-bottom-address" wx:if="{{happySeasonPrize.type===1}}">填写地址</button>
                        <button bindtap="gotoExchange" class="raffle-result-gift-bottom-address" wx:if="{{happySeasonPrize.type===5}}">前往兑换</button>
                    </view>
                </view>
                <view class="raffle-result-red-box" wx:if="{{happySeasonPrize.type===2&&!isBoxDonate}}">
                    <image class="raffle-result-red-box-top" src="{{giftTopImg}}"></image>
                    <view class="raffle-result-red-box-middle">
                        <text class="raffle-result-red-box-middle-name">微信红包<text style="font-weight:600;font-size:36px;color:#FFCD5E;">{{happySeasonPrize.money}}</text>元</text>
                        <text bindtap="viewRaffleRecords" class="raffle-result-red-box-middle-view" data-wpyviewrafflerecords-a="redBox">查看记录</text>
                    </view>
                    <view class="raffle-result-red-box-bottom">
                        <button bindtap="boxDonate" class="raffle-result-red-box-bottom-donate">我要捐赠</button>
                        <button bindtap="getRedBox" class="raffle-result-red-box-bottom-get">领取红包</button>
                    </view>
                </view>
                <view class="raffle-result-red-box-donate" wx:if="{{happySeasonPrize.type===2&&isBoxDonate}}">
                    <image class="raffle-result-red-box-donate-top" src="{{donateImg}}"></image>
                    <textarea bindinput="bindDonateBlessing" class="raffle-result-red-box-donate-top-input" maxlength="100" placeholder="例：献出一份爱，收获一份情"></textarea>
                    <view class="raffle-result-red-box-donate-middle">
                        <image class="raffle-result-red-box-donate-middle-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/red-warning.png"></image>
                        <text bindtap="goToDonateRule" class="raffle-result-red-box-donate-middle-name">您捐赠的金额将计入吾老安康基金，点击查看吾老安康基金介绍</text>
                    </view>
                    <view class="raffle-result-red-box-donate-bottom">
                        <button bindtap="cancelBoxDonate" class="raffle-result-red-box-donate-bottom-cancel">取消捐赠</button>
                        <button bindtap="confirmDonate" class="raffle-result-red-box-donate-bottom-confirm">确认捐赠</button>
                    </view>
                </view>
                <view class="raffle-result-points" wx:if="{{happySeasonPrize.type===3}}">
                    <image class="raffle-result-points-top" src="{{giftTopImg}}"></image>
                    <view class="raffle-result-points-middle">
                        <text class="raffle-result-points-middle-name">
                            <text class="raffle-result-points-middle-name-left">+{{happySeasonPrize.value}}</text>
                            <text class="raffle-result-points-middle-name-right">积分</text>
                        </text>
                        <view class="raffle-result-points-middle-tip" wx:if="{{happySeasonPrize.enable_upgrade}}">
                            <image class="raffle-result-points-middle-tip-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/red-warning.png"></image>
                            <text class="raffle-result-points-middle-tip-content">您有机会把积分升级为红包，是否升级？</text>
                        </view>
                    </view>
                    <view class="raffle-result-points-bottom">
                        <button bindtap="getPoints" class="raffle-result-points-bottom-get" wx:if="{{happySeasonPrize.enable_upgrade}}">不，我要积分</button>
                        <button bindtap="getPoints" class="raffle-result-points-bottom-get points-no-upgrade" wx:else>收下积分</button>
                        <button bindtap="updateToRedBox" class="raffle-result-points-bottom-update" wx:if="{{happySeasonPrize.enable_upgrade}}">积分升级红包</button>
                    </view>
                </view>
                <view class="raffle-result-points-confirm" wx:if="{{isPointSuccess}}">
                    <view class="raffle-result-points-confirm-top">
                        <view style="display:flex;justify-content:center;align-items:center">
                            <text class="raffle-result-points-confirm-score">{{IntegrationScore}}</text>
                            <view style="width:50px;display:flex;flex-direction:column;justify-content:center;">
                                <text class="raffle-result-points-confirm-icon">本次获得</text>
                                <text class="raffle-result-points-confirm-unit">积分</text>
                            </view>
                        </view>
                        <text class="raffle-result-points-confirm-tip">恭喜您本次获得</text>
                    </view>
                    <view class="raffle-result-points-confirm-bottom">
                        <button bindtap="gotoWithLogined" class="raffle-result-points-confirm-bottom-btn" data-url="/pages/customer/goToJph">去兑换</button>
                        <view>
                            <text bindtap="gotoWithLogined" class="raffle-result-points-confirm-bottom-text" data-to="pointsDetail" data-url="/pages/customer/goToJph">积分明细</text>
                        </view>
                    </view>
                    <image bindtap="closePointConfirm" class="raffle-result-points-confirm-close-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/rule-close.png"></image>
                </view>
            </view>
        </view>
        <view style="background:#338284">
            <view class="temporary-activity-box-mask" wx:if="{{temporaryActivity}}">
                <view class="temporary-activity-box">
                    <image bindtap="temporaryActivityGoTo" mode="widthFix" src="{{temporaryActivity.image}}"></image>
                    <view style="margin:0 auto;font-size:40rpx;color:white;text-align:center;">{{temporaryActivity.tips}}</view>
                    <view class="btn_box">
                        <view bindtap="temporaryActivityBoxClose" class="btn">关闭页面</view>
                        <view bindtap="temporaryActivityGoTo" class="btn">前往参与</view>
                    </view>
                </view>
            </view>
            <view class="detail-box product">
                <view class="{{true?'my-custom-navbar':''}} {{isSticky?'my-custom-navbar_sticky':''}}" style="height:{{navHeight}}px;">
                    <view bindtap="goBack" class="my-custom-navbar_back" style="top:{{navTop}}px;">
                        <image class="my-custom-navbar_back-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/back-icon.png" wx:if="{{isGoBack}}"></image>
                        <image class="my-custom-navbar_back-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/home-icon.png" wx:else></image>
                        <text>防伪查询</text>
                    </view>
                </view>
                <view style="width:100%;height:{{navHeight}}px;" wx:if="{{isSticky}}"></view>
                <view class="product-container">
                    <view class="product-img-container" wx:if="{{productInfo.productName}}">
                        <view class="product-img">
                            <image bindtap="goto" class="image" data-id="1" data-imageurl="{{productInfo.productImage}}" data-url="/pages/anticounterfeiting/detail?productErpNo={{productInfo.productErpNo}}&productDate={{productInfo.productionDate2}}" lazyLoad="lazy-load" src="{{productInfo.productImage}}" style="border-radius:8px 8px 0 0;"></image>
                        </view>
                        <view class="product-check" wx:if="{{productInfo.isFwInvisibleCode!=true}}">
                            <text wx:if="{{productInfo.codeType=='boxCode'}}">扫描盒码验证</text>
                            <text bindtap="goToValidate" wx:else>进一步验证</text>
                            <image class="product-check-img" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/arrow-white.png" wx:if="{{productInfo.codeType!=='boxCode'}}"></image>
                        </view>
                    </view>
                    <view class="product-info" wx:if="{{productInfo.productName}}">
                        <view class="info">
                            <text class="product-name">{{productInfo.productName}}</text>
                            <view class="product-create-date flex-box">
                                <text class="date">生产日期：{{productInfo.productionDate}}</text>
                                <view class="flag flag-jpzy" wx:if="{{productInfo.isJpzy}}">君品之约</view>
                            </view>
                            <text class="product-box-code" wx:if="{{!productInfo.productQrcodeLastSix}}">
                                <text class="product-box-code-title">酒盒防伪码：</text>
                                <text class="product-box-code-content">{{productInfo.productCode}}</text>
                            </text>
                            <text class="product-case-code" wx:if="{{productInfo.productQrcodeLastSix&&productInfo.productQrcodeLastSix.length!=0}}">
                                <text class="product-case-code-title">箱内酒盒防伪码后6位：</text>
                                <text class="product-qrcode-last-six" wx:for="{{productInfo.productQrcodeLastSix}}" wx:key="id">{{item}}</text>
                            </text>
                        </view>
                        <view class="tips" wx:if="{{productInfo.codeType=='boxCode'}}">
                            <image class="product-check-status" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/true.png" wx:if="{{queryResult=='真'||queryResult=='慎'}}"></image>
                            <image class="product-check-status" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/false.png" wx:if="{{queryResult=='疑'}}"></image>
                            <text wx:if="{{queryResult=='真'}}">该产品为真品，请放心饮用！</text>
                            <text wx:if="{{queryResult=='慎'}}">该产品已被查询{{productInfo.queryTimes}}次，最后查询时间为：{{productInfo.lastQueryDate}}。</text>
                            <text wx:if="{{queryResult=='疑'}}">该产品已超过正常查询次数，可能涉嫌假冒！</text>
                        </view>
                        <view class="tips" wx:else>
                            <image class="product-check-status" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/true.png" wx:if="{{queryResult=='真'||queryResult=='慎'}}"></image>
                            <image class="product-check-status" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/false.png" wx:if="{{queryResult=='疑'}}"></image>
                            <text wx:if="{{queryResult=='真'&&productInfo.isFwInvisibleCode!=true}}">该产品为真品，请放心饮用！</text>
                            <text bindtap="goToValidate" wx:if="{{queryResult=='真'&&productInfo.isFwInvisibleCode==true}}">该产品初步查验为真品，请点击<text style="color:yellow">进一步标签真伪鉴定</text>
                            </text>
                            <text wx:if="{{queryResult=='慎'}}">该产品已被查询{{productInfo.queryTimes}}次，最后查询时间为：{{productInfo.lastQueryDate}}。</text>
                            <text wx:if="{{queryResult=='疑'}}">该产品已超过正常查询次数，可能涉嫌假冒！</text>
                        </view>
                    </view>
                    <view class="product-error-info" wx:else>
                        <view class="error-tips">
                            <image class="error-tips-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/warning.png"></image>
                            <view class="error-tips-content">您输入的防伪码有误，请您核对后再次查询！</view>
                            <view class="error-tips-qrcode">二维码号：{{showqrCode}}</view>
                            <view class="error-tips-call">咨询电话：400-667-1988</view>
                            <view class="error-tips-custom">
                                <view class="error-tips-custom-left">
                                    <image class="error-tips-call-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/call.png"></image>
                                    <text class="error-tips-call-content">如有疑问，请咨询客服。</text>
                                </view>
                                <view bindtap="gokefu" class="error-tips-custom-right">
                                    <text class="error-tips-enter-content">客服咨询</text>
                                    <image class="error-tips-enter-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/arrow-white.png"></image>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view bindtap="gotoWithLogined" class="redpacket" data-id="4" data-url="/pages/web/redpacket?qrCode={{qrCode}}" wx:if="{{isRedpacket&&inActiveTime&&dataMsCenterUser}}">
                <image src="http://wap.exijiu.com/Public//Miniprogram/images/redpacket.gif"></image>
            </view>
            <view bindtap="goToMdascription" class="md-info" wx:if="{{isMd}}">
                <image src="http://wap.exijiu.com/Public//Miniprogram/images/shop-icon.png"></image>
            </view>
            <view bindtap="goto" class="md-info" data-url="/pages/web/redpacket?qrCode={{qrCode}}" wx:if="{{isQualityInspector}}">
                <image src="http://wap.exijiu.com/Public//Miniprogram/images/quality_inspect.jpg"></image>
            </view>
            <view class="detail-box gold-border nav-fixed-box">
                <view bindtap="gotoWithLogined" class="button anti-channel" data-url="/pages/customer/goToJph">
                    <image class="btn-img" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/points-mall-icon.png"></image>
                    <view class="btn-name">积分商城</view>
                </view>
                <view bindtap="goToYouZanShop" class="button">
                    <image class="btn-img" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/junpinhui-icon.png"></image>
                    <view class="btn-name">习酒官方商城</view>
                </view>
                <view bindtap="gokefu" class="button">
                    <image class="btn-img" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/customer-service-icon.png"></image>
                    <view class="btn-name">客服</view>
                </view>
                <view bindtap="gotoWithLogined" class="button" data-url="/pages/customer/goToUmall?pagePath=%2fpages%2findex%2fmy_v2">
                    <image class="btn-img" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/member-center-icon.png"></image>
                    <view class="btn-name">会员中心</view>
                </view>
            </view>
        </view>
        <view class="swiper-container">
            <view class="swiper" style="margin:20rpx;border-radius:8px;overflow:hidden;" wx:if="{{swiperInfo.length}}">
                <swiper autoplay indicatorActiveColor="#fff" indicatorDots="true" interval="3000" style="height:195rpx">
                    <swiper-item bindtap="gotoWithLogined" class="swiper-box" data-event="{{item.event_id}}" data-id="2" data-url="{{item.out_site_link}}" style="border-radius:10rpx;" wx:for="{{swiperInfo}}" wx:key="indexs">
                        <image mode="aspectFill" src="{{item.pic_url}}" style="border-radius:10rpx;"></image>
                    </swiper-item>
                </swiper>
            </view>
        </view>
        <view class="validate-image-wrapper" wx:if="{{productInfo.isHappySeason&&productInfo.codeType!='boxCode'}}">
            <view class="validate-image-wrapper-top">
                <image bindtap="goto" class="validate-image-wrapper-top-img" data-url="/pages/web/webView?url=https%3A%2F%2Fwap.exijiu.com%2Findex.php%2FXjhyjlb%2FE20220415%2Findex" mode="heightFix" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/scan-code-happy-season-banner.png"></image>
                <button bindtap="openRuleModal" class="validate-image-wrapper-top-btn">活动规则</button>
            </view>
            <view class="validate-image">
                <view class="validate-top">
                    <text class="validate-top-line1"></text>
                    <text class="validate-top-content" wx:if="{{hasBottleInnerQrcode}}">扫描瓶盖内二维码</text>
                    <text class="validate-top-content" wx:if="{{!hasBottleInnerQrcode}}">输入瓶盖内的4位数防伪码</text>
                    <text class="validate-top-line2"></text>
                </view>
                <view class="validate-middle-img">
                    <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_qrcode.png" wx:if="{{hasBottleInnerQrcode}}"></image>
                    <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_fwm.png" wx:if="{{!hasBottleInnerQrcode}}"></image>
                </view>
                <view class="validate-bottom">
                    <view class="btn-area" wx:if="{{hasBottleInnerQrcode}}">
                        <button bindtap="scan" class="{{true?'btn':''}}" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                            <text>扫描瓶盖内二维码</text>
                        </button>
                        <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                            <text>扫描瓶盖内二维码</text>
                        </button>
                    </view>
                    <view class="input-four-tips" wx:if="{{hasBottleInnerQrcode}}">
                        <text bindtap="inputFourTips">点击输入四位防伪码</text>
                    </view>
                    <form bindsubmit="getPointAndRaffle" wx:if="{{!hasBottleInnerQrcode}}">
                        <input bindinput="theLast4DigitsInput" class="validate-input" id="securitycode" maxlength="4" name="theLast4Digits" placeholder="请输入瓶盖内4位数字验证" value="{{validategetscore.theLast4Digits}}"></input>
                        <view class="btn-area">
                            <button class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" formType="submit" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                                <text>点击抽奖</text>
                            </button>
                            <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                                <text>点击抽奖</text>
                            </button>
                        </view>
                    </form>
                    <view class="validate-bottom-statistic">
                        <image class="validate-bottom-statistic-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/statistics.png"></image>
                        <text class="validate-bottom-statistic-data">本年已累计饮酒{{wineCount}}瓶</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="validate-image" wx:if="{{!productInfo.isHappySeason&&productInfo.isJifenProduct&&!inActiveTime&&productInfo.codeType!='boxCode'}}">
            <view class="validate-image-wrapper-top" wx:if="{{productInfo.activeInfo['name']}}">
                <image bindtap="goto" class="validate-image-wrapper-top-img" data-url="{{productInfo.activeInfo['url']}}" mode="heightFix" src="https://wap.exijiu.com/Public/Xjhyjlb/E20220415/ldimg/202501/{{productInfo.activeInfo['name']}}-name.png?v=1.0"></image>
                <button bindtap="openRuleModal" class="validate-image-wrapper-top-btn">活动规则</button>
            </view>
            <view class="validate-top">
                <text class="validate-top-line1"></text>
                <text class="validate-top-content" wx:if="{{hasBottleInnerQrcode}}">扫描瓶盖内二维码</text>
                <text class="validate-top-content" wx:if="{{!hasBottleInnerQrcode}}">输入瓶盖内的4位数防伪码</text>
                <text class="validate-top-line2"></text>
            </view>
            <view class="validate-middle-img">
                <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_qrcode.png" wx:if="{{hasBottleInnerQrcode}}"></image>
                <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_fwm.png" wx:if="{{!hasBottleInnerQrcode}}"></image>
            </view>
            <view class="validate-bottom">
                <view class="btn-area" wx:if="{{hasBottleInnerQrcode}}">
                    <button bindtap="scan" class="{{true?'btn':''}}" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                        <text>扫描瓶盖内二维码</text>
                    </button>
                    <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                        <text>扫描瓶盖内二维码</text>
                    </button>
                </view>
                <view class="input-four-tips" wx:if="{{hasBottleInnerQrcode}}">
                    <text bindtap="inputFourTips">点击输入四位防伪码</text>
                </view>
                <form bindsubmit="getIntegration" wx:if="{{!hasBottleInnerQrcode}}">
                    <input bindinput="theLast4DigitsInput" class="validate-input" id="securitycode" maxlength="4" name="theLast4Digits" placeholder="请输入瓶盖内4位数字验证" value="{{validategetscore.theLast4Digits}}"></input>
                    <view class="btn-area">
                        <button class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" formType="submit" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                            <text>获得积分</text>
                        </button>
                        <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                            <text>获得积分</text>
                        </button>
                    </view>
                </form>
                <view class="validate-bottom-statistic">
                    <image class="validate-bottom-statistic-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/statistics.png"></image>
                    <text class="validate-bottom-statistic-data">本年已累计饮酒{{wineCount}}瓶</text>
                </view>
            </view>
        </view>
        <view class="validate-image" wx:if="{{productInfo.isLegouActive&&!inActiveTime&&productInfo.codeType!='boxCode'}}">
            <view class="validate-top">
                <view class="validate-top-line1"></view>
                <text class="validate-top-content" wx:if="{{hasBottleInnerQrcode}}">扫描瓶盖内二维码</text>
                <text class="validate-top-content" wx:if="{{!hasBottleInnerQrcode}}">输入瓶盖内的4位数防伪码</text>
                <view class="validate-top-line2"></view>
            </view>
            <view class="validate-middle-img">
                <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_qrcode.png" wx:if="{{hasBottleInnerQrcode}}"></image>
                <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_fwm.png" wx:if="{{!hasBottleInnerQrcode}}"></image>
            </view>
            <view class="validate-bottom">
                <view class="btn-area" wx:if="{{hasBottleInnerQrcode}}">
                    <button bindtap="scan" class="{{true?'btn':''}}" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                        <text>扫描瓶盖内二维码</text>
                    </button>
                    <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                        <text>扫描瓶盖内二维码</text>
                    </button>
                </view>
                <view class="input-four-tips" wx:if="{{hasBottleInnerQrcode}}">
                    <text bindtap="inputFourTips">点击输入四位防伪码</text>
                </view>
                <form bindsubmit="getOnlineLegouChange" wx:if="{{!hasBottleInnerQrcode}}">
                    <input bindinput="theLast4DigitsInput" class="validate-input" id="securitycode" maxlength="4" name="theLast4Digits" placeholder="请输入瓶盖内4位数字验证" value="{{validategetscore.theLast4Digits}}"></input>
                    <view class="btn-area">
                        <button class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" formType="submit" plain="true" size="mini" wx:if="{{dataMsCenterUser}}">
                            <text>获得抽礼机会</text>
                        </button>
                        <button bindgetphonenumber="getPhoneNumber" class="{{true?'btn':''}} {{isStopClickRaffle?'btn-stop':''}}" disabled="{{isStopClickRaffle}}" openType="getPhoneNumber" plain="true" size="mini" wx:else>
                            <text>获得抽礼机会</text>
                        </button>
                    </view>
                </form>
                <view class="validate-bottom-statistic">
                    <image class="validate-bottom-statistic-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/statistics.png"></image>
                    <text class="validate-bottom-statistic-data">本年已累计饮酒{{wineCount}}瓶</text>
                </view>
            </view>
        </view>
        <view class="validate-image" wx:if="{{isRedpacket&&inActiveTime}}">
            <view class="validate-redcket-topap"></view>
            <view class="validate-middle-img">
                <image src="http://wap.exijiu.cn/Public/Qrcode/images/xiaochengxuimages/icons/bottle_fwm.png"></image>
            </view>
            <view class="validate-bottom">
                <view class="btn-area" wx:if="{{dataMsCenterUser}}">
                    <button bindtap="gotoWithLogined" class="btn" data-id="3" data-url="/pages/web/redpacket?qrCode={{qrCode}}" plain="true" size="mini">
                        <view wx:if="{{productInfo.productionDate>'2019-07-19'}}">点击领取红包并获得积分</view>
                        <view wx:else>点击领取红包</view>
                    </button>
                </view>
                <view class="btn-area" wx:else>
                    <button bindgetphonenumber="getPhoneNumber" class="btn" openType="getPhoneNumber" plain="true" size="mini">
                        <view wx:if="{{productInfo.productionDate>'2019-07-19'}}">点击领取红包并获得积分</view>
                        <view wx:else>点击领取红包</view>
                    </button>
                </view>
            </view>
        </view>
        <view>
            <view style="width:100%;" wx:if="{{jphJiuqian.length>0}}">
                <image mode="widthFix" src="http://wap.exijiu.com/Public/MemberClubV2/images/junpinhui/jiuqian.png" style="width:100%;"></image>
            </view>
            <view class="jph-product-list" wx:if="{{jphJiuqian.length>0}}">
                <view bindtap="gotoWithLogined" class="jph-product" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{jphJiuqian}}" wx:key="indexs">
                    <image mode="widthFix" src="{{item.pic_url}}" style="width:100%;"></image>
                </view>
            </view>
            <view style="clear:both;"></view>
            <view style="width:100%;" wx:if="{{jphJiuzhong.length>0}}">
                <image mode="widthFix" src="http://wap.exijiu.com/Public/MemberClubV2/images/junpinhui/jiuzhong.png" style="width:100%;"></image>
            </view>
            <view class="jph-product-list" wx:if="{{jphJiuzhong.length>0}}">
                <view bindtap="gotoWithLogined" class="jph-product" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{jphJiuzhong}}" wx:key="indexs">
                    <image mode="widthFix" src="{{item.pic_url}}" style="width:100%;"></image>
                </view>
            </view>
            <view style="clear:both;"></view>
            <view style="width:100%;" wx:if="{{jphJiuhou.length>0}}">
                <image mode="widthFix" src="http://wap.exijiu.com/Public/MemberClubV2/images/junpinhui/jiuhou.png" style="width:100%;"></image>
            </view>
            <view class="jph-product-list" wx:if="{{jphJiuhou.length>0}}">
                <view bindtap="gotoWithLogined" class="jph-product" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{jphJiuhou}}" wx:key="indexs">
                    <image mode="widthFix" src="{{item.pic_url}}" style="width:100%;"></image>
                </view>
            </view>
            <view style="clear:both;"></view>
        </view>
        <view class="jf-product-list-container">
            <view class="jf-product-list-line"></view>
            <view class="jf-product-list-title">
                <text class="jf-product-list-title_left">积分兑换</text>
                <view class="jf-product-list-title_right">
                    <div class="badge badge-anticounterfeiting-index">
                        <text bindtap="goto" data-url="/pages/web/webView?url=https%3A%2F%2Fmallwm.exijiu.com%2F%23%2Fpages%2Frich%2Fscore&nologin=1">积分规则</text>
                    </div>
                    <image class="jf-product-list-title_right-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/arrow-black.png"></image>
                </view>
            </view>
            <view bindtap="gotoWithLogined" class="jf-product-list-points" data-to="pointsDetail" data-url="/pages/customer/goToJph">
                <view class="jf-product-list-points-icon"></view>
                <text>积分值 ：</text>
                <text>{{integration}}</text>
                <image class="jf-product-list-points_enter-icon" src="https://wap.exijiu.com/Public/MemberClubV2/images/HappySeason/images/arrow-black.png"></image>
            </view>
            <view class="detail-box jf-product-list hide">
                <view bindtap="gotoWithLogined" class="jf-product" data-id="{{prod.id}}" data-url="/pages/customer/goToUmall?pagePath=%2fpages%2findex%2fgoods_detail%3fid%3d{{prod.id}}" wx:for="{{jifenShopProds}}" wx:for-item="prod" wx:key="index">
                    <image src="{{prod.thumbnail}}"></image>
                    <view class="name">
                        <text>{{prod.name}}</text>
                    </view>
                    <view class="score">
                        <text>{{prod.integral}}积分</text>
                    </view>
                </view>
            </view>
            <view bindtap="gotoWithLogined" class="get-more hide" data-url="/pages/customer/goToUmall?pagePath=%2fpages%2fclassification%2fclassification" style="width:92%;margin:15px;height:30px;display:inline-block;padding:15rpx 0;background-color:#f3f3f3;border:1px solid #d9caad;border-radius:10px;text-align:center;line-height:30px;color:#016f5d;">查看更多</view>
        </view>
        <view class="detail-box ad-banner"></view>
        <view class="weui-footer">
            <view class="weui-footer__links">
                <view class="weui-footer__text">版权所有 ©贵州习酒股份有限公司</view>
                <view class="weui-footer__text">服务热线：400-667-1988</view>
            </view>
        </view>
    </view>
    <view wx:if="{{visible}}">
        <view class="modal-container">
            <view style="width:480rpx;height:550rpx;">
                <image src="https://wap.exijiu.cn/Public/MemberClub/images/img9.png" style="width:100%;height:100%;"></image>
            </view>
            <view bindtap="handleCancel" class="buttonClose" type="primary"></view>
        </view>
        <view class="visible_mask"></view>
    </view>
    <view hidden="{{alertBoxBannersIsHidden}}">
        <view style="width:100%;height:100%;">
            <view class="pop-alert-box dialog">
                <view class="alert-content-box" style="height:70%;width:100%;">
                    <swiper autoplay interval="3000" style="height:100%;width:100%;">
                        <swiper-item bindtap="gotoWithLogined" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{alertBoxBanners}}" wx:key="id">
                            <image mode="aspectFit" src="{{item.pic_url}}" style="height:100%;width:100%;"></image>
                        </swiper-item>
                    </swiper>
                </view>
                <image catchtap="closeAlertBoxBanners" class="iconfont icon-close" src="https://wap.exijiu.cn/Public/MemberClub/images/closed.png"></image>
            </view>
        </view>
        <view catchtap="closeAlertBoxBanners" class="alert_mask"></view>
    </view>
    <view>
        <canvas canvasId="tdcanvas" style="visibility:hidden;position:fixed;z-index:-999;left:9999px"></canvas>
    </view>
</scroll-view>
