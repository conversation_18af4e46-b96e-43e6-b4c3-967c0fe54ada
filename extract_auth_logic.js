/**
 * 提取Authorization生成逻辑
 * 基于从源码中发现的关键信息
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AuthLogicExtractor {
    constructor() {
        // 从搜索结果中发现的关键信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码中发现的关键API路径
        this.keyAPIs = {
            // 从搜索结果中发现的关键API
            getJwt: '/Member/getJwt',  // 这个很关键！
            getJifenShopJwt: '/Member/getJifenShopJwt',
            getAuthData: 'getAuthData', // 这是一个方法名
            jifenCrmCreateJwt: '/api/v2/jifenCrm/createJwt'
        };
        
        // API域名
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apimallwm.exijiu.com/api',
            'https://apiforum.exijiu.com/api'
        ];
        
        console.log('🔧 Authorization逻辑提取器初始化完成');
        console.log('🎯 基于源码搜索发现的关键API');
    }

    /**
     * 分析从源码中发现的关键逻辑
     */
    analyzeDiscoveredLogic() {
        console.log('\n🔍 分析从源码中发现的关键逻辑...');
        
        console.log('\n📋 关键发现:');
        console.log('1. getAuthData方法的逻辑:');
        console.log('   - 从存储中获取authData');
        console.log('   - 检查expire_time是否过期');
        console.log('   - 如果过期，调用getJifenShopJwt()');
        console.log('   - 设置authorized_token = jwt');
        console.log('   - 保存到globalData.auth.Authorization');
        
        console.log('\n2. 关键代码片段:');
        console.log('   r={expire_time:n.expire_time,authorized_token:n.jwt}');
        console.log('   s.default.$instance.globalData.auth.Authorization=r.authorized_token');
        
        console.log('\n3. 关键API路径:');
        Object.entries(this.keyAPIs).forEach(([name, path]) => {
            console.log(`   ${name}: ${path}`);
        });
        
        console.log('\n💡 推测的Authorization生成流程:');
        console.log('1. 调用 /Member/getJwt 或 /Member/getJifenShopJwt');
        console.log('2. 从响应中获取 jwt 字段');
        console.log('3. 将 jwt 作为 authorized_token');
        console.log('4. 设置为 Authorization 头部');
    }

    /**
     * 尝试调用关键的JWT获取API
     */
    async tryJwtAPIs() {
        console.log('\n🔍 尝试调用关键的JWT获取API...');
        
        const jwtAPIs = [
            { name: 'getJwt', path: '/Member/getJwt' },
            { name: 'getJifenShopJwt', path: '/Member/getJifenShopJwt' },
            { name: 'jifenCrmCreateJwt', path: '/api/v2/jifenCrm/createJwt' },
            
            // 尝试其他可能的JWT API
            { name: 'auth/jwt', path: '/auth/jwt' },
            { name: 'garden/jwt', path: '/garden/jwt' },
            { name: 'member/jwt', path: '/member/jwt' },
            { name: 'jwt/create', path: '/jwt/create' },
            { name: 'token/jwt', path: '/token/jwt' }
        ];
        
        for (const api of jwtAPIs) {
            console.log(`\n🧪 测试API: ${api.name} (${api.path})`);
            
            try {
                const result = await this.callJwtAPI(api.path);
                
                if (result.success) {
                    console.log('✅ API调用成功!');
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    
                    // 检查响应中的JWT相关字段
                    const jwt = this.extractJwtFromResponse(result.data);
                    if (jwt) {
                        console.log('🎉 找到JWT!');
                        console.log('🔑 JWT:', jwt);
                        
                        // 验证这个JWT是否可以作为Authorization使用
                        const isValid = await this.validateJwtAsAuthorization(jwt);
                        if (isValid) {
                            console.log('🎊 JWT验证成功，可以作为Authorization使用!');
                            return { api: api.name, jwt: jwt };
                        } else {
                            console.log('⚠️ JWT验证失败，不能作为Authorization使用');
                        }
                    }
                } else {
                    console.log('❌ API调用失败');
                    if (result.data && typeof result.data === 'object') {
                        console.log('📄 错误信息:', result.data.msg || result.data.message || '未知错误');
                    }
                }
                
            } catch (error) {
                console.log(`❌ ${api.name} 请求失败: ${error.message}`);
            }
        }
        
        console.log('\n❌ 未能通过API获取有效的JWT');
        return null;
    }

    /**
     * 调用JWT API
     */
    async callJwtAPI(path) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'login_code': this.loginCode
        };
        
        // 尝试所有API域名
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.makeRequest(baseUrl, path, 'GET', null, headers);
                if (result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        return { success: false, data: null, status: 0 };
    }

    /**
     * 从响应中提取JWT
     */
    extractJwtFromResponse(data) {
        const jwtFields = [
            'jwt',
            'token',
            'auth',
            'authorized_token',
            'access_token',
            'authToken',
            'jwtToken'
        ];
        
        for (const field of jwtFields) {
            if (data[field]) {
                return data[field];
            }
        }
        
        // 递归检查嵌套对象
        for (const key in data) {
            if (typeof data[key] === 'object' && data[key] !== null) {
                const nested = this.extractJwtFromResponse(data[key]);
                if (nested) return nested;
            }
        }
        
        return null;
    }

    /**
     * 验证JWT是否可以作为Authorization使用
     */
    async validateJwtAsAuthorization(jwt) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': jwt,
                'login_code': this.loginCode
            };
            
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API',
                '/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                headers
            );
            
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 尝试模拟getAuthData方法的逻辑
     */
    async simulateGetAuthDataLogic() {
        console.log('\n🔍 模拟getAuthData方法的逻辑...');
        
        try {
            // 步骤1: 模拟检查存储中的authData
            console.log('📋 步骤1: 检查存储中的authData (模拟为过期)');
            
            // 步骤2: 调用getJifenShopJwt
            console.log('📋 步骤2: 调用getJifenShopJwt...');
            const jwtResult = await this.callJwtAPI('/Member/getJifenShopJwt');
            
            if (jwtResult.success) {
                console.log('✅ getJifenShopJwt调用成功');
                console.log('📊 响应数据:', JSON.stringify(jwtResult.data, null, 2));
                
                // 步骤3: 提取JWT
                const jwt = this.extractJwtFromResponse(jwtResult.data);
                if (jwt) {
                    console.log('🔑 提取到JWT:', jwt);
                    
                    // 步骤4: 模拟设置Authorization
                    console.log('📋 步骤4: 设置为Authorization...');
                    
                    // 步骤5: 验证Authorization
                    const isValid = await this.validateJwtAsAuthorization(jwt);
                    if (isValid) {
                        console.log('🎉 模拟getAuthData逻辑成功!');
                        console.log('🔑 生成的Authorization:', jwt);
                        return jwt;
                    } else {
                        console.log('❌ 生成的Authorization验证失败');
                    }
                } else {
                    console.log('❌ 未能从响应中提取JWT');
                }
            } else {
                console.log('❌ getJifenShopJwt调用失败');
            }
            
        } catch (error) {
            console.log('❌ 模拟getAuthData逻辑失败:', error.message);
        }
        
        return null;
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的Authorization逻辑提取
     */
    async runCompleteExtraction() {
        console.log('🔍 开始完整的Authorization逻辑提取...');
        console.log('🎯 基于源码搜索发现的关键信息');
        
        try {
            // 1. 分析发现的逻辑
            console.log('\n' + '='.repeat(60));
            console.log('📊 第一部分: 分析发现的逻辑');
            console.log('='.repeat(60));
            this.analyzeDiscoveredLogic();
            
            // 2. 尝试调用JWT API
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第二部分: 尝试调用JWT API');
            console.log('='.repeat(60));
            const jwtResult = await this.tryJwtAPIs();
            
            // 3. 模拟getAuthData逻辑
            console.log('\n' + '='.repeat(60));
            console.log('🔄 第三部分: 模拟getAuthData逻辑');
            console.log('='.repeat(60));
            const authResult = await this.simulateGetAuthDataLogic();
            
            // 4. 输出结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 提取结果总结');
            console.log('='.repeat(60));
            
            if (jwtResult || authResult) {
                console.log('🎉 成功提取Authorization生成逻辑!');
                
                if (jwtResult) {
                    console.log('🔑 通过API获取:', jwtResult.api);
                    console.log('🎯 JWT:', jwtResult.jwt);
                }
                
                if (authResult) {
                    console.log('🔑 通过模拟逻辑获取:', authResult);
                }
                
                console.log('\n💡 Authorization生成方法:');
                console.log('1. 调用 /Member/getJifenShopJwt API');
                console.log('2. 从响应的 jwt 字段获取token');
                console.log('3. 将该token作为Authorization头部使用');
                
                return jwtResult?.jwt || authResult;
                
            } else {
                console.log('❌ 未能提取Authorization生成逻辑');
                console.log('\n💡 可能的原因:');
                console.log('1. API需要特殊的认证参数');
                console.log('2. login_code可能已过期');
                console.log('3. 需要特定的请求头或环境');
                console.log('4. API路径可能有变化');
            }
            
            return null;
            
        } catch (error) {
            console.log('\n❌ 提取过程失败:', error.message);
            return null;
        }
    }
}

// 导出类
module.exports = AuthLogicExtractor;

// 如果直接运行此文件
if (require.main === module) {
    const extractor = new AuthLogicExtractor();
    
    console.log('🔍 Authorization逻辑提取器');
    console.log('🎯 基于源码搜索发现的关键信息');
    console.log('📋 目标: 找到Authorization的生成方法');
    console.log('');
    
    // 运行完整提取
    extractor.runCompleteExtraction().then(result => {
        if (result) {
            console.log('\n🎊 Authorization生成逻辑提取成功!');
            console.log('🔑 可以使用这个方法生成新的Authorization');
        } else {
            console.log('\n😔 Authorization生成逻辑提取失败');
            console.log('💡 建议继续分析源码或使用现有Authorization');
        }
    }).catch(error => {
        console.error('💥 提取异常:', error);
    });
}
