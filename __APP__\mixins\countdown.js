Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../npm/wepy/lib/wepy.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(e){function r(){var e,t,n;o(this,r);for(var a=arguments.length,l=Array(a),u=0;u<a;u++)l[u]=arguments[u];return t=n=i(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(l))),n.data={content:{},timer:null},i(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default.mixin),t(r,[{key:"onLoad",value:function(){this.clear()}},{key:"clear",value:function(){this.clearTime()}},{key:"countdowm",value:function(e,t){var r=this;this.clear(),this.timer=setInterval((function(){var n="groupTime"===t?new Date(e)-new Date+864e5:new Date(e)-new Date;if(n>0){var o="groupTime"===t?"":Math.floor(n/864e5),i="groupTime"===t?Math.floor(n/36e5):Math.floor(n/36e5%24),a=Math.floor(n/6e4%60),l=Math.floor(n/1e3%60);o=o<0?"0":o,i=i<10?"0"+i:i,a=a<10?"0"+a:a,l=l<10?"0"+l:l,r.content={day:o,hour:i,min:a,sec:l}}else r.clear();r.$apply()}),1e3),console.info("[interval] create interval id="+this.timer)}},{key:"clearTime",value:function(){null!=this.timer&&(console.info("[interval] destory interval id="+this.timer),clearInterval(this.timer),this.timer=null),this.content={day:"00",hour:"00",min:"00",sec:"00"}}}]),r}();exports.default=a;