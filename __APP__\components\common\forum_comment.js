Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),t=l(require("./../../npm/wepy/lib/wepy.js")),n=l(require("./../../api/forum_comment.js")),o=l(require("./../../api/identify.js")),a=l(require("./../../mixins/pagination.js")),i=l(require("./placeholder.js")),r=l(require("./../weui/loadmore.js")),s=l(require("./../../utils/Tips.js")),c=l(require("./../../api/appraisal.js")),u=l(require("./../../api/member.js"));function l(e){return e&&e.__esModule?e:{default:e}}function m(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function o(a,i){try{var r=t[a](i),s=r.value}catch(e){return void n(e)}if(!r.done)return Promise.resolve(s).then((function(e){o("next",e)}),(function(e){o("throw",e)}));e(s)}("next")}))}}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(l){function d(){var e,t,n;f(this,d);for(var l=arguments.length,h=Array(l),x=0;x<l;x++)h[x]=arguments[x];return t=n=p(this,(e=d.__proto__||Object.getPrototypeOf(d)).call.apply(e,[this].concat(h))),n.events={"on-reach-bottom-event":function(){console.log("onReon-reach-bottom-eventachBottom"),n.onReachBottom()}},n.props={postDetailList:{twoWay:!0},topicId:{type:String,default:"null"},commentData:{}},n.data={showCommentModalStatus:!1,animationData:{},css:{bankuaiSelected:""},ppp:0,hot:0,scrollLeft:0,currentTab:0,navScrollLeft:0,userInfo:{},create_time:"",postDetailList:{},comment:{},commentList:[],page:{list:[]},focusInput:!1,height:0,buttonClicked:!1,isofficial:{},isComment:!0},n.methods={inputFocus:function(e){var t=wx.createSelectorQuery();t.select("#commentbox").boundingClientRect(),t.exec((function(e){console.log("commentbox",e)})),console.log(e,"键盘弹起",e.detail.height),s.default.alert("键盘弹起"+e.detail.height),this.height=e.detail.height,this.$apply()},inputBlur:function(){this.height=0,this.inputFocus=!1,this.$apply()},gotoViewAllReplies:function(e){var t="/pagesforum/pages/view_all_replies?commentId="+e;wx.navigateTo({url:t})},goTomemberpost:function(e){var t="/pagesforum/pages/member_post?userId="+e;wx.navigateTo({url:t})},commentIsLike:function(e){var t=this;return m(regeneratorRuntime.mark((function n(){var a,i,r;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(console.log(e),a=e.currentTarget.dataset.id,i=e.currentTarget.dataset.index,console.log(i),r=t.page.list[i],console.log(r),null!=r.isLike&&0!=r.isLike){n.next=11;break}return n.next=9,o.default.dianZanComment(a).then((function(e){wx.showToast({title:"点赞成功",icon:"none",duration:2e3}),t.page.list[i].isLike+=1,t.page.list[i].likeCount+=1,t.$apply()}));case 9:n.next=14;break;case 11:if(1!=r.isLike){n.next=14;break}return n.next=14,o.default.cancelDianZanComment(a).then((function(e){wx.showToast({title:"取消点赞成功",icon:"none",duration:2e3}),t.page.list[i].isLike=0,t.page.list[i].likeCount-=1,t.$apply()}));case 14:case"end":return n.stop()}}),n,t)})))()},isLike:function(e){var t=this;return m(regeneratorRuntime.mark((function n(){var a,i;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(console.log(e),a=e.currentTarget.id,i=t.postDetailList.data,console.log(i.isLike),null!=i.isLike&&0!=i.isLike){n.next=9;break}return n.next=7,o.default.dianZanPost(a).then((function(e){wx.showToast({title:"点赞成功",icon:"none",duration:2e3}),t.postDetailList.data.isLike+=1,t.postDetailList.data.likeCount+=1,t.$apply()}));case 7:n.next=12;break;case 9:if(1!=i.isLike){n.next=12;break}return n.next=12,o.default.cancelDianZanPost(a).then((function(e){wx.showToast({title:"取消点赞成功",icon:"none",duration:2e3}),t.postDetailList.data.isLike=0,t.postDetailList.data.likeCount-=1,t.$apply()}));case 12:case"end":return n.stop()}}),n,t)})))()},isFollow:function(e){var t=this;return m(regeneratorRuntime.mark((function n(){var a,i;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(a=e.currentTarget.id,null!=(i=t.postDetailList.data).isFollow&&0!=i.isFollow){n.next=7;break}return n.next=5,o.default.followPost(a).then((function(e){wx.showToast({title:"已关注",icon:"none",duration:2e3}),t.postDetailList.data.isFollow+=1,t.postDetailList.data.followCount+=1,t.$apply()}));case 5:n.next=10;break;case 7:if(1!=i.isFollow){n.next=10;break}return n.next=10,o.default.cancelFollowPost(a).then((function(e){wx.showToast({title:"已取消关注",icon:"none",duration:2e3}),t.postDetailList.data.isFollow=0,t.postDetailList.data.followCount-=1,t.$apply()}));case 10:case"end":return n.stop()}}),n,t)})))()},showCommentModal:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.showCommentModalStatus=!0,e&&(this.comment.topicId=e),t&&(this.comment.pcommentId=t),n&&(this.comment.commenterNickname=n),this.focusInput=!0,this.$apply()},hideCommentModal:function(){this._hideCommentModal()},deleteComment:function(e){var t=this;return m(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:s.default.modal("确认删除该条评论").then(function(){var n=m(regeneratorRuntime.mark((function n(a){var i=a.cancel;a.confirm;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!i){n.next=2;break}return n.abrupt("return");case 2:return n.next=4,o.default.deleteMyComment(e).then((function(e){wx.showToast({title:"已删除",icon:"none",duration:2e3}),t.initComent(t.topicId),t.postDetailList.data.commentCount-=1,t.$apply()}));case 4:case"end":return n.stop()}}),n,t)})));return function(e){return n.apply(this,arguments)}}());case 1:case"end":return n.stop()}}),n,t)})))()},commentConfirm:function(e){var t=this,n=e.detail;return m(regeneratorRuntime.mark((function e(){var a,i,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isComment=!1,console.log("this.comment",t.comment),t.comment.content=n.value.content,e.next=5,c.default.validateContent(t.comment.content);case 5:return a=e.sent,e.next=8,u.default.officialAccount();case 8:if(i=e.sent,0!=t.comment.content&&t.comment.content){e.next=15;break}s.default.confirm("评论的内容不能为空！"),t.isComment=!0,t.$apply(),e.next=69;break;case 15:if(!/^(?=[@#$￥%^&*]+$)/.test(t.comment.content)){e.next=21;break}s.default.modal("评论的内容不能含有非法字符！"),t.isComment=!0,t.$apply(),e.next=69;break;case 21:if(!a.data||!a.data.code||600!=a.data.code){e.next=27;break}s.default.toasts("您当前输入的谈论内容中涉及敏感信息，请去除后重新发布！"),t.isComment=!0,t.$apply(),e.next=69;break;case 27:if(!(t.comment.content.indexOf("微信群")>=0||t.comment.content.indexOf("qq")>=0||t.comment.content.indexOf("QQ")>=0||t.comment.content.indexOf("qq群")>=0||t.comment.content.indexOf("QQ群")>=0||t.comment.content.indexOf("手机")>=0||t.comment.content.indexOf("wx")>=0||t.comment.content.indexOf("weixin")>=0||t.comment.content.indexOf("扣扣")>=0||t.comment.content.indexOf("微信")>=0||t.comment.content.indexOf("号码")>=0||t.comment.content.indexOf("weixinhao")>=0||t.comment.content.indexOf("加我")>=0||t.comment.content.indexOf("私聊")>=0||t.comment.content.indexOf("电话")>=0||t.comment.content.indexOf("群")>=0)){e.next=34;break}return s.default.toasts("您当前输入的谈论内容中涉及通讯信息，请去除后重新发布！"),t.isComment=!0,t.$apply(),e.abrupt("return",!1);case 34:if(!(t.comment.content.indexOf("习将军")>=0||t.comment.content.indexOf("喜将军")>=0||t.comment.content.indexOf("习圣")>=0||t.comment.content.indexOf("习礼")>=0||t.comment.content.indexOf("习虎")>=0||t.comment.content.indexOf("习府")>=0||t.comment.content.indexOf("习宴")>=0)){e.next=41;break}return s.default.toasts("您当前输入的谈论内容中涉及敏感词汇，请去除后重新发布！"),t.isComment=!0,t.$apply(),e.abrupt("return",!1);case 41:if(0!=i.is_subscribe){e.next=48;break}return e.next=44,s.default.confirm("请微信搜索公众号“习酒会员俱乐部”关注公众号后再来评论。");case 44:t.isComment=!0,t.$apply(),e.next=69;break;case 48:return s.default.loading(),e.prev=49,e.next=52,o.default.addComment(t.comment);case 52:r=e.sent,t.page.list.unshift(r.data),t._hideCommentModal(),e.next=63;break;case 57:return e.prev=57,e.t0=e.catch(49),console.log(e.t0),e.next=62,s.default.error("评论失败");case 62:return e.abrupt("return",!1);case 63:return t.isComment=!0,e.next=66,s.default.success("已评论",1e3);case 66:return t.postDetailList.data.commentCount+=1,e.next=69,o.default.postDetailList(t.topicId);case 69:case"end":return e.stop()}}),e,t,[[49,57]])})))()}},n.$repeat={},n.$props={Placeholder:{"v-bind:show.sync":"isPageEmpty",message:"暂无评论",top:"0rpx",fixed:"false"},Loadmore:{"xmlns:v-bind":"","v-bind:page.sync":"page"}},n.$events={},n.components={Placeholder:i.default,Loadmore:r.default},n.mixins=[a.default],p(n,t)}var h,x,g;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,t.default.component),e(d,[{key:"onReachBottom",value:(g=m(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next();case 2:this.$apply(),console.log("ForumComment onReachBottom");case 4:case"end":return e.stop()}}),e,this)}))),function(){return g.apply(this,arguments)})},{key:"onLoad",value:(x=m(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.default.getJifenShopMemberInfo();case 2:return this.userInfo=e.sent,e.next=5,o.default.isOfficial();case 5:this.isofficial=e.sent,console.log("lvshaojiang",this.isofficial.data.isOfficial);case 7:case"end":return e.stop()}}),e,this)}))),function(){return x.apply(this,arguments)})},{key:"initComent",value:(h=m(regeneratorRuntime.mark((function e(t){var a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,a){e.next=5;break}return e.next=4,o.default.postDetailList(this.topicId);case 4:this.postDetailList=e.sent;case 5:return e.next=7,n.default.comments(t,a);case 7:return this.page=e.sent,console.log("评论列表",this.page),e.next=11,this.next();case 11:e.next=15;break;case 13:e.prev=13,e.t0=e.catch(0);case 15:case"end":return e.stop()}}),e,this,[[0,13]])}))),function(e){return h.apply(this,arguments)})},{key:"_hideCommentModal",value:function(){this.showCommentModalStatus=!1,this.comment={},this.$apply()}},{key:"keyBoardChange",value:function(e){if(this.data.first)this.setData({first:!1});else{var t=e+"px";this.setData({keyBoardHeight:t}),"0px"===t&&(this.showCommentModalStatus=!1,this.$apply())}}},{key:"onShow",value:function(){var e=this;wx.onKeyboardHeightChange((function(t){e.keyBoardChange(t.height)}))}}]),d}();exports.default=d;