function t(t,e){return function(){return e(t.apply(void 0,arguments))}}exports.__esModule=!0,exports.default=function(e,o){if("function"==typeof e)return t(e,o);if("object"!=typeof e||null===e)throw new Error("bindActionCreators expected an object or a function, instead received "+(null===e?"null":typeof e)+'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?');for(var r=Object.keys(e),n={},i=0;i<r.length;i++){var f=r[i],u=e[f];"function"==typeof u&&(n[f]=t(u,o))}return n};