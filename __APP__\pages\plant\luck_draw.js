Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=u(require("./../../npm/wepy/lib/wepy.js")),n=u(require("./../../api/garden.js")),r=u(require("./../../utils/Tips.js")),a=u(require("./../../components/common/loading.js")),o=u(require("./../../mixins/base.js")),i=u(require("./../../components/plant/head/modal.js")),s=u(require("./../../api/member.js"));function u(e){return e&&e.__esModule?e:{default:e}}function c(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(a,o){try{var i=t[a](o),s=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(s).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(s)}("next")}))}}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(u){function d(){var e,u,m;f(this,d);for(var h=arguments.length,y=Array(h),g=0;g<h;g++)y[g]=arguments[g];return u=m=p(this,(e=d.__proto__||Object.getPrototypeOf(d)).call.apply(e,[this].concat(y))),m.data=l({lottery_id:1,ballList:[1,1,2,2,3,3,4,4],start:!1,end:!1,random:"1",showModal:!1,move:"move_",prize:"",lotteryRecord:"",detail:"",token:"",init:!1,limit:10,offset:0,userInfo:"",tipMsg:"您好！",showMsg:!1,remainFreeDrawChance:0,BaseData:{},CopyRight:"访问www.duoguyu.com, 获取更多小程序.",dmData:[],symbolLeft:"{{",symbolRight:"}}",lotteryAmount:"",os:"",phone_model:"",brower:"",networkType:"",platform:""},"userInfo",!1),m.$repeat={},m.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},TipsModal:{"xmlns:v-on":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},m.$events={TipsModal:{"v-on:closeIt":"closeIt"}},m.components={Loading:a.default,TipsModal:i.default},m.mixins=[o.default],m.methods={start:function(){var e=this;return c(regeneratorRuntime.mark((function t(){var a,o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,s.default.buryingPoint(154,e.userInfo.id?e.userInfo.id:"",e.os,e.brower,e.phone_model,e.networkType);case 2:return t.next=4,n.default.beforeDraw({lottery_id:e.lottery_id});case 4:return a=t.sent,t.next=7,n.default.draw({lottery_id:e.lottery_id,uuid:a.uuid,must_complete_member_info:1});case 7:if(!(o=t.sent).UNcomplete){t.next=12;break}return t.next=11,r.default.confirm("完善个人信息后才可进行抽奖！").then((function(t){return e.$navigate("/pages/plant/Improve"),!1})).catch((function(e){return!1}));case 11:return t.abrupt("return",!1);case 12:return e.prize=o,e.start=!0,e.end=!1,e.random=parseInt(4*Math.random())+1,setTimeout((function(){e.start=!1,e.end=!0,e.$apply()}),2e3),setTimeout((function(){e.showModal=!0,e.$apply()}),3500),t.next=20,n.default.memberInfo();case 20:e.userInfo=t.sent,e.$apply();case 22:case"end":return t.stop()}}),t,e)})))()},miss:function(){this.showModal=!1},listShow:function(){var e=this;return c(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.offset=e.offset+e.limit,t.next=3,n.default.lotteryRecord({limit:e.limit,offset:e.offset});case 3:r=t.sent,e.lotteryRecord.length?e.lotteryRecord=e.lotteryRecord.concat(r):e.lotteryRecord=r,e.$apply();case 6:case"end":return t.stop()}}),t,e)})))()},throttle:function(){},showModal_task:function(e){var t=this;return c(regeneratorRuntime.mark((function r(){var a,o,i;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,s.default.buryingPoint(156,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 2:if(1!=t.prize.prize_type&&1!=e){r.next=16;break}return console.log(t.prize.prize_id),a=wx.createAnimation({duration:200,timingFunction:"linear",delay:0}),t.animation=a,a.translateY(600).step(),t.setData({animationData:a.export(),showModalStatus:!0}),setTimeout(function(){a.translateY(0).step(),this.setData({animationData:a.export()})}.bind(t),0),o={limit:10,offset:0},r.next=12,n.default.lotteryRecord(o);case 12:i=r.sent,t.lotteryRecord=i,console.log(i),t.$apply();case 16:case"end":return r.stop()}}),r,t)})))()},showModal_task2:function(e){var t=this;return c(regeneratorRuntime.mark((function e(){var r,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.default.buryingPoint(155,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 2:return r=wx.createAnimation({duration:200,timingFunction:"linear",delay:0}),t.animation=r,r.translateY(600).step(),t.setData({animationData:r.export(),showModalStatus2:!0}),setTimeout(function(){r.translateY(0).step(),this.setData({animationData:r.export()})}.bind(t),0),e.next=9,n.default.read({id:t.lottery_id});case 9:a=e.sent,console.log(a),t.detail=a,t.$apply();case 13:case"end":return e.stop()}}),e,t)})))()},hideModal:function(){var e=t.default.createAnimation({duration:200,timingFunction:"linear",delay:0});this.animation=e,e.translateY(600).step(),this.setData({animationData:e.export()}),setTimeout(function(){e.translateY(0).step(),this.setData({animationData:e.export(),showModalStatus:!1,showModalStatus2:!1})}.bind(this),0),this.offset=0,this.$apply()},gotoOrderDetail:function(e){var t=this,n=e.currentTarget.dataset.url;this.token?(console.log(n),this.$navigate(n)):(this.tipMsg="跳转至首页成为习酒会员即可兑换商品,即将自动跳转！",this.showMsg=!0,this.$apply(),setTimeout((function(){t.$navigate("/pages/customer/index")}),3e3))},sortSoil:function(){}},m.config={navigationBarBackgroundColor:"#fff",navigationBarTitleText:"幸运抽奖",navigationBarTextStyle:"black"},p(m,u)}var m,h,y;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,t.default.page),e(d,[{key:"onLoad",value:(y=c(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.userInfo=t.default.getStorageSync("userInfo"),e.next=3,n.default.lotteryAmount();case 3:return this.lotteryAmount=e.sent,e.next=6,n.default.lotteryRecordRoll();case 6:this.BaseData=e.sent,console.log(this.BaseData),this.getUserSystemInfo(),this.getNetwork(),this.setDM();case 11:case"end":return e.stop()}}),e,this)}))),function(){return y.apply(this,arguments)})},{key:"onShow",value:(h=c(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.default.getStorageSync("Authorization"),this.token=r,e.next=4,n.default.memberInfo();case 4:this.userInfo=e.sent,this.loaded();case 6:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"getUserSystemInfo",value:function(){var e=this;wx.getSystemInfo({success:function(t){console.log(t),e.os=t.system?t.system:"",e.phone_model=t.model?t.model:"",e.brower="iPhone"==t.model?"safari":"chrome",e.$apply()}})}},{key:"getNetwork",value:function(){var e=this;wx.getNetworkType({success:function(t){var n=t.networkType?t.networkType:"";e.networkType=n,e.$apply()}})}},{key:"setDM",value:function(){for(var e=[],t=this.BaseData,n=0;n<t.length;n++){var r={id:t[n].id,head_imgurl:t[n].member.head_imgurl,nick_name:t[n].member.nick_name,formatCreateDate:t[n].formatCreateDate,prize_name:t[n].prize_name};e.push(r)}this.dmData=e,this.$apply()}},{key:"updateRemainFreeDrawChance",value:(m=c(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.default.remainFreeDrawChance();case 2:t=e.sent,this.remainFreeDrawChance=t.remainFreeDrawChance,this.$apply();case 5:case"end":return e.stop()}}),e,this)}))),function(){return m.apply(this,arguments)})}]),d}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(d,"pages/plant/luck_draw"));