/**
 * Node.js源码搜索器
 * 使用Node.js文件系统API搜索关键词
 */

const fs = require('fs');
const path = require('path');

class NodeJSSourceSearcher {
    constructor() {
        this.appPath = '__APP__';
        
        // 扩展的关键词列表
        this.keywords = [
            // Token相关
            'refreshtoken', 'refresh_token', 'accesstoken', 'access_token',
            'authtoken', 'auth_token', 'bearertoken', 'bearer_token',
            'sessiontoken', 'session_token', 'apikey', 'api_key',
            
            // JWT相关
            'jwt', 'jwttoken', 'jwt_token', 'jsonwebtoken',
            'payload', 'signature', 'header',
            
            // 认证相关
            'authorization', 'authenticate', 'credential', 'identity',
            'login', 'logout', 'signin', 'signout', 'oauth',
            
            // 密钥相关
            'secret', 'key', 'privatekey', 'publickey', 'secretkey',
            'encryption', 'decrypt', 'encrypt', 'hash', 'salt',
            
            // 会话相关
            'session', 'cookie', 'storage', 'cache',
            'expire', 'expiry', 'ttl', 'timeout',
            
            // 微信相关
            'wechat', 'weixin', 'wx', 'openid', 'unionid',
            'appid', 'appsecret', 'code2session',
            
            // 其他安全相关
            'password', 'pin', 'otp', 'captcha', 'verify',
            
            // API相关
            'getJwt', 'getJifenShopJwt', 'createJwt', 'refreshJwt',
            'getAuthData', 'setAuthData', 'checkSession',
            
            // 特定方法
            'Member/getJwt', 'jifenCrm/createJwt', 'wechat/login',
            'authorized_token', 'third_session'
        ];
        
        this.results = {
            foundFiles: [],
            keywordMatches: {},
            totalMatches: 0
        };
        
        console.log('🔧 Node.js源码搜索器初始化完成');
        console.log(`🔍 将搜索 ${this.keywords.length} 个关键词`);
    }

    /**
     * 递归获取所有JS文件
     */
    getAllJSFiles(dir) {
        const files = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    files.push(...this.getAllJSFiles(fullPath));
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.log(`❌ 读取目录失败: ${dir} - ${error.message}`);
        }
        
        return files;
    }

    /**
     * 在文件中搜索关键词
     */
    searchInFile(filePath, keyword) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            const matches = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const lowerLine = line.toLowerCase();
                const lowerKeyword = keyword.toLowerCase();
                
                if (lowerLine.includes(lowerKeyword)) {
                    matches.push({
                        lineNumber: i + 1,
                        line: line.trim(),
                        context: this.getContext(lines, i, 2)
                    });
                }
            }
            
            return matches;
        } catch (error) {
            return [];
        }
    }

    /**
     * 获取上下文行
     */
    getContext(lines, lineIndex, contextSize) {
        const start = Math.max(0, lineIndex - contextSize);
        const end = Math.min(lines.length, lineIndex + contextSize + 1);
        
        const context = [];
        for (let i = start; i < end; i++) {
            context.push({
                lineNumber: i + 1,
                line: lines[i].trim(),
                isCurrent: i === lineIndex
            });
        }
        
        return context;
    }

    /**
     * 执行完整搜索
     */
    async performCompleteSearch() {
        console.log('\n🔍 开始完整源码搜索...');
        console.log(`📁 搜索路径: ${this.appPath}`);

        // 检查路径是否存在
        if (!fs.existsSync(this.appPath)) {
            console.log(`❌ 路径不存在: ${this.appPath}`);
            return this.results;
        }

        // 获取所有JS文件
        console.log('📁 获取所有JS文件...');
        const jsFiles = this.getAllJSFiles(this.appPath);
        console.log(`📊 找到 ${jsFiles.length} 个JS文件`);

        // 显示前几个文件
        if (jsFiles.length > 0) {
            console.log('📄 前几个文件:');
            jsFiles.slice(0, 5).forEach(file => {
                console.log(`  - ${file}`);
            });
        }
        
        if (jsFiles.length === 0) {
            console.log('❌ 没有找到JS文件');
            return this.results;
        }
        
        // 搜索每个关键词
        for (const keyword of this.keywords) {
            console.log(`\n🔍 搜索关键词: "${keyword}"`);
            
            const keywordResults = {
                keyword: keyword,
                totalMatches: 0,
                files: []
            };
            
            for (const filePath of jsFiles) {
                const matches = this.searchInFile(filePath, keyword);
                
                if (matches.length > 0) {
                    keywordResults.files.push({
                        file: filePath,
                        matches: matches
                    });
                    keywordResults.totalMatches += matches.length;
                }
            }
            
            if (keywordResults.totalMatches > 0) {
                this.results.keywordMatches[keyword] = keywordResults;
                this.results.totalMatches += keywordResults.totalMatches;
                console.log(`  ✅ 找到 ${keywordResults.totalMatches} 个匹配`);
                
                // 显示前几个匹配
                const firstFile = keywordResults.files[0];
                if (firstFile && firstFile.matches.length > 0) {
                    const firstMatch = firstFile.matches[0];
                    console.log(`  📄 ${firstFile.file}:${firstMatch.lineNumber}`);
                    console.log(`  📝 ${firstMatch.line}`);
                }
            }
        }
        
        return this.results;
    }

    /**
     * 输出详细结果
     */
    outputDetailedResults() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 详细搜索结果');
        console.log('='.repeat(60));
        
        console.log(`\n📈 总体统计:`);
        console.log(`  🔢 搜索关键词数: ${this.keywords.length}`);
        console.log(`  ✅ 找到匹配的关键词: ${Object.keys(this.results.keywordMatches).length}`);
        console.log(`  📊 总匹配数: ${this.results.totalMatches}`);
        
        if (Object.keys(this.results.keywordMatches).length === 0) {
            console.log('\n❌ 没有找到任何匹配的关键词');
            console.log('💡 可能的原因:');
            console.log('1. JS文件被压缩或混淆');
            console.log('2. 关键词可能使用了不同的命名');
            console.log('3. 代码可能使用了动态字符串构建');
            return;
        }
        
        // 按匹配数排序
        const sortedKeywords = Object.entries(this.results.keywordMatches)
            .sort(([,a], [,b]) => b.totalMatches - a.totalMatches);
        
        console.log('\n🔍 关键词匹配详情 (按匹配数排序):');
        
        for (const [keyword, data] of sortedKeywords.slice(0, 10)) {
            console.log(`\n📌 "${keyword}" - ${data.totalMatches} 个匹配:`);
            
            for (const fileData of data.files.slice(0, 3)) {
                console.log(`  📄 ${fileData.file}:`);
                
                for (const match of fileData.matches.slice(0, 2)) {
                    console.log(`    ${match.lineNumber}: ${match.line}`);
                    
                    // 显示上下文
                    if (match.context && match.context.length > 1) {
                        console.log(`    上下文:`);
                        for (const ctx of match.context) {
                            const marker = ctx.isCurrent ? '>>>' : '   ';
                            console.log(`    ${marker} ${ctx.lineNumber}: ${ctx.line}`);
                        }
                    }
                }
            }
        }
        
        // 特别关注的关键词
        const criticalKeywords = [
            'refreshtoken', 'refresh_token', 'getJwt', 'createJwt',
            'authorized_token', 'third_session', 'getAuthData'
        ];
        
        console.log('\n🎯 关键认证相关匹配:');
        let foundCritical = false;
        
        for (const keyword of criticalKeywords) {
            if (this.results.keywordMatches[keyword]) {
                foundCritical = true;
                const data = this.results.keywordMatches[keyword];
                console.log(`\n🔑 "${keyword}" - ${data.totalMatches} 个匹配:`);
                
                for (const fileData of data.files) {
                    console.log(`  📄 ${fileData.file}:`);
                    for (const match of fileData.matches) {
                        console.log(`    ${match.lineNumber}: ${match.line}`);
                    }
                }
            }
        }
        
        if (!foundCritical) {
            console.log('❌ 没有找到关键认证相关的匹配');
        }
    }

    /**
     * 分析特定的认证模式
     */
    analyzeAuthPatterns() {
        console.log('\n🔍 分析认证模式...');
        
        const authPatterns = [
            // JWT相关模式
            /jwt[_\s]*token/gi,
            /refresh[_\s]*token/gi,
            /access[_\s]*token/gi,
            /auth[_\s]*token/gi,
            
            // API路径模式
            /\/[a-zA-Z]+\/getJwt/gi,
            /\/[a-zA-Z]+\/createJwt/gi,
            /\/[a-zA-Z]+\/refresh/gi,
            /\/[a-zA-Z]+\/token/gi,
            
            // 方法名模式
            /getJwt\s*\(/gi,
            /createJwt\s*\(/gi,
            /refreshToken\s*\(/gi,
            /getAuthData\s*\(/gi,
            
            // 变量赋值模式
            /authorized_token\s*[=:]/gi,
            /Authorization\s*[=:]/gi,
            /third_session\s*[=:]/gi
        ];
        
        const jsFiles = this.getAllJSFiles(this.appPath);
        const patternResults = {};
        
        for (const pattern of authPatterns) {
            const patternName = pattern.source;
            patternResults[patternName] = [];
            
            for (const filePath of jsFiles) {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const matches = content.match(pattern);
                    
                    if (matches) {
                        patternResults[patternName].push({
                            file: filePath,
                            matches: matches
                        });
                    }
                } catch (error) {
                    // 忽略读取错误
                }
            }
        }
        
        console.log('\n📊 认证模式分析结果:');
        for (const [pattern, results] of Object.entries(patternResults)) {
            if (results.length > 0) {
                console.log(`\n🎯 模式: ${pattern}`);
                for (const result of results) {
                    console.log(`  📄 ${result.file}: ${result.matches.join(', ')}`);
                }
            }
        }
    }
}

// 导出类
module.exports = NodeJSSourceSearcher;

// 如果直接运行此文件
if (require.main === module) {
    const searcher = new NodeJSSourceSearcher();
    
    console.log('🔍 Node.js源码搜索器');
    console.log('🎯 使用文件系统API深度搜索认证相关关键词');
    console.log('🔑 专门寻找refreshtoken等认证机制');
    console.log('');
    
    // 执行搜索
    searcher.performCompleteSearch().then(results => {
        // 输出详细结果
        searcher.outputDetailedResults();
        
        // 分析认证模式
        searcher.analyzeAuthPatterns();
        
        if (results.totalMatches > 0) {
            console.log('\n🎉 搜索完成！找到了认证相关的关键信息！');
        } else {
            console.log('\n🤔 没有找到明显的认证关键词');
            console.log('💡 建议:');
            console.log('1. 检查文件是否被压缩或混淆');
            console.log('2. 尝试搜索更通用的模式');
            console.log('3. 分析网络请求和响应');
        }
    }).catch(error => {
        console.error('💥 搜索异常:', error);
    });
}
