<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view class="container" wx:if="{{init}}">
    <view class="detail-box" style="background:#56a2cf">
        <view class="detail-title row-center">
            <text class="primary lg" style="height:100rpx;font-size:14px;line-height:100rpx;color:#fff;">箱码：{{xiangmaCode}}</text>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb" wx:for="{{detail}}" wx:for-index="key" wx:key="id">
        <view class="tr">
            <view class="title">产品编号</view>
            <view class="value  bg-w">{{item.productCode}}</view>
        </view>
        <view class="tr">
            <view class="title">生产日期</view>
            <view class="value bg-g">{{item.productionDate}}</view>
        </view>
        <view class="tr">
            <view class="title">生产单号</view>
            <view class="value bg-g">{{item.productionErpNo}}</view>
        </view>
        <view class="tr">
            <view class="title">进箱序号</view>
            <view class="value bg-g">{{item.indexInBox}}</view>
        </view>
        <view bindtap="chanPinPiHao" class="tr" id="{{key}}">
            <view class="title">产品批号</view>
            <view class="value bg-g" style="color:#21b327">{{item.lotNo}}</view>
        </view>
    </view>
</view>
