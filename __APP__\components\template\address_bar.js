Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=a(require("./../../npm/wepy/lib/wepy.js")),r=a(require("./../../mixins/base.js")),o=require("./../../npm/wepy-redux/lib/index.js"),i=a(require("./../../store/utils.js")),u=a(require("./../../utils/Tips.js"));function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=(0,o.connect)({shop:i.default.get("shop")})(e=function(e){function o(){var e,t,i;s(this,o);for(var a=arguments.length,l=Array(a),f=0;f<a;f++)l[f]=arguments[f];return t=i=c(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(l))),i.data={},i.methods={phone:function(){this.shop?n.default.makePhoneCall({phoneNumber:this.shop.phone}):u.default.alert("该店铺没有电话")},chooseLocation:function(){var e=this;n.default.chooseLocation({type:"wgs84"}).then((function(t){e.shop.latitude=t.latitude,e.shop.longitude=t.longitude;var n=t.location;""!=n&&null!=n||(n="请选择店铺地址"),e.shop.location=n,e.$apply()}))}},i.mixins=[r.default],c(i,t)}var a,l;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:(a=regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.default.init();case 2:this.loaded();case 3:case"end":return e.stop()}}),e,this)})),l=function(){var e=a.apply(this,arguments);return new Promise((function(t,n){return function r(o,i){try{var u=e[o](i),a=u.value}catch(e){return void n(e)}if(!u.done)return Promise.resolve(a).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(a)}("next")}))},function(){return l.apply(this,arguments)})}]),o}())||e;exports.default=l;