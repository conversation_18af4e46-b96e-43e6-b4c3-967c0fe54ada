Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=n(require("./../../npm/wepy/lib/wepy.js")),t=n(require("./../../mixins/countdown.js")),r=n(require("./../../api/bargain.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(o,i){try{var a=t[o](i),u=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(n){function u(){var n,c,s;i(this,u);for(var f=arguments.length,p=Array(f),l=0;l<f;l++)p[l]=arguments[l];return c=s=a(this,(n=u.__proto__||Object.getPrototypeOf(u)).call.apply(n,[this].concat(p))),s.props={bargain:{}},s.data={},s.methods={help:function(t,n){var i=this;return o(regeneratorRuntime.mark((function o(){var a,u,c,s;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,r.default.GoodsBargain(t,n);case 2:a=o.sent,u=e.default.getStorageSync("user"),c=u.id,s=a.details.find((function(e){return e.customer.id===c})).reducePrice,i.$emit("help",s,n);case 6:case"end":return o.stop()}}),o,i)})))()},want:function(e){var t=this;return o(regeneratorRuntime.mark((function r(){return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:t.$root.$navigate("/pages/bargain/goods_detail?ruleId="+e);case 1:case"end":return r.stop()}}),r,t)})))()}},s.components={},s.watch={bargain:function(e){null!=e&&this.countdowm(e.createTime,"groupTime")}},s.mixins=[t.default],a(s,c)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,e.default.component),u}();exports.default=u;