Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./../npm/wepy-redux/lib/index.js"),r=require("./types/cache.js"),t=a(require("./../api/config.js")),n=a(require("./../api/shop.js")),o=a(require("./../api/goods.js")),i=a(require("./../api/coupon.js")),u=a(require("./../api/member.js"));function a(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(o,i){try{var u=r[o](i),a=u.value}catch(e){return void t(e)}if(!u.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}var c,f,l,p,d=(0,e.getStore)(),m={},h=["config","member","coupon"],v=[],g=!1,x=[],y=function(e,t){console.info("[store] save key="+e+", data=",t),d.dispatch({type:r.SAVE,payload:{key:e,value:t}})},w=(c=s(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!g){e.next=5;break}return console.info("[store] store is loading, wait completed"),e.abrupt("return",new Promise((function(e){x.push((function(){e()}))})));case 5:return console.info("[store] start init store"),g=!0,e.next=9,b.apply(void 0,v);case 9:console.info("[store] store init completed"),g=!1,x.forEach((function(e){return e()})),x=[];case 13:case"end":return e.stop()}}),e,void 0)}))),function(){return c.apply(this,arguments)}),b=(f=s(regeneratorRuntime.mark((function e(){for(var r=arguments.length,t=Array(r),n=0;n<r;n++)t[n]=arguments[n];var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!((o=t.filter((function(e){return!E(e)}))).length>0)){e.next=7;break}return console.info("[store] use store: fields="+o),e.next=5,k(o);case 5:e.next=8;break;case 7:console.info("[store] use store: all fields cached");case 8:case"end":return e.stop()}}),e,void 0)}))),function(){return f.apply(this,arguments)}),k=(l=s(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.map((function(e){return R(e)})),e.next=3,Promise.all(t);case 3:n=e.sent,r.forEach((function(e,r){var t=n[r];if(P(e)){var o=Object.keys(t);console.info("[store] load ["+e+"] nest fields = "+o),o.forEach((function(e){return y(e,t[e])}))}else y(e,t)})),y("meta",m);case 6:case"end":return e.stop()}}),e,void 0)}))),function(e){return l.apply(this,arguments)}),j=(p=s(regeneratorRuntime.mark((function e(){for(var r=arguments.length,t=Array(r),n=0;n<r;n++)t[n]=arguments[n];return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.info("[store] reflesh store: fields="+t),e.next=3,k(t);case 3:case"end":return e.stop()}}),e,void 0)}))),function(){return p.apply(this,arguments)}),R=function(e){switch(q(e),e){case"config":return t.default.init();case"member":return u.default.info();case"notices":return n.default.notices();case"status":return n.default.getStatus();case"categories":return o.default.categories();case"coupon":return i.default.all();case"reduce":return n.default.reduces();case"recommend":return o.default.recommend().next();case"version":return n.default.chargeLimit()}},q=function(e){null==m[e]&&(m[e]={},m[e].init=!0),m[e].updateTime=(new Date).getTime()},P=function(e){return h.some((function(r){return r==e}))},E=function(e){if(null==m[e]||1!=m[e].init)return!1;var r=m[e].updateTime;return(new Date).getTime()-r<3e5};exports.default={get:function(e){return function(r){return r.cache[e]}},save:y,use:b,refresh:j,init:w};