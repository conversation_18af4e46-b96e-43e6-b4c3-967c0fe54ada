Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=require("./../../npm/wepy/lib/wepy.js"),o=(e=t)&&e.__esModule?e:{default:e};function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(e){function t(){var e,o,a;r(this,t);for(var s=arguments.length,i=Array(s),c=0;c<s;c++)i[c]=arguments[c];return o=a=n(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i))),a.props={tab:{twoWay:!0},fixed:{default:!0}},a.data={},a.methods={handleZanTabChange:function(e){var t=e.currentTarget.dataset,o=t.componentId,r=t.itemId,n={componentId:o,selectedId:r};this.tab.order&&(this.tab.selectedId==r&&(this.tab.sord="desc"==this.tab.sord?"asc":"desc"),n.sord=this.tab.sord);this.tab.selectedId=r,this.$emit("change",n)}},a.events={},n(a,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,o.default.component),t}();exports.default=a;