Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),n=c(require("./../../npm/wepy/lib/wepy.js")),t=c(require("./../../api/member.js")),r=c(require("./../../mixins/base.js")),a=c(require("./../../components/common/loading.js")),i=c(require("./../../utils/Tips.js")),s=c(require("./../../api/garden.js")),o=c(require("./../../components/plant/head/modal.js"));function c(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(){var n=e.apply(this,arguments);return new Promise((function(e,t){return function r(a,i){try{var s=n[a](i),o=s.value}catch(e){return void t(e)}if(!s.done)return Promise.resolve(o).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(o)}("next")}))}}function p(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function d(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}var g=function(c){function g(){var e,c,l;p(this,g);for(var h=arguments.length,f=Array(h),x=0;x<h;x++)f[x]=arguments[x];return c=l=d(this,(e=g.__proto__||Object.getPrototypeOf(g)).call.apply(e,[this].concat(f))),l.data={userId:"",regionArr:[],radioItems:[{name:"USA",value:"美国"},{name:"CHN",value:"中国",checked:"true"}],region:["广东省","广州市","海珠区"],flag:!1,checked:!1,personage:{province:"",province_id:"",city:"",city_id:"",area:"",area_id:"",address:"",birthday:"",id_card:"",real_name:"",phone_no:"",sex:1},Addressv:"",num:"",where:"",provinces:[],citys:[],areas:[],ID:"-",ID1:"-",ID2:"-",init:!1,idCardDisabled:!0,tipMsg:"您好！",showMsg:!1,err:"none",returnAge:"",idcards:"",is_hidden:!1},l.$repeat={},l.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},TipsModal:{"xmlns:v-on":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},l.$events={TipsModal:{"v-on:closeIt":"closeIt"}},l.components={Loading:a.default,TipsModal:o.default},l.mixins=[r.default],l.methods={goUpdateAvatarAndNick:function(){var e=encodeURIComponent("https://wap.exijiu.com/index.php/Xjhyjlb/User/showXcxUserInfo?uid="+this.userId),n="/pages/web/webView?url="+e;console.log("到公众号修改头像信息",e),this.$navigate(n)},bindRegionChange:function(e){console.log("picker发送选择改变，携带值为",e.detail.value),this.region=e.detail.value},closeIt:function(){this.showMsg=!1,this.err||wx.navigateBack(),this.$apply()},saved:function(){n.default.setStorageSync("improved",!0),this.$navigate("/pages/plant/index")},bindPickerChange:function(){this.flag=!0},nocv:function(){this.flag=!1},gotoSeeRules:function(e){var n=e.currentTarget.dataset.url;this.$navigate(n)},yes:function(){this.flag=!1},checkIt:function(){this.checked=!this.checked},closeActivityWindow:function(){this.is_hidden=!1,this.$apply()},election:function(e){console.log(e);var n=e.detail.value;"男"==n?(this.personage.sex="1",this.$apply()):"女"==n?(this.personage.sex="2",this.$apply()):(this.personage.sex="0",this.$apply())},province1:function(e){var n=this;return u(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=parseInt(e.currentTarget.dataset.index),n.district="",n.personage.city="",n.personage.area="",n.ID=r,n.$apply(),"2",a={parent_id:n.provinces[r].id},n.personage.province_id=n.provinces[r].id,n.personage.province=n.provinces[r].name,t.next=12,s.default.address(a);case 12:n.citys=t.sent,n.areas="",n.Addressv=n.personage.province+","+n.personage.city+","+n.personage.area,n.$apply();case 16:case"end":return t.stop()}}),t,n)})))()},city1:function(e){var n=this;return u(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=parseInt(e.currentTarget.dataset.index),n.personage.area="",n.ID1=r,n.ID2="-","3",a={parent_id:n.citys[r].id},n.personage.city_id=n.citys[r].id,n.personage.city=n.citys[r].name,t.next=10,s.default.address(a);case 10:n.areas=t.sent,n.Addressv=n.personage.province+","+n.personage.city+","+n.personage.area,n.$apply();case 13:case"end":return t.stop()}}),t,n)})))()},district1:function(e){var n=parseInt(e.currentTarget.dataset.index);this.ID2=n,this.personage.area_id=this.areas[n].id,this.personage.area=this.areas[n].name,this.Addressv=this.personage.province+","+this.personage.city+","+this.personage.area,this.$apply()},getbirth:function(e){var n=e.detail.value;this.idcards=n,console.log(n);if(!n.match(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/))return wx.showToast({title:"身份证号错误",icon:"none"}),!1;if(15==n.length){var t="19"+n.substring(6,8)+"-"+n.substring(8,10)+"-"+n.substring(10,12);this.id_card=t,this.personage.birthday=t,this.$apply()}else if(18==n.length){var r=n.substring(6,10)+"-"+n.substring(10,12)+"-"+n.substring(12,14);if(this.id_card=r,this.personage.birthday=r,this.getAge(this.id_card),this.returnAge<18)return wx.showToast({title:"温馨提示，未满18周岁不能注册。",icon:"none"}),!1;if(-1==this.returnAge)return wx.showToast({title:"出生日期不能晚于今天。",icon:"none"}),!1;this.$apply()}},bangding1:function(e){var n=e.currentTarget.dataset,t=e.detail.value,r=n.name;this.data[r]=t,this.personage.real_name=this.data[r],this.$apply()},bangding2:function(e){var n=e.currentTarget.dataset,t=e.detail.value,r=n.phone;this.data[r]=t,this.personage.phone_no=this.data[r],this.$apply()},bangding3:function(e){var n=e.currentTarget.dataset,t=e.detail.value,r=n.num;this.data[r]=t,this.idcards=this.data[r],this.$apply()},getIdcards:function(e){var n=e.detail.value;this.idcards=n,this.$apply()},bangding4:function(e){var n=e.currentTarget.dataset,t=e.detail.value,r=n.place;this.data[r]=t,this.personage.address=this.data[r],this.$apply()},preservation:function(){var e=this;return u(regeneratorRuntime.mark((function n(){var t,r,a,i,o,c,u;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=/^1\d{10}$/,a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,0!=(t=e).personage.sex){n.next=6;break}return wx.showToast({title:"请选择性别",icon:"none"}),n.abrupt("return",!1);case 6:if(t.personage.real_name.replace(/\s+/g,"")){n.next=11;break}return wx.showToast({title:"姓名不能为空",icon:"none"}),n.abrupt("return",!1);case 11:if(0!=t.personage.phone_no.length){n.next=16;break}return wx.showToast({title:"联系电话不能为空",icon:"none"}),n.abrupt("return",!1);case 16:if(!(t.personage.phone_no.length<11||t.personage.phone_no.length>11)){n.next=21;break}return wx.showToast({title:"联系电话长度有误！",icon:"none"}),n.abrupt("return",!1);case 21:if(t.personage.phone_no.match(r)){n.next=26;break}return wx.showToast({title:"联系电话格式有误！",icon:"none"}),n.abrupt("return",!1);case 26:if(t.idcards){n.next=30;break}wx.showToast({title:"身份证号不能为空",icon:"none"}),n.next=69;break;case 30:if(!1!==t.idcards.match(a)){n.next=35;break}return wx.showToast({title:"请输入正确的身份证号",icon:"none"}),n.abrupt("return",!1);case 35:if(t.personage.birthday){n.next=40;break}return wx.showToast({title:"请选择生日",icon:"none"}),n.abrupt("return",!1);case 40:if(""!=t.personage.area&&""!=t.personage.city&&""!=t.personage.province){n.next=44;break}wx.showToast({title:"请把省，市，区选择完整",icon:"none"}),n.next=69;break;case 44:if(t.personage.address.replace(/\s+/g,"")){n.next=48;break}wx.showToast({title:"详细地址不能为空",icon:"none"}),n.next=69;break;case 48:if(t.checked){n.next=52;break}wx.showToast({title:"请先阅读并同意用户服务协议和隐私保护指引",icon:"none"}),n.next=69;break;case 52:if(e.getAge(t.personage.birthday),!(e.returnAge<18)){n.next=56;break}return wx.showToast({title:"温馨提示，未满18周岁不能注册。",icon:"none"}),n.abrupt("return",!1);case 56:if(-1!=e.returnAge){n.next=59;break}return wx.showToast({title:"出生日期不能晚于今天。",icon:"none"}),n.abrupt("return",!1);case 59:return i={source:"xijiu_garden",id:t.userId,real_name:t.personage.real_name.replace(/\s+/g,""),phone_no:t.personage.phone_no.replace(/\s+/g,""),sex:t.personage.sex,birthday:t.personage.birthday,id_card:t.idcards?t.idcards:t.personage.id_card.replace(/\s+/g,""),province_id:t.personage.province_id,province:t.personage.province,city_id:t.personage.city_id,city:t.personage.city,area_id:t.personage.area_id,area:t.personage.area,address:t.personage.address},n.next=62,s.default.ImproveInfo(i);case 62:o=n.sent,c=o.msg,u=o.err,e.err=u,e.tipMsg=c,e.showMsg=!0,e.$apply();case 69:case"end":return n.stop()}}),n,e)})))()},preservations:function(){var e=this;return u(regeneratorRuntime.mark((function n(){var t,r,a,i,o,c,u;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=/^1\d{10}$/,a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,0!=(t=e).personage.sex){n.next=6;break}return wx.showToast({title:"请选择性别",icon:"none"}),n.abrupt("return",!1);case 6:if(t.personage.real_name.replace(/\s+/g,"")){n.next=11;break}return wx.showToast({title:"姓名不能为空",icon:"none"}),n.abrupt("return",!1);case 11:if(0!=t.personage.phone_no.length){n.next=16;break}return wx.showToast({title:"联系电话不能为空",icon:"none"}),n.abrupt("return",!1);case 16:if(!(t.personage.phone_no.length<11||t.personage.phone_no.length>11)){n.next=21;break}return wx.showToast({title:"联系电话长度有误！",icon:"none"}),n.abrupt("return",!1);case 21:if(t.personage.phone_no.match(r)){n.next=26;break}return wx.showToast({title:"联系电话格式有误！",icon:"none"}),n.abrupt("return",!1);case 26:if(t.personage.id_card){n.next=30;break}wx.showToast({title:"身份证号不能为空",icon:"none"}),n.next=69;break;case 30:if(!1!==t.personage.id_card.match(a)){n.next=35;break}return wx.showToast({title:"请输入正确的身份证号",icon:"none"}),n.abrupt("return",!1);case 35:if(t.personage.birthday){n.next=40;break}return wx.showToast({title:"请选择生日",icon:"none"}),n.abrupt("return",!1);case 40:if(""!=t.personage.area&&""!=t.personage.city&&""!=t.personage.province){n.next=44;break}wx.showToast({title:"请把省，市，区选择完整",icon:"none"}),n.next=69;break;case 44:if(t.personage.address.replace(/\s+/g,"")){n.next=48;break}wx.showToast({title:"详细地址不能为空",icon:"none"}),n.next=69;break;case 48:if(t.checked){n.next=52;break}wx.showToast({title:"请先阅读并同意用户服务协议和隐私保护指引",icon:"none"}),n.next=69;break;case 52:if(e.getAge(t.personage.birthday),!(e.returnAge<18)){n.next=56;break}return wx.showToast({title:"温馨提示，未满18周岁不能注册。",icon:"none"}),n.abrupt("return",!1);case 56:if(-1!=e.returnAge){n.next=59;break}return wx.showToast({title:"出生日期不能晚于今天。",icon:"none"}),n.abrupt("return",!1);case 59:return i={source:"xijiu_garden",id:t.userId,real_name:t.personage.real_name.replace(/\s+/g,""),phone_no:t.personage.phone_no.replace(/\s+/g,""),sex:t.personage.sex,birthday:t.personage.birthday,id_card:t.idcards?t.idcards:t.personage.id_card.replace(/\s+/g,""),province_id:t.personage.province_id,province:t.personage.province,city_id:t.personage.city_id,city:t.personage.city,area_id:t.personage.area_id,area:t.personage.area,address:t.personage.address},n.next=62,s.default.ImproveInfo(i);case 62:o=n.sent,c=o.msg,u=o.err,e.err=u,e.tipMsg=c,e.showMsg=!0,e.$apply();case 69:case"end":return n.stop()}}),n,e)})))()},unbindwechat:function(){var e=this;return u(regeneratorRuntime.mark((function r(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.default.confirms("退出登录后，重新登录需验证手机号，是否退出登录？",{},"提示","确定").then((function(e){t.default.unbindwechat().then((function(e){wx.showToast({title:"解绑成功！",icon:"none",duration:2e3}),n.default.clearStorageSync(),wx.exitMiniProgram()})).catch((function(e){wx.showToast({title:e.message,icon:"none",duration:2e3})}))})).catch((function(e){}));case 2:case"end":return e.stop()}}),r,e)})))()},cancellation:function(){var e=this;return u(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i.default.confirms("注销后会员信息及积分将清空，无法恢复，是否注销账号？",{},"提示","确定").then((function(n){e.is_hidden=!0,e.$apply()}));case 2:case"end":return n.stop()}}),n,e)})))()},goSubmit:function(){var e=this;return u(regeneratorRuntime.mark((function r(){var a,s,o;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(a=e,console.log(a.idcards),s=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,a.idcards||wx.showToast({title:"身份证号不能为空!",icon:"none"}),a.idcards.match(s)){r.next=9;break}return wx.showToast({title:"请输入正确的身份证号!",icon:"none"}),r.abrupt("return",!1);case 9:return i.default.loading(),o={id_card:a.idcards},r.next=13,t.default.cancellation(o).then((function(t){e.is_hidden=!1,wx.showToast({title:"注销成功！",icon:"none"}),n.default.clearStorageSync(),setTimeout((function(){wx.exitMiniProgram()}),3e3)})).catch((function(e){wx.showToast({title:e.message,icon:"none"})}));case 13:e.$apply(),e.loaded();case 15:case"end":return r.stop()}}),r,e)})))()},getPhoneNumber:function(e){var r=this;return u(regeneratorRuntime.mark((function a(){var s,o,c,u,p;return regeneratorRuntime.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,n.default.login();case 2:if(s=a.sent,o=s.code,"getPhoneNumber:ok"==e.detail.errMsg){a.next=7;break}return i.default.alert("授权后才可变更手机！"),a.abrupt("return");case 7:return i.default.longLoading(),c={code:o,iv:e.detail.iv,encryptedData:e.detail.encryptedData},a.prev=9,a.next=12,t.default.modifyPhone(c);case 12:u=a.sent,p=u.phone_no,r.personage.phone_no=p,r.$apply(),i.default.confirm("修改成功"),a.next=22;break;case 19:a.prev=19,a.t0=a.catch(9),i.default.confirm(a.t0.message);case 22:i.default.loaded();case 23:case"end":return a.stop()}}),a,r,[[9,19]])})))()},goRules:function(e){var t="",r=n.default.$instance.globalData.jifenShopUrl;if("user"==e?t=encodeURIComponent(r+"/#/pages/notice/detail?id=3"):"privacy"==e&&(t=encodeURIComponent(r+"/#/pages/notice/detail?id=4")),t){var a="/pages/web/webView?url="+t+"&nologin=true";console.log(a),this.$navigate(a)}}},l.config={navigationBarBackgroundColor:"#fff",navigationBarTitleText:"个人信息修改",navigationBarTextStyle:"black"},d(l,c)}var l;return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}(g,n.default.page),e(g,[{key:"onLoad",value:(l=u(regeneratorRuntime.mark((function e(n){var t,r,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!n.id){e.next=4;break}this.userId=n.id,e.next=8;break;case 4:return e.next=6,s.default.memberInfo();case 6:t=e.sent,this.userId=t.member_id;case 8:return e.next=10,s.default.address({parent_id:1e5});case 10:return r=e.sent,e.next=13,s.default.getImprovedInfo({id:n.id});case 13:a=e.sent,this.idCardDisabled=!!a.id_card,this.personage=a,this.getAge(this.personage.birthday),this.provinces=r,this.where=a.province+","+a.city+","+a.area,this.$apply(),this.loaded();case 21:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"getAge",value:function(e){if(!e)e=this.personage.birthday;var n,t=e?e.split("-"):"",r=t[0],a=t[1],i=t[2],s=new Date,o=s.getFullYear(),c=s.getMonth()+1,u=s.getDate();if(o==r)n=0;else{var p=o-r;if(p>0)if(c==a)n=u-i<0?p-1:p;else n=c-a<0?p-1:p;else n=-1}this.returnAge=n,this.$apply()}}]),g}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(g,"pages/plant/Improve"));