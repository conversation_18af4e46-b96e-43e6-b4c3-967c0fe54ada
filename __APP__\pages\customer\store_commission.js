Object.defineProperty(exports,"__esModule",{value:!0});var t=function(){function t(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,o,n){return o&&t(e.prototype,o),n&&t(e,n),e}}(),e=n(require("./../../npm/wepy/lib/wepy.js")),o=(n(require("./../../api/auth.js")),n(require("./../../store/utils.js")),n(require("./../../api/config.js")),n(require("./../../mixins/base.js")),n(require("./../../components/common/loading.js")),n(require("./../../mixins/router.js")),n(require("./../../api/member.js")));function n(t){return t&&t.__esModule?t:{default:t}}function r(t){return function(){var e=t.apply(this,arguments);return new Promise((function(t,o){return function n(r,i){try{var s=e[r](i),a=s.value}catch(t){return void o(t)}if(!s.done)return Promise.resolve(a).then((function(t){n("next",t)}),(function(t){n("throw",t)}));t(a)}("next")}))}}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var a=function(n){function a(){var t,e,n;i(this,a);for(var u=arguments.length,h=Array(u),c=0;c<u;c++)h[c]=arguments[c];return e=n=s(this,(t=a.__proto__||Object.getPrototypeOf(a)).call.apply(t,[this].concat(h))),n.data={switchTabSwipering:!1,init:!1,navData:[{text:"白银会员"},{text:"黄金会员"},{text:"铂金会员"},{text:"钻石会员"},{text:"皇冠会员"}],showModal:!1,showModalby:!1,showModalhj:!1,showModalzs:!1,imgalist:["http://wap.exijiu.cn/Public/Qrcode/images/biankuang.png"],currentTab:0,navScrollLeft:0,os:"",phone_model:"",brower:"",networkType:"",platform:"",userInfo:!1,giftImage:""},n.methods={goto:function(t){var e=this;return r(regeneratorRuntime.mark((function n(){var r;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("/pages/customer/goToUmall?pagePath=%2Fpages%2Frich%2Flevel"!=t.currentTarget.dataset.url){n.next=3;break}return n.next=3,o.default.buryingPoint(138,e.userInfo.id?e.userInfo.id:"",e.os,e.brower,e.phone_model,e.networkType);case 3:if("/pages/anticounterfeiting/process"!=t.currentTarget.dataset.url){n.next=6;break}return n.next=6,o.default.buryingPoint(139,e.userInfo.id?e.userInfo.id:"",e.os,e.brower,e.phone_model,e.networkType);case 6:r=t.currentTarget.dataset.url,console.log(r),e.$navigate(r);case 9:case"end":return n.stop()}}),n,e)})))()},submits:function(){o.default.buryingPoint(140,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),this.setData({showModal:!0})},submitsby:function(){o.default.buryingPoint(141,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),this.setData({showModalby:!0})},submitshj:function(){o.default.buryingPoint(142,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),this.setData({showModalhj:!0})},submitszs:function(){o.default.buryingPoint(143,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),this.setData({showModalzs:!0})},submitshg:function(){o.default.buryingPoint(144,this.userInfo.id?this.userInfo.id:"",this.os,this.brower,this.phone_model,this.networkType),this.setData({showModalhg:!0})},preventTouchMove:function(){},preventTouchMoveby:function(){},preventTouchMovehj:function(){},preventTouchMovezs:function(){},preventTouchMovehg:function(){},gos:function(){this.setData({showModal:!1})},gosby:function(){this.setData({showModalby:!1})},goshj:function(){this.setData({showModalhj:!1})},goszs:function(){this.setData({showModalzs:!1})},goshg:function(){this.setData({showModalhg:!1})},setCurrentTab:function(t){this.currentTab=t},showGiftDialog:function(t){this.giftImage="http://wap.exijiu.com/Public/MemberClubV2/images/new/"+t+".png"},hideGiftDialog:function(){this.giftImage=""}},n.components={},n.computed={},n.config={navigationBarTitleText:"等级特权"},s(n,e)}var u,h;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(a,e.default.page),t(a,[{key:"onLoad",value:(h=r(regeneratorRuntime.mark((function t(o){var n=this,r=o.showModal,i=o.showModalby,s=o.showModalhj,a=o.showModalzs,u=o.showModalhg,h=o.name;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.default.setNavigationBarColor({backgroundColor:"#024236",frontColor:"#ffffff"}),this.userInfo=e.default.getStorageSync("userInfo"),this.showModal=r,this.showModalby=i,this.showModalhj=s,this.showModalzs=a,this.showModalhg=u,e.default.getSystemInfo({success:function(t){n.setData({pixelRatio:t.pixelRatio,windowHeight:t.windowHeight,windowWidth:t.windowWidth})}}),this.getUserSystemInfo(),this.getNetwork(),h&&(this.currentTab=this.navData.findIndex((function(t,e){return t.text==h})));case 11:case"end":return t.stop()}}),t,this)}))),function(t){return h.apply(this,arguments)})},{key:"getUserSystemInfo",value:function(){var t=this;wx.getSystemInfo({success:function(e){console.log(e),t.os=e.system?e.system:"",t.phone_model=e.model?e.model:"",t.brower="iPhone"==e.model?"safari":"chrome",t.$apply()}})}},{key:"getNetwork",value:function(){var t=this;wx.getNetworkType({success:function(e){var o=e.networkType?e.networkType:"";t.networkType=o,t.$apply()}})}},{key:"switchNav",value:function(t){console.log(t);var e=t.currentTarget.dataset.current,o=this.data.windowWidth/5;if(this.navScrollLeft=(e-2)*o,this.$apply(),this.data.currentTab==e)return!1;this.currentTab=e,this.$apply()}},{key:"switchTab",value:function(t){if(this.switchTabSwipering)return!1;this.switchTabSwipering=!0,this.$apply();var e=t.detail.current,o=this.data.windowWidth/5;this.currentTab=e,this.navScrollLeft=(e-2)*o,this.$apply()}},{key:"switchTabEnd",value:function(t){this.switchTabSwipering=!1,this.$apply()}},{key:"onShow",value:(u=r(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t,this)}))),function(){return u.apply(this,arguments)})}]),a}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(a,"pages/customer/store_commission"));