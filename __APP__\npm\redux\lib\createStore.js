exports.__esModule=!0,exports.ActionTypes=void 0,exports.default=function r(o,i,u){var c;"function"==typeof i&&void 0===u&&(u=i,i=void 0);if(void 0!==u){if("function"!=typeof u)throw new Error("Expected the enhancer to be a function.");return u(r)(o,i)}if("function"!=typeof o)throw new Error("Expected the reducer to be a function.");var f=o,s=i,a=[],d=a,p=!1;function l(){d===a&&(d=a.slice())}function b(){return s}function y(e){if("function"!=typeof e)throw new Error("Expected listener to be a function.");var t=!0;return l(),d.push(e),function(){if(t){t=!1,l();var r=d.indexOf(e);d.splice(r,1)}}}function h(t){if(!(0,e.default)(t))throw new Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===t.type)throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(p)throw new Error("Reducers may not dispatch actions.");try{p=!0,s=f(s,t)}finally{p=!1}for(var r=a=d,n=0;n<r.length;n++){(0,r[n])()}return t}return h({type:n.INIT}),(c={dispatch:h,subscribe:y,getState:b,replaceReducer:function(e){if("function"!=typeof e)throw new Error("Expected the nextReducer to be a function.");f=e,h({type:n.INIT})}})[t.default]=function(){var e,r=y;return(e={subscribe:function(e){if("object"!=typeof e)throw new TypeError("Expected the observer to be an object.");function t(){e.next&&e.next(b())}return t(),{unsubscribe:r(t)}}})[t.default]=function(){return this},e},c};var e=r(require("./../../lodash/isPlainObject.js")),t=r(require("./../../symbol-observable/lib/index.js"));function r(e){return e&&e.__esModule?e:{default:e}}var n=exports.ActionTypes={INIT:"@@redux/INIT"};