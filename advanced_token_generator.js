/**
 * 高级Token生成器
 * 基于小程序源码深度分析的Token生成方案
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AdvancedTokenGenerator {
    constructor() {
        // 从小程序源码中提取的关键信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码分析得出的配置
        this.appConfig = {
            appId: 'wx489f950decfeb93e',
            appCode: 'owVHb1gHrvktni80kjMlFMzSDJDWY0xR', // 从app.js中提取
            version: 'v3.2.6',
            shopType: '1',
            shopName: '防伪查询'
        };
        
        // 从config-prod.js分析的API域名
        this.apiDomains = {
            baseUrl: 'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
            forumBaseUrl: 'https://apiforum.exijiu.com',
            wechatRedpacketUrl: 'https://wap.exijiu.com/index.php/API',
            jifenShopApiUrl: 'https://apimallwm.exijiu.com'
        };
        
        // 从源码分析的登录流程
        this.loginInfo = this.decodeJWT(this.loginCode);
        this.unionId = this.loginInfo.unionid;
        this.memberId = 6865357; // 已知的会员ID
        
        console.log('🔧 高级Token生成器初始化完成');
        console.log('📱 App版本:', this.appConfig.version);
        console.log('🔑 UnionID:', this.unionId);
        console.log('👤 会员ID:', this.memberId);
    }

    /**
     * 解析JWT Token
     */
    decodeJWT(token) {
        try {
            const parts = token.split('.');
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            return payload;
        } catch (e) {
            return null;
        }
    }

    /**
     * 构建完整的小程序请求头
     */
    buildMiniProgramHeaders(includeAuth = false, authToken = null) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
            'Referer': `https://servicewechat.com/${this.appConfig.appId}/devtools/page-frame.html`,
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            
            // 小程序特有头部
            'login_code': this.loginCode,
            'X-WX-AppId': this.appConfig.appId,
            'X-WX-Version': this.appConfig.version,
            'X-WX-Platform': 'ios',
            'X-WX-System': 'iOS 16.6',
            'X-App-Code': this.appConfig.appCode,
            'X-Shop-Type': this.appConfig.shopType
        };
        
        if (includeAuth && authToken) {
            headers['Authorization'] = authToken;
        }
        
        return headers;
    }

    /**
     * 方法1: 模拟完整的微信登录流程
     */
    async simulateCompleteWxLogin() {
        console.log('\n🔄 方法1: 模拟完整微信登录流程...');
        
        try {
            // 步骤1: 模拟wx.login()获取code
            const mockCode = this.generateRealisticWxCode();
            console.log('📱 生成模拟微信code:', mockCode);
            
            // 步骤2: 调用session接口
            const sessionData = await this.callSessionAPI(mockCode);
            if (sessionData && sessionData.login_code) {
                console.log('✅ 获取到session数据');
                
                // 步骤3: 使用新的login_code调用登录接口
                const authToken = await this.callGardenLoginAPI(sessionData.login_code);
                if (authToken) {
                    console.log('✅ 完整登录流程成功!');
                    return authToken;
                }
            }
            
        } catch (error) {
            console.log('❌ 完整登录流程失败:', error.message);
        }
        
        return null;
    }

    /**
     * 方法2: 尝试Token刷新接口
     */
    async tryTokenRefresh() {
        console.log('\n🔄 方法2: 尝试Token刷新接口...');
        
        const refreshAPIs = [
            { path: '/auth/refresh', data: { login_code: this.loginCode } },
            { path: '/garden/auth/refresh', data: { login_code: this.loginCode } },
            { path: '/auth/token/refresh', data: { login_code: this.loginCode, unionid: this.unionId } },
            { path: '/garden/token/refresh', data: { login_code: this.loginCode, unionid: this.unionId } }
        ];
        
        for (const api of refreshAPIs) {
            try {
                console.log(`🧪 测试刷新接口: ${api.path}`);
                const result = await this.makeRequest(api.path, 'POST', api.data);
                
                if (result.success && result.data) {
                    const token = this.extractTokenFromResponse(result.data);
                    if (token) {
                        console.log('✅ Token刷新成功!');
                        return token;
                    }
                }
            } catch (error) {
                console.log(`❌ ${api.path} 失败`);
            }
        }
        
        return null;
    }

    /**
     * 方法3: 基于源码分析的特殊登录流程
     */
    async trySpecialLoginFlow() {
        console.log('\n🔄 方法3: 基于源码分析的特殊登录流程...');
        
        try {
            // 从源码分析，小程序有特殊的认证流程
            const specialData = {
                code: this.loginCode,
                unionid: this.unionId,
                app_code: this.appConfig.appCode,
                version: this.appConfig.version,
                shop_type: this.appConfig.shopType,
                third_session: this.generateThirdSession()
            };
            
            const specialAPIs = [
                { path: '/garden/wechat/auth', data: specialData },
                { path: '/auth/session', data: specialData },
                { path: '/garden/auth/session', data: specialData }
            ];
            
            for (const api of specialAPIs) {
                try {
                    console.log(`🧪 测试特殊登录: ${api.path}`);
                    const result = await this.makeRequest(api.path, 'POST', api.data);
                    
                    if (result.success && result.data) {
                        const token = this.extractTokenFromResponse(result.data);
                        if (token) {
                            console.log('✅ 特殊登录流程成功!');
                            return token;
                        }
                    }
                } catch (error) {
                    console.log(`❌ ${api.path} 失败`);
                }
            }
            
        } catch (error) {
            console.log('❌ 特殊登录流程失败:', error.message);
        }
        
        return null;
    }

    /**
     * 方法4: 高级算法生成Token
     */
    async tryAdvancedAlgorithm() {
        console.log('\n🔄 方法4: 高级算法生成Token...');
        
        // 基于源码分析的高级密钥生成策略
        const advancedSecrets = [
            // 基于app配置的密钥
            this.appConfig.appCode,
            this.appConfig.appCode.substring(0, 16),
            crypto.createHash('md5').update(this.appConfig.appCode).digest('hex'),
            
            // 基于版本和配置的组合密钥
            `${this.appConfig.appId}_${this.appConfig.version}`,
            `${this.appConfig.shopType}_${this.appConfig.appCode}`,
            `xijiu_${this.appConfig.version}`,
            
            // 基于UnionID和会员ID的复合密钥
            `${this.unionId}_${this.memberId}`,
            crypto.createHash('sha256').update(`${this.unionId}${this.memberId}`).digest('hex').substring(0, 32),
            
            // 基于时间戳的动态密钥
            this.generateTimeBasedSecret(),
            this.generateDateBasedSecret()
        ];
        
        for (const secret of advancedSecrets) {
            try {
                console.log(`🧪 测试高级密钥: ${secret.substring(0, 20)}...`);
                
                const token = this.generateAdvancedJWT(secret);
                const isValid = await this.validateToken(token);
                
                if (isValid) {
                    console.log('✅ 高级算法生成成功!');
                    console.log('🔑 有效密钥:', secret);
                    return token;
                }
                
            } catch (error) {
                console.log(`❌ 密钥 ${secret.substring(0, 10)}... 失败`);
            }
        }
        
        return null;
    }

    /**
     * 生成真实的微信code
     */
    generateRealisticWxCode() {
        const timestamp = Date.now();
        const random1 = Math.random().toString(36).substring(2, 10);
        const random2 = Math.random().toString(36).substring(2, 6);
        return `${random1}${timestamp.toString(36)}${random2}`;
    }

    /**
     * 生成third_session
     */
    generateThirdSession() {
        const timestamp = Date.now();
        const random = crypto.randomBytes(16).toString('hex');
        return `${timestamp}_${random}`;
    }

    /**
     * 生成基于时间的密钥
     */
    generateTimeBasedSecret() {
        const now = new Date();
        const timeStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`;
        return crypto.createHash('md5').update(`xijiu_${timeStr}`).digest('hex').substring(0, 16);
    }

    /**
     * 生成基于日期的密钥
     */
    generateDateBasedSecret() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
        return crypto.createHash('sha256').update(`${this.appConfig.appId}_${dateStr}`).digest('hex').substring(0, 24);
    }

    /**
     * 生成高级JWT Token
     */
    generateAdvancedJWT(secret) {
        const now = Math.floor(Date.now() / 1000);
        const expireTime = now + (6 * 30 * 24 * 60 * 60); // 6个月后
        
        const payload = {
            memberInfo: {
                id: this.memberId
            },
            expireTime: expireTime,
            iat: now, // 签发时间
            unionid: this.unionId // 添加unionid
        };
        
        return this.generateJWT(payload, secret);
    }

    /**
     * 生成JWT Token
     */
    generateJWT(payload, secret) {
        const header = { "typ": "JWT", "alg": "HS256" };
        
        const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        const signature = crypto
            .createHmac('sha256', secret)
            .update(encodedHeader + '.' + encodedPayload)
            .digest('base64')
            .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        
        return `${encodedHeader}.${encodedPayload}.${signature}`;
    }

    /**
     * 调用session API
     */
    async callSessionAPI(code) {
        try {
            const result = await this.makeRequest(`/auth/session?code=${code}`, 'GET');
            return result.success ? result.data : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 调用garden登录API
     */
    async callGardenLoginAPI(loginCode) {
        try {
            const result = await this.makeRequest(`/garden/wechat/login?code=${loginCode}`, 'GET');
            return result.success ? this.extractTokenFromResponse(result.data) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 从响应中提取Token
     */
    extractTokenFromResponse(data) {
        const tokenFields = [
            'authorized_token',
            'Authorization',
            'auth_token',
            'token',
            'access_token',
            'jwt_token'
        ];
        
        for (const field of tokenFields) {
            if (data[field]) {
                return data[field];
            }
        }
        
        return null;
    }

    /**
     * 验证Token有效性
     */
    async validateToken(token) {
        try {
            const result = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo', 'GET', null, true, token);
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null, includeAuth = false, authToken = null) {
        const headers = this.buildMiniProgramHeaders(includeAuth, authToken);
        
        // 尝试所有API域名
        const domains = Object.values(this.apiDomains);
        
        for (const baseUrl of domains) {
            try {
                const result = await this.tryRequest(baseUrl, path, method, data, headers);
                if (result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 主要方法：尝试所有高级方法生成authtoken
     */
    async generateAuthToken() {
        console.log('🚀 开始高级Token生成流程...');
        
        // 方法1: 完整微信登录流程
        let authToken = await this.simulateCompleteWxLogin();
        if (authToken) return authToken;
        
        // 方法2: Token刷新接口
        authToken = await this.tryTokenRefresh();
        if (authToken) return authToken;
        
        // 方法3: 特殊登录流程
        authToken = await this.trySpecialLoginFlow();
        if (authToken) return authToken;
        
        // 方法4: 高级算法生成
        authToken = await this.tryAdvancedAlgorithm();
        if (authToken) return authToken;
        
        console.log('\n❌ 所有高级方法都失败了');
        console.log('\n💡 深度分析结果:');
        console.log('1. 小程序使用了复杂的认证机制');
        console.log('2. Token生成可能依赖服务端状态');
        console.log('3. 可能需要真实的微信环境');
        console.log('4. 建议使用现有Token配合会话保活');
        
        return null;
    }
}

// 导出类
module.exports = AdvancedTokenGenerator;

// 如果直接运行此文件
if (require.main === module) {
    const generator = new AdvancedTokenGenerator();
    
    generator.generateAuthToken().then(authToken => {
        if (authToken) {
            console.log('\n🎉 高级方法成功生成authtoken!');
            console.log('🔑 AuthToken:', authToken);
            
            // 解析生成的Token
            const decoded = generator.decodeJWT(authToken);
            if (decoded) {
                console.log('📊 Token信息:');
                console.log(JSON.stringify(decoded, null, 2));
            }
        } else {
            console.log('\n❌ 高级方法无法生成authtoken');
            console.log('\n🎯 最终建议:');
            console.log('1. 你的现有authtoken有效期到2025年6月，建议直接使用');
            console.log('2. 配合会话保活器解决手动进入页面问题');
            console.log('3. 当Token真正过期时，重新获取login_code和authtoken对');
        }
    }).catch(error => {
        console.error('高级生成失败:', error);
    });
}
