Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=p(require("./../../npm/wepy/lib/wepy.js")),r=p(require("./../../api/order.js")),a=p(require("./../../api/bargain.js")),o=require("./../../npm/wepy-redux/lib/index.js"),i=p(require("./../../store/utils.js")),s=p(require("./../zanui/quantity.js")),u=p(require("./../../utils/Sku.js")),c=p(require("./../../utils/Tips.js")),l=p(require("./../../utils/Event.js")),d=p(require("./../../utils/Cart.js")),f=p(require("./discount_badge.js"));function p(e){return e&&e.__esModule?e:{default:e}}function h(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(a,o){try{var i=t[a](o),s=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(s).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(s)}("next")}))}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var b=(0,o.connect)({status:i.default.get("status")})(e=function(e){function o(){var e,t,n;g(this,o);for(var i=arguments.length,u=Array(i),l=0;l<i;l++)u[l]=arguments[l];return t=n=y(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(u))),n.data={goods:{},source:null,action:"cart",display:!1},n.skuManager=null,n.cartManager=d.default.create(),n.methods={select:function(e,t){this.sku.disabledSkuValues[t]||this.skuManager.select(e,t)},close:function(){this.clear()},num:function(e){var t=e.quantity;this.skuManager.setNum(t)},confirm:function(e){var t=this;return h(regeneratorRuntime.mark((function n(){var o,i,s,u,l,d,f,p,h;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t.sku.next){n.next=2;break}return n.abrupt("return");case 2:if(o=t.sku,i=o.skuText,s=o.num,"cart"!==e){n.next=10;break}c.default.success("已加入购物车"),t.display=!1,t.$apply(),t.cartManager.plus(t.goods,i,s),n.next=24;break;case 10:if("begin"!==e&&"help"!==e){n.next=20;break}return n.next=13,a.default.GoodsBargain(t.goods.ruleId,t.goods.id,t.sku.skuText);case 13:return u=n.sent,n.next=16,c.default.success("砍价成功");case 16:!0,t.$root.$navigate("../bargain/bargain_detail?bargainId="+u.id+"&&isBegin="+!0),n.next=24;break;case 20:l=t.cartManager.createCart(t.goods,i,s),d=r.default.createCartTrade([l],{orderType:10}),"join"===e||"group"===e?(f=t.goods,p=f.groupId,h=f.ruleId,Object.assign(d,{ruleId:h,groupId:p}),t.$root.$preload("params",{trade:d,type:e})):t.$root.$preload("params",{trade:d,type:e}),t.$root.$navigate("../order/trade");case 24:t.clear();case 25:case"end":return n.stop()}}),n,t)})))()}},n.computed={sku:function(){if(null!=this.skuManager)return this.skuManager.export()},buttonText:function(){if(null!=this.goods&&null!=this.sku)return this.goods.totalStock<1?"已售罄":"cart"===this.action?"加入购物车":"buy"===this.action||"bargain"===this.action?"立即购买":"group"===this.action?"一键开团":"join"===this.action?"一键参团":"begin"===this.action?"发起砍价":void 0},quantity:function(){return null==this.sku?null:{num:this.sku.num,min:1,max:this.sku.stock}}},n.$repeat={},n.$props={ZanQuantity:{"v-bind:quantity.sync":"quantity","xmlns:v-on":""},DiscountBadge:{"xmlns:v-bind":"","v-bind:goods.sync":"goods"}},n.$events={ZanQuantity:{"v-on:change":"num"}},n.components={ZanQuantity:s.default,DiscountBadge:f.default},n.events={clear:function(){console.info("[SilderPanel] clear"),this.clear()}},y(n,t)}var i;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:(i=h(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.source=this.$root.$wxpage.route,l.default.listen(l.default.GOODS_PANEL_OPEN,this.open.bind(this),this);case 2:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"open",value:function(e){var t=e.goods,n=e.source,r=e.action;console.info("[slider] open goodId="+t.id+", listen_source="+this.source+" source="+n+", action="+r),n==this.source&&(this.goods=t,this.action=r,this.skuManager=new u.default(t),this.skuManager.action=r,this.display=!0,this.$apply())}},{key:"clear",value:function(){console.info("[SilderPanel] clear"),this.goods=null,this.action="cart",this.display=!1}}]),o}())||e;exports.default=b;