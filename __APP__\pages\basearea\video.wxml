<view>
    <view class="container" wx:if="{{isCheat}}">
        <web-view src="https://9adktmu5g.wasee.com/wt/9adktmu5g?def_sid=1965828"></web-view>
    </view>
    <view class="container" wx:else>
        <view class="video_box">
            <video controls autoplay="{{true}}" bindloadedmetadata="bindloadedmetadata" objectFit="cover" showCenterPlayBtn="{{false}}" src="{{url}}" title="{{title}}"></video>
        </view>
        <view class="blackboard" hidden="{{blackHidden}}">
            <view class="blacktext">加 载 中 ...</view>
        </view>
        <view class="bannerBox" wx:if="{{videoList}}">
            <swiper class="swiperBox" current="{{current}}" displayMultipleItems="{{displayMultipleItems}}" nextMargin="{{nextMargin}}" previousMargin="{{previousMargin}}">
                <swiper-item wx:for="{{videoList}}" wx:key="index">
                    <view bindtap="videoChange" class="swiper-item {{item.id==id?'currentItem':''}}" data-index="{{index}}">
                        <view class="imgBox">
                            <image mode="aspectFill" src="{{item.bgImage.url}}"></image>
                        </view>
                        <view class="titleBox">
                            <view>{{item.title}}</view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </view>
    </view>
</view>
