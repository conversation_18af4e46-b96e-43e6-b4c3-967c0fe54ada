var e=function(){function e(e,n){for(var a=0;a<n.length;a++){var c=n[a];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(e,c.key,c)}}return function(n,a,c){return a&&e(n.prototype,a),c&&e(n,c),n}}();function n(e,n,a){return n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function a(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}"function"==typeof SuppressedError&&SuppressedError;var c=["onShow","onHide","onReady","onLoad","onUnload"],t=["show","hide","created","attached","ready","moved","detached","error"],s="shuyun-track-is-first-day",r="shuyun-track-is-first-visit";function o(){var e=wx.getStorageSync(s);if(e){var n=e;return(new Date).getTime()<n.expiration}return function(){var e=new Date,n={value:!1,expiration:new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999).getTime()};wx.setStorageSync(s,n)}(),!0}function i(e){return"wifi"===e?"Wi-Fi":"2g"===e||"3g"===e||"4g"===e?"移动网络":"未知"}var p={connectionType:"",userCarrier:"",isWifi:!1};function u(e){var n="phone";return e.includes("windows")?n="PC":(e.includes("mac")||e.includes("ipad"))&&(n="pad"),n}function y(){var e=wx.getStorageSync("shuyun-track-id")||"";return e||(e=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,a="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),c=[],t=0;if(n=n||a.length,e)for(t=0;t<e;t++)c[t]=a[Math.floor(Math.random()*n)];else{var s=void 0;for(c[8]=c[13]=c[18]=c[23]="-",c[14]="4",t=0;t<36;t++)c[t]||(s=Math.floor(16*Math.random()),c[t]=a[19===t?3&s|8:s])}return c.join("")}(),wx.setStorageSync("shuyun-track-id",e)),e}function m(){var e=getCurrentPages();return e&&e.length?e[e.length-1].route:""}function d(){var e=getCurrentPages();return e&&e.length?e[e.length-1].data.title:""}function l(e){return["_MPLaunch","_MPAppHide","_MPAppShow","_MPPageview","_MPTap","_OrderSubmit"].includes(e)}function T(){var e=function(){var e=wx.getSystemInfoSync(),n=e.brand,a=e.model,c=e.system;return function(){var e;e=function(e){var n=i(e);p.connectionType=e,p.userCarrier=n,p.isWifi="Wi-Fi"===i(e)},wx.getNetworkType({success:function(n){e(n.networkType)},fail:function(n){console.error("获取网络类型失败：",n),e("未知")}})}(),{deviceBrand:n,deviceModel:a,deviceType:u(c),osName:c.split(" ")[0],osVersion:c,browserName:"",browserVersion:""}}(),n={projectId:"",deviceId:y(),loginId:"",timezoneOffset:(new Date).getTimezoneOffset()};return Object.assign(Object.assign({},e),n)}function f(){var e=function(){var e=m();return{pageUrl:e,pagePath:e.split("?")[0],pageTitle:d()||""}}(),n=o(),a="no"!==wx.getStorageSync(r)&&(wx.setStorageSync(r,"no"),!0),c=(new Date).toISOString(),t=wx.getSystemInfoSync(),s=t.windowWidth>t.windowHeight?"横屏":"竖屏";return Object.assign(Object.assign(Object.assign(Object.assign({},e),{isFirstDay:n,isFirstVisit:a,clientTime:c}),p),{screenOrientation:s})}var h,N,v=function(){function n(e,c){var t=this;a(this,n),this.trackInstance=e,this.report=Object.assign({},this.trackInstance.baseReport),this.setReportValue=function(e,n){t.report[e]=n},this.setEventCode=function(e){t.report.header.eventCode=e},this.setBurialSiteType=function(e){t.report.header.burialSiteType=e},this.setExtendData=function(e){t.report.record.extendData=e},this.setUserData=function(e){t.report.record.userData=e},this.resetReport=function(){t.report.header.eventCode="",t.report.header.burialSiteType="ALL_TRACK",t.report.record.extendData={},t.report.record.userData={}},this.appendGlobalData=function(e){Object.assign(t.report.record.globalData,e)},this.type=c}return e(n,[{key:"send",value:function(){return function(e,n,a,c){return new(a||(a=Promise))((function(t,s){function r(e){try{i(c.next(e))}catch(e){s(e)}}function o(e){try{i(c.throw(e))}catch(e){s(e)}}function i(e){var n;e.done?t(e.value):(n=e.value,n instanceof a?n:new a((function(e){e(n)}))).then(r,o)}i((c=c.apply(e,n||[])).next())}))}(this,void 0,void 0,regeneratorRuntime.mark((function e(){var n,a,c,t,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.trackInstance.config,a=n.reportUrl,c=n.ignore,!this.ignoreUrl((null==c?void 0:c.urls)||[])){e.next=3;break}return e.abrupt("return");case 3:t=f(),Object.assign(this.report.record.globalData,t),s=function(e){var n=JSON.parse(JSON.stringify(e));if(l(n.header.eventCode))for(var a in n.record.extendData)if(Object.prototype.hasOwnProperty.call(n.record.extendData,a)){var c="_"+a;n.record.extendData[c]=n.record.extendData[a],delete n.record.extendData[a]}for(var t in n.header)if(Object.prototype.hasOwnProperty.call(n.header,t)){var s="_"+t;n.header[s]=n.header[t],delete n.header[t]}for(var r in n.record.userData)if(Object.prototype.hasOwnProperty.call(n.record.userData,r)){var o="_"+r;n.record.userData[o]=n.record.userData[r],delete n.record.userData[r]}for(var i in n.record.globalData)if(Object.prototype.hasOwnProperty.call(n.record.globalData,i)){var p="_"+i;n.record.globalData[p]=n.record.globalData[i],delete n.record.globalData[i]}return n}(this.report),console.warn("reporter",s),wx.request({url:a,data:s,header:{"content-type":"application/json"},method:"POST"});case 7:case"end":return e.stop()}}),e,this)})))}},{key:"ignoreUrl",value:function(e){return e.some((function(e){return m().includes(e)}))}}]),n}();function g(e,n){var a,c,t,s=new v(e,"ua");try{var r=(null===(a=n[0])||void 0===a?void 0:a.currentTarget)||{},o=r.offsetLeft,i=r.offsetTop,p=r.x,u=r.y,y=r.dataset,m=((null===(c=n[0])||void 0===c?void 0:c._relatedInfo)||{}).anchorTargetText,d=(null===(t=n[0])||void 0===t?void 0:t.detail)||{},l=d.x,T=d.y;s.setExtendData(Object.assign({offsetLeft:o,offsetTop:i,x:p||l,y:u||T,content:m},y)),s.setEventCode("_MPTap"),s.setBurialSiteType("ALL_TRACK"),s.send(),s.resetReport()}catch(e){console.warn("tap事件上报异常",e)}}function b(e,n,a){e.setEventCode(n),e.setBurialSiteType(a),e.send(),e.resetReport()}!function(e){e.onLaunch="onLaunch",e.onError="onError",e.onUnhandledRejection="onUnhandledRejection",e.onPageNotFound="onPageNotFound",e.onHide="onHide",e.onShow="onShow",e.onShareAppMessage="onShareAppMessage"}(h||(h={})),function(e){e.onLoad="onLoad",e.onShow="onShow",e.onReady="onReady",e.onHide="onHide",e.onUnload="onUnload",e.created="created",e.attached="attached",e.ready="ready",e.moved="moved",e.detached="detached",e.error="error",e.show="show",e.hide="hide",e.eh="eh",e.tap="tap"}(N||(N={}));var w="",D=(new Date).getTime();function S(e){var n=new v(e,"");try{var a=m();w!==a&&(b(n,"_MPPageShow","ALL_TRACK"),w&&(n.setExtendData({stayTime:(new Date).getTime()-D,originPage:w}),b(n,"_pageview","ALL_TRACK")),D=(new Date).getTime(),w=a)}catch(e){console.warn("Page事件上报异常",e)}}function k(){return App}function C(){return Page}function _(){return Component}function O(e){App=e}function j(e){Page=e}function L(e){Component=e}var P=function(){function s(e){a(this,s),this.config=e,this.pageHooksCallbacks=[],this.appHooksCallbacks=[],this.nativeApp=k(),this.nativePage=C(),this.nativeComponent=_()}return e(s,[{key:"getMethods",value:function(e){var n=[],a=e.methods||e;for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&"function"==typeof a[c]&&n.push(c);return n}},{key:"click_proxy",value:function(e,n,a){if(n){var c=e.methods||e,t=c[n];c[n]=function(){for(var e=arguments.length,n=Array(e),c=0;c<e;c++)n[c]=arguments[c];var s=t.apply(this,n);return a(n),s}}}},{key:"decorateComponent",value:function(e,a){var s=_(),r=C();this.pageHooksCallbacks.push(e);var o=this,i=a?c:t,p=function(e){i.forEach((function(a){var c=e[a];e&&e.pageLifetimes&&(a===N.show||a===N.hide)&&(e.__trackUsePageLifetime__=!0,c=e.pageLifetimes[a]);var t=function(){for(var n,t=arguments.length,s=Array(t),r=0;r<t;r++)s[r]=arguments[r];return e.__callbackResult__=s,o.pageHooksCallbacks.forEach((function(n){return n(a,e)})),c&&(n=c).call.apply(n,[this].concat(s))};e=e&&e.pageLifetimes&&(a===N.show||a===N.hide)?Object.assign(Object.assign({},e),{pageLifetimes:Object.assign(Object.assign({},e.pageLifetimes),n({},a,t))}):Object.assign(Object.assign({},e),n({},a,t))})),a?r(e):s(e)};a?j(p):L(p)}},{key:"decorateApp",value:function(e){var a=k();this.appHooksCallbacks.push(e);var c=this;O((function(e){["onError","onUnhandledRejection","onPageNotFound","onLaunch","onHide","onShow","onShareAppMessage"].forEach((function(a){var t=e[a];e=Object.assign(Object.assign({},e),n({},a,(function(){for(var n=arguments.length,s=Array(n),r=0;r<n;r++)s[r]=arguments[r];return e.__callbackResult__=s,c.appHooksCallbacks.forEach((function(n){return n(a,e)})),t&&t.call.apply(t,[this].concat(s))})))})),a(e)}))}},{key:"decorateTap",value:function(e,n){var a=_(),c=C(),t=this,s=function(s){for(var r=t.getMethods(s),o=0;o<=r.length;o++)t.click_proxy(s,r[o],e);n?c(s):a(s)};n?j(s):L(s)}},{key:"destroy",value:function(){O(this.nativeApp),j(this.nativePage),L(this.nativeComponent)}}]),s}();module.exports=function(){function c(){var e=this;a(this,c),this.config={},this.setGlobalReport=function(a,c){a&&Object.assign(e.baseReport,n({},a,c))};var t=T();this.baseReport={header:{projectCode:"",eventCode:"",burialSiteType:"ALL_TRACK",platformType:"WECHAT_APPLET",platformVersion:"2.2.5",appid:"",platformLanguage:"JS"},record:{globalData:Object.assign({},t),userData:{},extendData:{}}}}return e(c,[{key:"init",value:function(e){var n;"undefined"!=typeof wx?e&&e.enable?e.reportUrl?e.projectID?(this.baseReport.header.projectCode=e.projectID,this.baseReport.header.appid=e.appid,Object.assign(this.config,e),this.hooksDecorator=new P(this.config),function(e){var n=!1,a=new v(e,"onLaunch");e.hooksDecorator.decorateApp((function(e,c){var t=c.__callbackResult__&&c.__callbackResult__[0];if(e===h.onLaunch&&!n){n=!0;var s=function(e){var n=e.scene,a=e.referrerInfo,c=e.query,t=null==a?void 0:a.extraData,s=function(e){var n={1181:{sceneType:"pc端",sceneName:"网站应用打开PC小程序"},1260:{sceneType:"pc端",sceneName:"pc端小程序面板「推荐在电脑端使用」列表"},1258:{sceneType:"pc端",sceneName:"pc端小程序面板「为电脑端优化」模块"},1257:{sceneType:"pc端",sceneName:"pc端小程序面板「我的小程序」列表"},1259:{sceneType:"pc端",sceneName:"pc端小程序面板「小游戏专区」模块"},1256:{sceneType:"pc端",sceneName:"pc端小程序面板「最近使用」列表"},1183:{sceneType:"pc端",sceneName:"PC微信-小程序面板-发现小程序-搜索"},1078:{sceneType:"Wi-Fi",sceneName:"微信连Wi-Fi成功提示页"},1064:{sceneType:"Wi-Fi",sceneName:"微信首页连Wi-Fi状态栏"},1114:{sceneType:"安卓手机",sceneName:"安卓手机侧边栏，打开小程序（三星）"},1113:{sceneType:"安卓手机",sceneName:"安卓手机负一屏，打开小程序（三星）"},1023:{sceneType:"安卓手机",sceneName:"安卓系统桌面图标"},1223:{sceneType:"安卓手机",sceneName:"安卓桌面Widget打开小程序"},1129:{sceneType:"调试或测试",sceneName:"微信爬虫访问详情"},1203:{sceneType:"调试或测试",sceneName:"微信小程序压测工具的请求"},1101:{sceneType:"调试或测试",sceneName:"远程调试热更新"},1030:{sceneType:"调试或测试",sceneName:"自动化测试下打开小程序"},1230:{sceneType:"订阅号",sceneName:"订阅号H5广告进入小程序"},1152:{sceneType:"订阅号",sceneName:"订阅号视频打开小程序"},1107:{sceneType:"订阅消息",sceneName:"订阅消息，打开小程序"},1014:{sceneType:"订阅消息",sceneName:"小程序订阅消息"},1145:{sceneType:"发现栏",sceneName:"发现栏-发现小程序"},1151:{sceneType:"发现栏",sceneName:"发现栏-我的订单"},1026:{sceneType:"发现栏",sceneName:"发现栏小程序主入口，「附近的小程序」列表"},1169:{sceneType:"发现栏",sceneName:"发现栏小程序主入口，各个生活服务入口"},1006:{sceneType:"发现栏",sceneName:"发现栏小程序主入口搜索框的搜索结果页"},1273:{sceneType:"发现页",sceneName:"发现页「常用的小程序」列表"},1254:{sceneType:"发现页",sceneName:"发现页「动态」卡片打开小程序"},1272:{sceneType:"发现页",sceneName:"发现页「游戏」服务tab打开小程序"},1103:{sceneType:"发现页",sceneName:"发现页小程序「我的小程序」列表"},1001:{sceneType:"发现页",sceneName:"发现页小程序「最近使用」列表"},1242:{sceneType:"发现页",sceneName:"小程序发现页门店快送模块频道页进入小程序"},1245:{sceneType:"发现页",sceneName:"小程序发现页门店快送搜索结果页进入小程序"},1187:{sceneType:"浮窗",sceneName:"浮窗"},1131:{sceneType:"浮窗",sceneName:"浮窗"},1157:{sceneType:"公众号",sceneName:"服务号会话页打开小程序"},1102:{sceneType:"公众号",sceneName:"公众号profile页服务预览"},1020:{sceneType:"公众号",sceneName:"公众号profile页相关小程序列表"},1082:{sceneType:"公众号",sceneName:"公众号会话下发的文字链"},1074:{sceneType:"公众号",sceneName:"公众号会话下发的小程序消息卡片"},1043:{sceneType:"公众号",sceneName:"公众号模板消息"},1058:{sceneType:"公众号",sceneName:"公众号文章"},1144:{sceneType:"公众号",sceneName:"公众号文章-视频贴片"},1067:{sceneType:"公众号",sceneName:"公众号文章广告"},1091:{sceneType:"公众号",sceneName:"公众号文章商品卡片"},1035:{sceneType:"公众号",sceneName:"公众号自定义菜单"},1189:{sceneType:"广告",sceneName:"表情雨广告"},1215:{sceneType:"广告",sceneName:"广告预约打开小程序"},1095:{sceneType:"广告",sceneName:"小程序广告组件"},1068:{sceneType:"广告",sceneName:"附近小程序列表广告"},1100:{sceneType:"其他",sceneName:"红包封面详情页打开小程序"},1148:{sceneType:"卡包",sceneName:"卡包-交通卡，打开小程序"},1028:{sceneType:"卡包",sceneName:"我的卡包"},1104:{sceneType:"快捷入口",sceneName:"微信聊天主界面下拉，「我的小程序」栏"},1089:{sceneType:"快捷入口",sceneName:"微信聊天主界面下拉，「最近使用」栏"},1090:{sceneType:"快捷入口",sceneName:"长按小程序右上角菜单唤出最近使用历史"},1208:{sceneType:"聊天",sceneName:"聊天打开商品卡片"},1056:{sceneType:"聊天",sceneName:"聊天顶部音乐播放器右上角菜单"},1022:{sceneType:"聊天",sceneName:"聊天顶部置顶小程序入口"},1096:{sceneType:"聊天",sceneName:"聊天记录，打开小程序"},1173:{sceneType:"聊天",sceneName:"聊天素材用小程序打开详情"},1106:{sceneType:"聊天",sceneName:"聊天主界面下拉，从顶部搜索结果页，打开小程序"},1045:{sceneType:"朋友圈",sceneName:"朋友圈广告"},1046:{sceneType:"朋友圈",sceneName:"朋友圈广告详情页"},1084:{sceneType:"朋友圈",sceneName:"朋友圈广告原生页"},1154:{sceneType:"朋友圈",sceneName:"朋友圈内打开“单页模式”"},1e3:{sceneType:"其他",sceneName:"其他"},1017:{sceneType:"其他",sceneName:"前往小程序体验版的入口页"},1212:{sceneType:"其他",sceneName:"青少年模式申请页打开小程序"},1248:{sceneType:"其他",sceneName:"通过小程序账号迁移进入小程序"},1179:{sceneType:"其他",sceneName:"话题页打开小程序"},1155:{sceneType:"其他",sceneName:"“单页模式”打开小程序"},1153:{sceneType:"其他",sceneName:"“识物”结果页打开小程序"},1167:{sceneType:"外部渠道",sceneName:"H5通过开放标签打开小程序详情"},1194:{sceneType:"其他",sceneName:"URLLink详情"},1065:{sceneType:"其他",sceneName:"URLscheme详情"},1092:{sceneType:"其他",sceneName:"城市服务入口"},1146:{sceneType:"其他",sceneName:"地理位置信息打开出行类小程序"},1231:{sceneType:"其他",sceneName:"动态消息提醒入口打开小程序"},1196:{sceneType:"其他",sceneName:"个人状态打开小程序"},1088:{sceneType:"其他",sceneName:"会话中查看系统消息，打开小程序"},1052:{sceneType:"其他",sceneName:"卡券的适用门店列表"},1081:{sceneType:"其他",sceneName:"客服消息下发的文字链"},1079:{sceneType:"其他",sceneName:"微信游戏中心"},1171:{sceneType:"其他",sceneName:"微信运动记录"},1029:{sceneType:"其他",sceneName:"小程序中的卡券详情页"},1099:{sceneType:"其他",sceneName:"页面内嵌插件"},1202:{sceneType:"企业微信",sceneName:"企微客服号会话打开小程序卡片"},1207:{sceneType:"企业微信",sceneName:"企微客服号会话打开小程序文字链"},1192:{sceneType:"企业微信",sceneName:"企微客服号会话打开小程序文字链"},1120:{sceneType:"企业微信",sceneName:"【企业微信】个人资料页内打开小程序"},1119:{sceneType:"企业微信",sceneName:"【企业微信】工作台内打开小程序"},1121:{sceneType:"企业微信",sceneName:"【企业微信】聊天加号附件框内打开小程序"},1071:{sceneType:"钱包",sceneName:"钱包中的银行卡列表页"},1057:{sceneType:"钱包",sceneName:"钱包中的银行卡详情页"},1160:{sceneType:"群工具",sceneName:"群待办"},1158:{sceneType:"群工具",sceneName:"群工具打开小程序"},1185:{sceneType:"群工具",sceneName:"群公告"},1124:{sceneType:"扫码",sceneName:"扫“一物一码”打开小程序"},1011:{sceneType:"扫码",sceneName:"扫描二维码"},1126:{sceneType:"扫码",sceneName:"扫描手机相册中选取的“一物一码”"},1013:{sceneType:"扫码",sceneName:"扫描手机相册中选取的二维码"},1049:{sceneType:"扫码",sceneName:"扫描手机相册中选取的小程序码"},1032:{sceneType:"扫码",sceneName:"扫描手机相册中选取的一维码"},1047:{sceneType:"扫码",sceneName:"扫描小程序码"},1025:{sceneType:"扫码",sceneName:"扫描一维码"},1150:{sceneType:"扫码",sceneName:"扫一扫商品条码结果页打开小程序"},1125:{sceneType:"扫码",sceneName:"长按图片识别“一物一码”"},1012:{sceneType:"扫码",sceneName:"长按图片识别二维码"},1048:{sceneType:"扫码",sceneName:"长按图片识别小程序码"},1031:{sceneType:"扫码",sceneName:"长按图片识别一维码"},1216:{sceneType:"视频号",sceneName:"视频号订单中心打开小程序"},1200:{sceneType:"视频号",sceneName:"视频号广告打开小程序"},1201:{sceneType:"视频号",sceneName:"视频号广告详情页打开小程序"},1191:{sceneType:"视频号",sceneName:"视频号活动"},1198:{sceneType:"视频号",sceneName:"视频号开播界面打开小游戏"},1184:{sceneType:"视频号",sceneName:"视频号链接打开小程序"},1206:{sceneType:"视频号",sceneName:"视频号小游戏直播间打开小游戏"},1228:{sceneType:"视频号",sceneName:"视频号原生广告组件打开小程序"},1176:{sceneType:"视频号",sceneName:"视频号直播间主播打开小程序"},1177:{sceneType:"视频号",sceneName:"视频号直播商品"},1197:{sceneType:"视频号",sceneName:"视频号主播从直播间返回小游戏"},1193:{sceneType:"视频号",sceneName:"视频号主页服务菜单打开小程序"},1175:{sceneType:"视频号",sceneName:"视频号主页商店入口"},1195:{sceneType:"视频号",sceneName:"视频号主页商品tab"},1186:{sceneType:"收藏",sceneName:"收藏-笔记"},1010:{sceneType:"收藏",sceneName:"收藏夹"},1005:{sceneType:"搜索",sceneName:"微信首页顶部搜索框的搜索结果页"},1027:{sceneType:"搜索",sceneName:"微信首页顶部搜索框搜索结果页「使用过的小程序」列表"},1053:{sceneType:"搜索",sceneName:"搜一搜的结果页"},1252:{sceneType:"搜索",sceneName:"搜一搜小程序搜索页「小功能」模块进入小程序"},1042:{sceneType:"搜索",sceneName:"添加好友搜索框的搜索结果页"},1054:{sceneType:"搜索",sceneName:"顶部搜索框小程序快捷入口"},1059:{sceneType:"体验版",sceneName:"体验版小程序绑定邀请页"},1168:{sceneType:"外部渠道",sceneName:"移动/网站应用直接运行小程序"},1069:{sceneType:"外部渠道",sceneName:"移动应用通过openSDK进入微信，打开小程序"},1133:{sceneType:"外部渠道",sceneName:"硬件设备打开小程序详情"},1178:{sceneType:"外部渠道",sceneName:"在电脑打开手机上打开的小程序"},1038:{sceneType:"外部渠道",sceneName:"从另一个小程序返回"},1037:{sceneType:"外部渠道",sceneName:"小程序打开小程序"},1019:{sceneType:"微信钱包",sceneName:"微信钱包"},1072:{sceneType:"微信支付",sceneName:"二维码收款页面"},1097:{sceneType:"微信支付",sceneName:"微信支付签约原生页，打开小程序"},1034:{sceneType:"微信支付",sceneName:"微信支付完成页"},1060:{sceneType:"微信支付",sceneName:"微信支付完成页"},1036:{sceneType:"消息卡片",sceneName:"App分享消息卡片"},1044:{sceneType:"消息卡片",sceneName:"带shareTicket的小程序消息卡片详情"},1007:{sceneType:"消息卡片",sceneName:"单人聊天会话中的小程序消息卡片"},1073:{sceneType:"消息卡片",sceneName:"客服消息列表下发的小程序消息卡片"},1008:{sceneType:"消息卡片",sceneName:"群聊会话中的小程序消息卡片"},1024:{sceneType:"小程序列表页",sceneName:"小程序profile页"},1135:{sceneType:"小程序列表页",sceneName:"小程序profile页相关小程序列表，打开小程序"},1039:{sceneType:"摇一摇",sceneName:"摇电视"},1077:{sceneType:"摇一摇",sceneName:"摇周边"}};return e in n?n[e]:{sceneType:"",sceneName:""}}(n);return{scene:n,sceneName:s.sceneName,sceneType:s.sceneType,utmCampaign:(null==t?void 0:t.utmCampaign)||(null==c?void 0:c.utmCampaign)||"",utmContent:(null==t?void 0:t.utmContent)||(null==c?void 0:c.utmContent)||"",utmMedium:(null==t?void 0:t.utmMedium)||(null==c?void 0:c.utmMedium)||"",utmSource:(null==t?void 0:t.utmSource)||(null==c?void 0:c.utmSource)||"",utmTerm:(null==t?void 0:t.utmTerm)||(null==c?void 0:c.utmTerm)||""}}(t);a.appendGlobalData(s),b(a,"_MPLaunch","ALL_TRACK")}e===h.onHide&&b(a,"_MPAppHide","ALL_TRACK"),e===h.onShow&&b(a,"_MPAppShow","ALL_TRACK")}))}(this),(n=this).hooksDecorator.decorateComponent((function(e){e!==N.show&&e!==N.created||S(n)})),n.hooksDecorator.decorateComponent((function(e){e===N.onShow&&S(n)}),!0),function(e){e.hooksDecorator.decorateTap((function(n){var a;"tap"===(null===(a=n[0])||void 0===a?void 0:a.type)&&g(e,n)})),e.hooksDecorator.decorateTap((function(n){var a;"tap"===(null===(a=n[0])||void 0===a?void 0:a.type)&&g(e,n)}),!0)}(this)):console.warn("缺少项目ID或token!"):console.warn("缺少上报地址!"):console.warn("未开启日志收集"):console.warn("非小程序运行环境")}},{key:"setUserInfo",value:function(e){var n=new v(this,"setUserInfo");e.loginId&&n.appendGlobalData({loginId:e.loginId}),n.setUserData(Object.assign({deviceId:y()},e))}},{key:"sendOrderInfo",value:function(e){var n=new v(this,"OrderSubmit");n.setEventCode("_OrderSubmit"),n.setBurialSiteType("CODE_TRACK"),n.setExtendData(e),n.send(),n.resetReport()}},{key:"sendCustomEvent",value:function(e,n){if(l(e))console.error("自定义的事件Key，不能和预置事件相同");else{var a=new v(this,e);a.setEventCode(e),a.setBurialSiteType("CODE_TRACK"),a.setExtendData(n),a.send(),a.resetReport()}}}]),c}();