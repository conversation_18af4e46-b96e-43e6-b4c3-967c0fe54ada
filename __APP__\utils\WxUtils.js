Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=s(require("./Tips.js")),i=s(require("./../npm/wepy/lib/wepy.js"));function s(e){return e&&e.__esModule?e:{default:e}}var o=(t=e=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return r(e,null,[{key:"isTab",value:function(e){return 1==i.default.$instance.globalData.shopType&&this.tabUrls.some((function(t){return t==e}))}},{key:"mapUrl",value:function(e){return 1==i.default.$instance.globalData.shopType&&this.mapUrls[e]?this.mapUrls[e]:e}},{key:"backOrRedirect",value:function(e){var t=this;if(e=this.mapUrl(e),this.isTab(e))wx.switchTab({url:e});else{var a=getCurrentPages(),r=a.findIndex((function(a){var r="/"+a.__route__;return"{}"!=JSON.stringify(a.options)&&(r+="?"+t.httpBuildQuery(a.options)),r==e}));if(a.length<2||r<0)wx.redirectTo({url:e});else{var n=a.length-1-r;wx.navigateBack({delta:n})}}}},{key:"backOrNavigate",value:function(e){var t=this;if(e=this.mapUrl(e),this.isTab(e))wx.switchTab({url:e});else{var a=getCurrentPages();console.log("getCurrentPages",a);var r=a.findIndex((function(a){var r="/"+a.__route__;return"{}"!=JSON.stringify(a.options)&&(r+="?"+t.httpBuildQuery(a.options)),r==e}));if(a.length<2||r<0)console.log("backOrNavigate:navigateTo",e),wx.navigateTo({url:e});else{var n=a.length-1-r;console.log("backOrNavigate:navigateBack",n),wx.navigateBack({delta:n})}}}},{key:"wxPay",value:function(e){return new Promise((function(t,r){wx.requestPayment(a({},e,{complete:function(e){"requestPayment:ok"==e.errMsg?t(e):r(e)}}))}))}},{key:"canIUse",value:function(e){return!!wx.canIUse&&wx.canIUse(e)}},{key:"isSDKExipred",value:function(){var e=wx.getSystemInfoSync().SDKVersion;return console.info("[version]sdk "+e),null==e||e<"1.2.0"}},{key:"checkSDK",value:function(){this.isSDKExipred()&&n.default.modal("您的微信版本太低，为确保正常使用，请尽快升级")}},{key:"parseUrl",value:function(e){for(var t=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e),a=["url","scheme","slash","host","port","path","query","hash"],r={},n=0;n<a.length;n++){var i=a[n],s=t[n];r[i]=s}return r}},{key:"getUrlkey",value:function(e){for(var t={},a=e.split("?"),r=a[1]&&a[1].split("&")||e.split("&"),n=0,i=r.length;n<i;n++){var s=r[n].split("=");t[s[0]]=s[1]}return t}},{key:"httpBuildQuery",value:function(e){var t="";for(var a in e)t+=a+"="+e[a]+"&";return t.slice(0,-1)}},{key:"isPhone",value:function(e){return!!/^1(3|4|5|7|8)\d{9}$/.test(e)}},{key:"isSixNum",value:function(e){return!!/^\d{6}$/.test(e)}},{key:"isCard",value:function(e){return!!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(e)}},{key:"IdentityIDCard",value:function(e){var t="",a=!0;if(e&&/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(e))if({11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江 ",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北 ",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏 ",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外 "}[e.substr(0,2)]){if(18==e.length){e=e.split("");for(var r=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],n=[1,0,"X",9,8,7,6,5,4,3,2],i=0,s=0;s<17;s++)i+=e[s]*r[s];n[i%11]!=e[17]&&(t="您输入的身份证号不存在！",a=!1)}}else t="您输入的身份证地址编码有误！",a=!1;else t="您输入的身份证号格式有误！",a=!1;return{errorMess:t,isPass:a}}},{key:"GetDistance",value:function(e,t,a,r){var n=e*Math.PI/180,i=a*Math.PI/180,s=n-i,o=t*Math.PI/180-r*Math.PI/180,u=2*Math.asin(Math.sqrt(Math.pow(Math.sin(s/2),2)+Math.cos(n)*Math.cos(i)*Math.pow(Math.sin(o/2),2)));return u*=6378.137,u=Math.round(1e4*u)/1e4}}]),e}(),e.tabUrls=["/pages/home/<USER>","/pages/home/<USER>","/pages/home/<USER>","/pages/home/<USER>","/pages/home/<USER>"],e.mapUrls={"/pages/home/<USER>":"/pages/home/<USER>","/pages/home/<USER>":"/pages/home/<USER>"},t);exports.default=o;