Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var o=0;o<t.length;o++){var a=t[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,o,a){return o&&e(t.prototype,o),a&&e(t,a),t}}(),t=s(require("./../npm/wepy/lib/wepy.js")),o=s(require("./../utils/Event.js")),a=s(require("./../utils/Tips.js")),r=s(require("./../api/goods.js"));function s(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(s){function u(){var e,o,r;i(this,u);for(var s=arguments.length,c=Array(s),l=0;l<s;l++)c[l]=arguments[l];return o=r=n(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(c))),r.config={},r.components={},r.methods={routeToCart:function(){this.routeToCart()},routeToHome:function(){this.routeToHome()},routeToCustomer:function(){this.roureCustomer()},routeToIndex:function(){this.routeToIndex()},handleMinusCart:function(e){this.minusCart(e)},handlePlusCart:function(e){this.plusCart(e)},handleBuy:function(e){this.buy(e)},handleGroup:function(e,t,o,a,r){this.group(e,t,o,a,r)},handleBargain:function(e,t,o,a){this.bargain(e,t,o,a)},routeToGoods:function(e){this.$root.$navigate("/pages/goods/detail?goodsId="+e)},routeByAction:function(e){var o=e.action,r=e.targetId;switch(console.info("[router] handle action="+o+", targetId="+r),o){case"FUNC":case"NONE":break;case"NO_OPEN":a.default.success(null!=r?r:"敬请期待");break;case"DEBUG":console.info("[router] debug action",e);break;case"PAGE_NAVIGATE":this.$root.$navigate(r);break;case"PAGE_REDIRECT":this.$root.$redirect(r);break;case"PAGE_SWITCH":this.$root.$switch(r);break;case"PAGE_BACK":t.default.navigateBack();break;case"GOODS":this.$root.$navigate("/pages/goods/detail?goodsId="+r);break;case"CATEGORY":this.$root.$navigate("/pages/goods/search_list?categoryId="+r);break;case"HOME":this.routeToHome();break;case"USER":this.$root.$switch("/pages/customer/index_template");break;case"GOODS_ALL":this.routeToIndex();break;case"GOODS_SEARCH":var s=r.categoryId,i=r.sort,n=r.by,u=r.content,c="/pages/goods/search_list?";null!=s&&(c+="&categoryId="+s),null!=i&&(c+="&sort="+i),null!=n&&(c+="&by="+n),null!=u&&(c+="&keyword="+u),this.$root.$navigate(c);break;case"MEMBER_SIGN":this.$root.$navigate("/pages/customer/sign_in");break;case"COUPON_OWN":this.$root.$navigate("/pages/coupon/list");break;case"COUPON_PICK":this.$root.$navigate("/pages/coupon/pick");break;case"FAVORITE":this.$root.$navigate("/pages/goods/favorite");break;case"ADDRESS":this.$root.$navigate("/pages/customer/address_list");break;case"COMMENT_LIST":this.$root.$navigate("/pages/customer/comment_list");break;case"MEMBER_DETAIL":this.$root.$navigate("/pages/customer/vip_detail");break;case"MEMBER_CODE":this.$root.$navigate("/pages/customer/vip_info");break;case"MEMBER_BONUS":this.$root.$navigate("/pages/customer/bonus_list");break;case"TOP_UP":this.$root.$navigate("/pages/customer/balance");break;case"ORDER_LIST":this.$root.$navigate("/pages/order/list");break;case"PHONE":t.default.makePhoneCall({phoneNumber:""+r});break;case"SHARE":t.default.showShareMenu();break;case"MEMBER_REGIST":this.routeToCustomer();break;case"OFFLINE_PAYMENT":this.$root.$navigate("/pages/shop/pay");break;case"TEMPLATE":this.$root.$navigate("/pages/home/<USER>"+r);break;case"SHOP_INFO":this.$root.$navigate("/pages/shop/about_us");break;default:console.info("[router] unsupport action="+o+", targetId="+r)}}},r.events={},n(r,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.mixin),e(u,[{key:"buy",value:function(e){var t=this.$root.$wxpage.route;o.default.emit(o.default.GOODS_PANEL_OPEN,{goods:e,source:t,action:"buy"})}},{key:"group",value:function(e,t,a,s,i){(e.ruleId=a,e.groupId=i,e.goodsSkuInfo&&t)&&e.goodsSkuInfo.goodsSkuDetails.forEach((function(o){var a=o.sku,s=o.goodsSkuDetailBase,i=t.find((function(e){return e.sku===a}));i?s.price=i.price:console.warn("[group] can not find group price, sku="+a),r.default._processGoodsPriceRange(e),r.default._processGoodsPriceLabel(e)}));var n=this.$root.$wxpage.route,u=s;o.default.emit(o.default.GOODS_PANEL_OPEN,{goods:e,source:n,action:u})}},{key:"bargain",value:function(e,t,a,r){e.ruleId=t,e.id=r;var s=this.$root.$wxpage.route,i=a;o.default.emit(o.default.GOODS_PANEL_OPEN,{goods:e,source:s,action:i})}},{key:"plusCart",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=this.$root.$wxpage.route,r=this.getMode(e),s="cart";switch(console.info("[router] goodId="+e.id+",source="+a+", action="+s),r){case"SLIDER":o.default.emit(o.default.GOODS_PANEL_OPEN,{goods:e,source:a,action:s});break;case"POPUP":o.default.emit(o.default.GOODS_PANEL_PLUS,{goods:e,goodsSku:t,source:a});break;default:o.default.emit(o.default.GOODS_PANEL_PLUS,{goods:e,goodsSku:t,source:a})}}},{key:"minusCart",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=this.$root.$wxpage.route;console.info("[router] goodId="+e+",source="+a),o.default.emit(o.default.GOODS_PANEL_MINUS,{goodsId:e,goodsSku:t,source:a})}},{key:"getMode",value:function(e){if(e.skuMode)return e.skuMode;try{return this.$root.$data.buyPanelType}catch(e){return null}}},{key:"isTab",value:function(){return 1==t.default.$instance.globalData.shopType}},{key:"routeToCart",value:function(){this.$root.$switch("/pages/goods/cart")}},{key:"routeToIndex",value:function(){this.isTab()?this.$root.$switch("/pages/goods/category"):this.$root.$navigate("/pages/shop/index")}},{key:"routeToHome",value:function(){this.isTab()?this.$root.$switch("/pages/home/<USER>"):this.$root.$navigate("/pages/home/<USER>")}},{key:"routeToCustomer",value:function(){this.isTab()?this.$root.$switch("/pages/customer/index"):this.$root.$navigate("/pages/customer/index")}}]),u}();exports.default=u;