<view wx:if="{{init}}">
    <view class="container">
        <view class="wechatapp">
            <view class="header">
                <open-data class="" type="userAvatarUrl"></open-data>
            </view>
            <view>
                <open-data class="" type="userNickName"></open-data>
            </view>
        </view>
        <view class="auth-title">申请获取以下权限</view>
        <view class="auth-subtitle">获得您的公开信息（昵称、头像等）</view>
        <button bindgetuserinfo="confirm" class="login-btn" openType="getUserInfo" type="primary" wx:if="{{reInfo}}">确认授权</button>
        <button bindtap="tips" class="weui-btn" type="primary" wx:else>确认授权</button>
    </view>
    <Copyright></Copyright>
</view>
