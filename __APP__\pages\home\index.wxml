<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view wx:if="{{init}}">
    <swiper autoplay indicatorActiveColor="#EEE" indicatorDots="true" interval="3000" style="height:{{$SwiperBar$param.height}}">
        <swiper-item bindtap="$SwiperBar$gotoWithLogined" class="swiper-box" data-id="2" data-url="{{item.out_site_link}}" wx:if="{{item.type==3}}" wx:for="{{$SwiperBar$param.data}}" wx:key="indexs">
            <image mode="aspectFill" src="{{item.pic_url}}"></image>
        </swiper-item>
    </swiper>
    <view class="fangweisb">
        <view class="fangweisb_bj">
            <view class="fangweisb_sub01">{{queryResult}}</view>
            <view class="fangweisb_sub02" wx:if="{{queryResult=='真'}}">
                <view class="xwbiaoti">该产品为真品，请放心饮用！</view>
                <view class="xixi">
                    <view style="float:left;">如有疑问，请点击<button bindtap="goToValidate" class="validate-btn" type="primary">进一步验证</button>
                    </view>
                </view>
            </view>
            <view class="fangweisb_sub02" wx:if="{{queryResult=='慎'}}">
                <view class="xixi">
                    <view>该产品已被扫描查询{{productInfo.queryTimes}}次；</view>
                    <view>最后查询时间为：{{productInfo.lastQueryDate}}</view>
                    <view style="float:left;">如有疑问，请点击<button bindtap="goToValidate" class="validate-btn" type="primary">进一步验证</button>
                    </view>
                </view>
            </view>
            <view class="fangweisb_sub02" wx:if="{{productInfo.productName&&productInfo.queryTimes>50}}">
                <view class="xixi">
                    <view style="float:left;">该产品已超过正常查询次数，可能涉嫌假冒，请点击<button bindtap="goToValidate" class="validate-btn" type="primary">进一步验证</button>
                    </view>
                </view>
            </view>
            <view class="fangweisb_sub02" wx:if="{{!productInfo.productName}}">
                <view class="xixi">
                    <view>
                        <view>您购买的产品为假冒伪劣产品！</view>
                        <view>二维码为：{{qrCode}}</view>
                        <view>举报电话：400-667-1988</view>
                        <view>也可点下方“举报咨询”举报！</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="changpin" wx:if="{{productInfo.productName}}">
        <view class="changpin_sub01">
            <image src="{{productInfo.productImage}}"></image>
        </view>
        <view class="changpin_sub02">
            <view style="font-size:22rpx;float:left">名称：{{productInfo.productName}}</view>
            <view style="font-size:22rpx;float:left" wx:if="{{!productInfo.productQrcodeLastSix}}">酒盒防伪码：{{productInfo.productCode}}XXXX</view>
            <view style="font-size:22rpx;float:left">生产日期：{{productInfo.productionDate}}</view>
            <view style="font-size:22rpx;float:left" wx:if="{{productInfo.productQrcodeLastSix}}">
                <text style="font-size:22rpx;">箱内酒盒防伪码后6位：</text>
                <view>
                    <text class="product-qrcode-last-six" style="font-size:22rpx;" wx:for="{{productInfo.productQrcodeLastSix}}" wx:key="id">{{item}}</text>
                </view>
            </view>
        </view>
    </view>
    <view class="button-sp-area">
        <button plain bindtap="goto" class="goodsdetail" data-url="/pages/home/<USER>" formType="submit" type="primary">
            <icon class="category"></icon>产品详情</button>
        <button bindtap="goToYouZanShop" class="right" plain="true" type="primary">
            <icon class="cart"></icon>网上商城</button>
        <button bindtap="submits" class="" plain="true" type="primary">
            <icon class="wechart"></icon>关注习酒</button>
        <navigator url="../home/<USER>">
            <button class="right" plain="true" type="primary">
                <icon class="question"></icon>查询指南</button>
        </navigator>
        <button plain bindtap="goto" class="" data-url="/pages/home/<USER>" formType="submit" type="primary">
            <icon class="evaluated"></icon>举报咨询</button>
        <button plain class="right" formType="submit" type="primary">
            <icon class="inquire-store"></icon>个人中心</button>
        <view catchtouchmove="preventTouchMove" class="mask" wx:if="{{showModal}}"></view>
        <view class="modalDlg" wx:if="{{showModal}}" wx:for="{{imgalist}}" wx:for-item="image" wx:key="item">
            <text>扫描二维码关注习酒</text>
            <image bindtap="previewImage" data-src="{{image}}" src="{{image}}"></image>
            <button bindtap="gos">点我关闭</button>
        </view>
    </view>
    <view bindtap="$Copyright$tap" class="weui-footer {{$Copyright$buttom=='true'?'weui-footer_fixed-bottom':''}} ">
        <view class="weui-footer__links">
            <view class="weui-footer__text">版权所有 ©贵州习酒股份有限公司</view>
            <view class="weui-footer__text">服务热线：400-667-1988</view>
        </view>
    </view>
</view>
