/**
 * 简化版会话保活器
 * 只解决需要手动进入页面的问题，不包含其他功能
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class SimpleSessionKeeper {
    constructor() {
        // Token配置
        this.authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // API配置
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api'
        ];
        
        this.appId = 'wx489f950decfeb93e';
        this.memberInfo = this.decodeJWT(this.authToken);
        this.memberId = this.memberInfo.memberInfo.id;
        
        // 会话状态
        this.heartbeatInterval = null;
        this.isRunning = false;
        
        console.log('🔧 简化会话保活器初始化完成');
        console.log('👤 会员ID:', this.memberId);
        console.log('🎯 目标: 解决手动进入页面问题');
    }

    /**
     * 解析JWT Token
     */
    decodeJWT(token) {
        try {
            const parts = token.split('.');
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            return payload;
        } catch (e) {
            return null;
        }
    }

    /**
     * 构建请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'Referer': `https://servicewechat.com/${this.appId}/devtools/page-frame.html`,
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Authorization': this.authToken,
            'login_code': this.loginCode,
            'X-WX-AppId': this.appId,
            'X-WX-Version': 'v3.2.6',
            'X-WX-Platform': 'ios'
        };
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        const headers = this.buildHeaders();
        
        // 尝试所有可能的API域名
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.tryRequest(baseUrl, path, method, data, headers);
                if (result.success) {
                    return result.data;
                }
            } catch (error) {
                continue; // 静默失败，尝试下一个域名
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ success: false, data: responseData, status: res.statusCode });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 模拟页面访问 - 核心功能
     */
    async simulatePageVisit() {
        try {
            console.log('💓 模拟页面访问...');
            
            // 1. 模拟获取用户信息 (相当于页面onLoad)
            await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            
            // 2. 模拟获取土地信息 (相当于页面onShow)
            await this.makeRequest('/garden/sorghum/index');
            
            console.log('✅ 页面访问模拟完成');
            return true;
            
        } catch (error) {
            console.log('❌ 页面访问模拟失败:', error.message);
            return false;
        }
    }

    /**
     * 启动会话保活
     */
    async start(intervalMinutes = 20) {
        if (this.isRunning) {
            console.log('⚠️ 会话保活已在运行中');
            return;
        }

        console.log(`\n🚀 启动会话保活，间隔${intervalMinutes}分钟`);
        
        // 首次模拟页面访问
        const success = await this.simulatePageVisit();
        if (!success) {
            console.log('❌ 初始页面访问失败，无法启动会话保活');
            return false;
        }
        
        this.isRunning = true;
        
        // 设置定时心跳
        this.heartbeatInterval = setInterval(async () => {
            await this.simulatePageVisit();
        }, intervalMinutes * 60 * 1000);
        
        console.log('✅ 会话保活已启动');
        console.log('🎯 现在你的自动脚本可以正常运行，无需手动进入页面');
        
        return true;
    }

    /**
     * 停止会话保活
     */
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ 会话保活未在运行');
            return;
        }

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        this.isRunning = false;
        console.log('🛑 会话保活已停止');
    }

    /**
     * 获取状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            memberId: this.memberId,
            tokenExpire: new Date(this.memberInfo.expireTime * 1000).toLocaleString('zh-CN')
        };
    }
}

// 导出类
module.exports = SimpleSessionKeeper;

// 如果直接运行此文件
if (require.main === module) {
    const keeper = new SimpleSessionKeeper();
    
    console.log('🎯 简化版会话保活器');
    console.log('📋 功能: 解决需要手动进入种植页面的问题');
    console.log('');
    
    // 启动会话保活
    keeper.start(20).then(success => {
        if (success) {
            console.log('\n🎉 会话保活启动成功！');
            console.log('');
            console.log('📝 使用说明:');
            console.log('  1. 保持此程序运行');
            console.log('  2. 现在可以运行你的自动种植脚本');
            console.log('  3. 无需再手动进入种植页面');
            console.log('  4. 系统会自动维持会话状态');
            console.log('');
            console.log('⚠️  注意: 不要关闭此程序');
            console.log('🛑 按 Ctrl+C 停止会话保活');
            
            // 处理退出信号
            process.on('SIGINT', () => {
                console.log('\n🛑 收到退出信号，正在停止会话保活...');
                keeper.stop();
                console.log('✅ 会话保活已安全停止');
                process.exit(0);
            });
            
        } else {
            console.log('❌ 会话保活启动失败');
            process.exit(1);
        }
    });
}
