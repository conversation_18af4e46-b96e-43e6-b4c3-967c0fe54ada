Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../../npm/wepy/lib/wepy.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function r(){var e,t,n;o(this,r);for(var i=arguments.length,u=Array(i),c=0;c<i;c++)u[c]=arguments[c];return t=n=a(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(u))),n.props={param:{}},n.methods={detail:function(e){this.$root.$navigate("/pages/bargain/goods_detail?ruleId="+e)}},a(n,t)}var i,u;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default.component),t(r,[{key:"onLoad",value:(i=regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)})),u=function(){var e=i.apply(this,arguments);return new Promise((function(t,r){return function n(o,a){try{var i=e[o](a),u=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(u)}("next")}))},function(){return u.apply(this,arguments)})}]),r}();exports.default=i;