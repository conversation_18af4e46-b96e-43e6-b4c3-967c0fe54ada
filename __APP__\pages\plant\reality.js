Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=a(require("./../../npm/wepy/lib/wepy.js")),r=(a(require("./../../api/member.js")),a(require("./../../api/duanwu.js")),a(require("./../../mixins/base.js"))),n=a(require("./../../components/common/loading.js")),i=(a(require("./../../utils/Tips.js")),a(require("./../../api/garden.js"))),o=a(require("./../../components/plant/head/modal.js"));function a(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,o){try{var a=t[i](o),s=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(s).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(s)}("next")}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=function(a){function l(){var e,t,a;u(this,l);for(var p=arguments.length,f=Array(p),g=0;g<p;g++)f[g]=arguments[g];return t=a=c(this,(e=l.__proto__||Object.getPrototypeOf(l)).call.apply(e,[this].concat(f))),a.data={rest:{},imgList:[],showImg:[],vidoList:[],query:"",init:!1,tipMsg:"您好！",showMsg:!1},a.mixins=[r.default],a.$repeat={},a.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},TipsModal:{"xmlns:v-on":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},a.$events={TipsModal:{"v-on:closeIt":"closeIt"}},a.components={Loading:n.default,TipsModal:o.default},a.methods={closeIt:function(){this.showMsg=!1,this.$apply()},previewImage:function(e){var t=e.currentTarget.dataset.src,r=e.currentTarget.dataset.list;wx.previewImage({current:t,urls:r})},bottomRefresh:function(){var e=this;return s(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log("到达底部了,加载更多电影文件内容"),t.next=3,i.default.reward();case 3:0===(r=t.sent).err&&(e.tipMsg=r.msg,e.showMsg=!0,e.$apply()),console.log(r);case 6:case"end":return t.stop()}}),t,e)})))()}},a.config={navigationBarBackgroundColor:"#fff",navigationBarTitleText:"实景",navigationBarTextStyle:"black"},c(a,t)}var p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,t.default.page),e(l,[{key:"onLoad",value:(p=s(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.default.realScene();case 2:for(r=e.sent,this.setData({rest:r,imgList:r.images,vidoList:r.videos}),this.imgList=r.images,n=0;n<r.images.length;n++)this.showImg.push(r.images[n].url);this.vidoList=r.videos,this.query=t,this.loaded();case 9:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})}]),l}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(l,"pages/plant/reality"));