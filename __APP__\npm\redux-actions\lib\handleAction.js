exports.__esModule=!0,exports.default=function(a,l,o){void 0===l&&(l=u.default);var f=(0,d.default)(a).split(s.ACTION_TYPE_DELIMITER);(0,e.default)(!(0,n.default)(o),"defaultState for reducer handling "+f.join(", ")+" should be defined"),(0,e.default)((0,t.default)(l)||(0,r.default)(l),"Expected reducer to be a function or object with next and throw reducers");var c=(0,t.default)(l)?[l,l]:[l.next,l.throw].map((function(e){return(0,i.default)(e)?u.default:e})),j=c[0],q=c[1];return function(e,t){void 0===e&&(e=o);var r=t.type;return r&&-1!==f.indexOf((0,d.default)(r))?(!0===t.error?q:j)(e,t):e}};var e=a(require("./../../invariant/browser.js")),t=a(require("./utils/isFunction.js")),r=a(require("./utils/isPlainObject.js")),u=a(require("./utils/identity.js")),i=a(require("./utils/isNil.js")),n=a(require("./utils/isUndefined.js")),d=a(require("./utils/toString.js")),s=require("./constants.js");function a(e){return e&&e.__esModule?e:{default:e}}