Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=function(){function t(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,i,s){return i&&t(e.prototype,i),s&&t(e,s),e}}(),e=s(require("./../npm/wepy/lib/wepy.js")),i=s(require("./../store/utils.js"));function s(t){return t&&t.__esModule?t:{default:t}}var n=function(){function s(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),this.init()}return t(s,[{key:"save",value:function(){this.computeCart(),e.default.setStorage({key:"carts",data:this}),console.time("[cart] save cart store"),i.default.save("cart",this.export()),console.timeEnd("[cart] save cart store")}},{key:"export",value:function(){return console.info("[cart] export cart data"),this.carts.forEach((function(t,e){t.quantity={componentId:e,num:t.goodsNum,min:1,max:99}})),{carts:this.carts,price:this.price,num:this.num,all:this.all,reload:this.reload,batch:this.batch,limitPrice:this.limitPrice,open:this.open,buy:this.num>0}}},{key:"empty",value:function(){return 0==this.num}},{key:"limit",value:function(){return Number(this.price)<Number(this.limitPrice)}},{key:"init",value:function(){var t=e.default.getStorageSync("carts");null==t||""==t?(this.carts=[],this.price=0,this.num=0,this.all=!1,this.reload=!1,this.batch=!1):Object.assign(this,t),i.default.save("cart",this.export())}},{key:"createCart",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=void 0,n=void 0;if(e){var o=t.goodsSkuInfo.goodsSkuDetails.find((function(t){return t.sku==e}));s=o.goodsSkuDetailBase.price,n=o.goodsSkuDetailBase.originalPrice}else s=t.sellPrice,n=t.originalPrice;return{goodsId:t.id,goodsSku:e,goodsName:t.name,innerCid:t.innerCid,goodsImage:t.imageUrl,goodsPrice:s,goodsNum:i,totalPrice:s,originalPrice:n,check:!0,discount:t.discount,discountRate:t.discountRate,discountText:t.discountText}}},{key:"plus",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=this.find(t.id,e);if(s)s.goodsNum=s.goodsNum+i,s.totalPrice=(s.goodsNum*s.goodsPrice).toFixed(2);else{var n=this.createCart(t,e,i);this.carts.push(n)}this.save()}},{key:"minus",value:function(t,e){var i=this.findIndex(t,e);if(-1!=i){var s=this.carts[i];s.goodsNum<=1?this.removeByIndex(i):(s.goodsNum-=1,s.totalPrice=(s.goodsNum*s.goodsPrice).toFixed(2)),this.save()}else console.warn("商品在购物车中不存在 id="+t+", sku="+e)}},{key:"clear",value:function(){this.carts=[],this.save()}},{key:"removeByIndex",value:function(t){this.carts.length>=t+1&&(this.carts.splice(t,1),this.save())}},{key:"removeChecked",value:function(){this.carts=this.carts.filter((function(t){return 0==t.check})),this.save()}},{key:"updateNum",value:function(t,e){var i=this.carts[t];i&&(i.goodsNum=e),this.save()}},{key:"toggleCartCheck",value:function(t){var e=this.carts[t];e&&(e.check=!e.check),this.save()}},{key:"toggleAllCheck",value:function(){this.all=!this.all,this.updateAllCheck(this.all),this.save()}},{key:"toggleBatch",value:function(){this.batch=!this.batch,this.batch?this.unselectAll():this.selectAll(),this.save()}},{key:"updateAllCheck",value:function(t){this.carts.forEach((function(e){e.check=t})),this.save()}},{key:"computeCart",value:function(){var t=this.carts.length>0,e=0,i=0,s=0;for(var n in this.carts){var o=this.carts[n];s+=o.goodsNum,o.check?(i+=o.goodsNum,e+=o.goodsPrice*o.goodsNum):t=!1}e=e.toFixed(2),this.all=t,this.num=i,this.price=e,this.count=s,0==this.carts.length&&(this.batch=!1)}},{key:"selectAll",value:function(){this.all=!0,this.updateAllCheck(this.all)}},{key:"unselectAll",value:function(){this.all=!1,this.updateAllCheck(this.all)}},{key:"find",value:function(t,e){return this.carts.find((function(i){return i.goodsId==t&&i.goodsSku==e}))}},{key:"findIndex",value:function(t,e){return null!=e&&""!=e?this.carts.findIndex((function(i){return i.goodsId==t&&i.goodsSku==e})):this.carts.findIndex((function(e){return e.goodsId==t}))}},{key:"findByGoodsId",value:function(t){return this.carts.filter((function(e){return e.goodsId==t}))}},{key:"getCheckedCarts",value:function(){return this.carts.filter((function(t){return t.check}))}},{key:"checkGoodsStock",value:function(){var t=this.carts.find((function(t){return t.goodsNum>t.stock||0==t.stock}));if(null!=t)return 0==t.stock?t.goodsName+" 无货":t.goodsName+" 库存不足"}}],[{key:"create",value:function(){return null==this._self&&(this._self=new s),this._self}}]),s}();exports.default=n;