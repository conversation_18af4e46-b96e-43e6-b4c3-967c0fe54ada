Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=u(require("./../../npm/wepy/lib/wepy.js")),r=u(require("./../../store/utils.js")),n=u(require("./../../api/auth.js")),i=u(require("./../../mixins/base.js")),a=u(require("./../../utils/Tips.js")),o=u(require("./../../utils/WxUtils.js"));function u(e){return e&&e.__esModule?e:{default:e}}function c(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,a){try{var o=t[i](a),u=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=function(u){function l(){var e,r,u;s(this,l);for(var p=arguments.length,d=Array(p),b=0;b<p;b++)d[b]=arguments[b];return r=u=f(this,(e=l.__proto__||Object.getPrototypeOf(l)).call.apply(e,[this].concat(d))),u.data={redirect:"false",init:!1,reInfo:null,reAuth:null},u.methods={confirm:function(e){var r=this,i=e.detail;return c(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!i.rawData){e.next=10;break}return e.next=3,n.default.user({block:!1,redirect:!1},i);case 3:if(e.sent){e.next=6;break}return e.abrupt("return");case 6:a.default.success("授权成功"),1==r.redirect||"true"==r.redirect?o.default.backOrRedirect("/pages/anticounterfeiting/index"):t.default.navigateBack(),e.next=11;break;case 10:a.default.error("授权失败");case 11:case"end":return e.stop()}}),e,r)})))()},back:function(){"true"==this.redirect?o.default.backOrRedirect("/pages/anticounterfeiting/index"):t.default.navigateBack()},tips:function(){a.default.modal("微信版本过低，请更新微信，或在列表删除小程序后重新访问")}},u.events={},u.components={},u.mixins=[i.default],u.config={navigationBarTitleText:"用户授权"},f(u,r)}var p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,t.default.page),e(l,[{key:"onLoad",value:(p=c(regeneratorRuntime.mark((function e(t){var n=t.redirect;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.default.init();case 2:this.redirect=n,this.reInfo=o.default.canIUse("button.open-type.getUserInfo"),this.loaded();case 5:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})}]),l}();exports.default=l;