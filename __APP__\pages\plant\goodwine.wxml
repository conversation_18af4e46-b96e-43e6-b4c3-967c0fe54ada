<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <view class="outer" wx:if="{{init}}">
        <view class="doommview">
            <view class="steal" style="animation:first 10s {{ind*3}}s linear forwards;top:100rpx;" wx:for="{{stealRecord}}" wx:for-index="ind" wx:key="id">
                <image class="head_img" src="{{item.steal_head_img}}"></image>
                <view class="names">
                    <view class="name">{{item.steal_nick_name}}</view>
                    <view style="font-size:24rpx">偷取了您{{item.steal_wine_num}}L酒~</view>
                </view>
            </view>
        </view>
        <view catchtap="close" class="cover" wx:if="{{liangShow}}">
            <view catchtap="catchtap" class="your_gifts">
                <view class="hide_title">放入粮食</view>
                <view class="hide_cont">
                    <view bindtap="gotIt" class="close">X</view>
                    <view class="hide_send">
                        <text>数量</text>
                        <view class="food-input-content">
                            <view bindtap="reduceFood" class="btn">
                                <view class="reduce-btn" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/jianliangshi.png)" wx:if="{{howmany>100}}"></view>
                                <view class="reduce-btn" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/jianliangshihui.png)" wx:else></view>
                            </view>
                            <view class="food-count">{{howmany}}</view>
                            <view bindtap="addFood" class="btn">
                                <view class="add-btn" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/jialiangshi.png)" wx:if="{{howmany<1000}}"></view>
                                <view class="add-btn" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/jialiangshihui.png)" wx:else></view>
                            </view>
                        </view>
                        <text>斤</text>
                    </view>
                    <view bindtap="closeItSo" class="gotIt">开始酿酒</view>
                </view>
            </view>
        </view>
        <view class="content2" style="background-image:{{contentBg}}">
            <view class="cloud1"></view>
            <view class="cloud2"></view>
            <view class="bubble" wx:if="{{poptipsMessage&&showBubble}}">
                <view class="cont">
                    <rich-text nodes="{{poptipsMessage.content}}" style="font-size:24rpx"></rich-text>
                </view>
            </view>
            <view bindtap="show" class="humBox" data-wpyshow-a="2"></view>
            <view bindtap="show" class="pool" data-wpyshow-a="3"></view>
            <view class="bubblePro" wx:if="{{poptipsMessage&&showBubblePro}}">
                <view class="cont">
                    <text style="font-weight:bold">{{poptipsMessage.title}}:</text>
                    <rich-text nodes="{{poptipsMessage.content}}" style="font-size:24rpx"></rich-text>
                </view>
            </view>
            <view class="swtch">
                <view catchtap="switch1Change" class="switch-content" data-wpyswitch1change-a="{{false}}" wx:if="{{switch1Checked}}">
                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-music-on.png"></image>
                </view>
                <view catchtap="switch1Change" class="switch-content" data-wpyswitch1change-a="{{true}}" wx:else>
                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-music-off.png"></image>
                </view>
            </view>
            <view class="header-bar">
                <view class="avatar">
                    <view class="img-content">
                        <image class="avatar-image" src="{{userInfo.head_imgurl}}"></image>
                    </view>
                    <view class="name">{{userInfo.nick_name}}的农场</view>
                </view>
                <view class="depot-info">
                    <view class="depot-info-item">
                        <view class="icon">
                            <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-sorghum-package.png"></image>
                        </view>
                        <view class="info-main">高粱{{userInfo.sorghum==null?0:userInfo.sorghum}}斤</view>
                    </view>
                    <view class="depot-info-item">
                        <view class="icon">
                            <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-wheat-package.png"></image>
                        </view>
                        <view class="info-main">小麦{{userInfo.wheat==null?0:userInfo.wheat}}斤</view>
                    </view>
                </view>
            </view>
            <view class="introduction">
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/customer/index" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/Introduction" hoverClass="hover1"></view>
            </view>
            <view class="wbox">
                <view bindtap="redirecttoWithLogined" class="goPlant" data-url="/pages/plant/index"></view>
            </view>
            <view class="wineBox">
                <view bindtap="show" class="jar" data-wpyshow-a="1" hoverClass="jar-animation" style="background-image:url({{jarImage}})">
                    <view class="wine-num {{userInfo.wine==0?'wine-num-0':''}}">{{userInfo.wine}}L</view>
                </view>
                <view bindtap="upToMiss" class="yuanbing yuanbing{{index+1}} {{notgain[index]}}" data-show="{{index}}" id="{{item.id}}" style="{{current==index?uptomiss:''}}" wx:if="{{popShow[index].show}}" wx:for="{{popShow}}" wx:key="index">{{backend[index].time?backend[index].time:''}}</view>
                <view class="touch">
                    <view class="news1" wx:if="{{poptipsMessage&&showNews}}">
                        <view catchtap="catchtap" class="news">
                            <view class="kuang"></view>
                            <view bindtap="close" class="close"></view>
                            <view class="cont">
                                <text style="font-weight:bold">{{poptipsMessage.title}}:</text>
                                <rich-text nodes="{{poptipsMessage.content}}" style="font-size:24rpx"></rich-text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="labour">
                <view bindtap="putFoods" class="button2" hoverClass="hover2"></view>
            </view>
            <view class="bottom-bar">
                <view bindtap="showModal_firends" class="item" data-wpyshowmodal_firends-a="" hoverClass="hover1"></view>
                <view bindtap="showModal_task" class="item" data-wpyshowmodal_task-a="" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/luck_draw" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/exchangeStore" hoverClass="hover1"></view>
                <view catchtap="showDepotModal" class="item" data-wpyshowdepotmodal-a="{{true}}" hoverClass="hover1"></view>
            </view>
            <view bindtap="hideModal" catchtouchmove="throttle" class="commodity_screen" wx:if="{{showModalStatus}}"></view>
            <view animation="{{animationData}}" class="commodity_attr_box_task2" wx:if="{{showModalStatus_task2}}">
                <view class="title">做任务取河水</view>
                <view class="task_cont">
                    <view class="tasks  type{{item.type}}" wx:for="{{tasks}}" wx:for-index="tindex" wx:key="tindex">
                        <view class="task_name">
                            <view class="tLeft">
                                <view class="img"></view>
                                <view class="abc">
                                    <view class="font1">{{item.name}}</view>
                                    <view class="font2">{{item.reward}}</view>
                                </view>
                            </view>
                            <button bindtap="gotoWithTasks" class="task_btn" data-url="{{item.target}}" id="{{tindex}}" openType="{{item.type==3?'share':''}}">{{item.is_complete?'已完成':'去完成'}}</button>
                        </view>
                    </view>
                </view>
            </view>
            <view animation="{{animationData}}" class="commodity_attr_box_friends" wx:if="{{showModalStatus_firends2}}">
                <view class="title">我的微信好友</view>
                <scroll-view bindscrolltolower="friend_listShow" class="friends_cont" scrollY="true" style="height:{{friend_notlogin.length?'60%':'80%'}}">
                    <view bindtap="redirecttoWithLogined" class="friends" data-url="/pages/plant/scrounge?id={{item.member_id}}&url={{item.head_imgurl}}&nick={{item.nick_name}}&hum={{item.sorghum}}&wine={{item.wine}}&real={{item.real_name}}" wx:for="{{friend_list}}" wx:for-index="tindex" wx:key="tindex">
                        <view class="task_name">
                            <view class="tLeft">
                                <text class="ranks">{{tindex+1}}</text>
                                <image class="img" src="{{item.head_imgurl}}"></image>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name?item.nick_name:item.real_name}}</view>
                                </view>
                            </view>
                            <view class="task_btn">{{item.wine}}L</view>
                        </view>
                        <view class="handbg" wx:if="{{item.is_can_steal}}"></view>
                    </view>
                    <view class="nomore" wx:if="{{more_friend}}">您没有更多好友啦，到每日任务里通过分享即可邀请并和TA成为游戏好友！</view>
                </scroll-view>
                <view wx:if="{{friend_notlogin.length}}">
                    <view class="title2">您的好友已经迷路了，快去找他回来</view>
                    <view class="friends notlogin" wx:for="{{friend_notlogin}}" wx:key="index">
                        <view class="task_name">
                            <view class="tLeft">
                                <image class="img" src="{{item.head_imgurl}}"></image>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name}}</view>
                                </view>
                            </view>
                            <button class="invite_btn" openType="share">邀请TA</button>
                        </view>
                    </view>
                </view>
            </view>
            <view class="depot-modal" wx:if="{{showDepot}}">
                <view class="modal-select-box">
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-sorghum.png"></image>
                        <view class="item-text">高粱:{{userInfo.sorghum}}斤</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-wheat.png"></image>
                        <view class="item-text">小麦:{{userInfo.wheat}}斤</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-jar.png"></image>
                        <view class="item-text">酒:{{userInfo.wine}}ML</view>
                    </view>
                </view>
                <view class="modal-select-box">
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-kettle.png"></image>
                        <view class="item-text">水:{{userInfo.water}}桶</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-muck.png"></image>
                        <view class="item-text">肥料:{{userInfo.manure}}袋</view>
                    </view>
                </view>
                <view catchtap="showDepotModal" class="modal-button-enter" data-wpyshowdepotmodal-a="{{false}}" hoverClass="hover1"></view>
                <view class="modal-bg"></view>
                <view class="mask"></view>
            </view>
        </view>
    </view>
</view>
