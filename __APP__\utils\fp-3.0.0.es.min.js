Object.defineProperty(exports,"__esModule",{value:!0});var t,e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r={appId:"default",channel:"",organization:"",apiHost:"fp-it.fengkongcloud.com",apiPath:"/deviceprofile/v4",apiProtocol:"https",publicKey:""};!function(t){t.wx="wx",t.qq="qq",t.tt="tt",t.my="my"}(t||(t={}));var n="wx"===t.wx,i=(t.qq,"wx"===t.tt),s="wx"===t.my,o=wx;function a(){try{return o.getLaunchOptionsSync()}catch(t){return{}}}function h(){try{return o.getSystemInfoSync()}catch(t){return{}}}function u(){var t=[new Promise((function(t){var e={screenBright:-1};try{o.getScreenBrightness({success:function(r){try{var n=s?r.brightness:r.value;t({screenBright:n})}catch(r){t(e)}},fail:function(){t(e)}})}catch(r){t(e)}})),new Promise((function(t){var e={networkType:""};try{o.getNetworkType({success:function(r){try{t({networkType:r.networkType})}catch(r){t(e)}},fail:function(){t(e)}})}catch(r){t(e)}})),h()];return(n||i)&&t.push(new Promise((function(t){var e={connectedWifi:{}};try{n?o.startWifi({success:function(){o.getConnectedWifi({complete:function(r){try{t(r.wifi?{connectedWifi:r.wifi}:e)}catch(r){t(e)}}})},fail:function(){t(e)}}):i&&o.getConnectedWifi({complete:function(r){try{t(r?{connectedWifi:r}:e)}catch(r){t(e)}}})}catch(r){t(e)}}))),Promise.all(t)}var c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var l,p=Array.isArray,d="object"==(void 0===c?"undefined":e(c))&&c&&c.Object===Object&&c,g="object"==("undefined"==typeof self?"undefined":e(self))&&self&&self.Object===Object&&self,v=d||g||Function("return this")(),y=v.Symbol,m=y,_=Object.prototype,b=_.hasOwnProperty,w=_.toString,S=m?m.toStringTag:void 0,x=Object.prototype.toString,T=function(t){var e=b.call(t,S),r=t[S];try{t[S]=void 0;var n=!0}catch(t){}var i=w.call(t);return n&&(e?t[S]=r:delete t[S]),i},E=y?y.toStringTag:void 0,D=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":E&&E in Object(t)?T(t):function(t){return x.call(t)}(t)},B=D,A=function(t){return"symbol"==(void 0===t?"undefined":e(t))||function(t){return null!=t&&"object"==(void 0===t?"undefined":e(t))}(t)&&"[object Symbol]"==B(t)},k=p,O=A,z=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,I=/^\w*$/,R=function(t){var r=void 0===t?"undefined":e(t);return null!=t&&("object"==r||"function"==r)},C=D,N=R,V=v["__core-js_shared__"],P=(l=/[^.]+$/.exec(V&&V.keys&&V.keys.IE_PROTO||""))?"Symbol(src)_1."+l:"",M=Function.prototype.toString,j=function(t){if(!N(t))return!1;var e=C(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e},H=function(t){return!!P&&P in t},q=R,L=/^\[object .+?Constructor\]$/,F=Function.prototype,U=Object.prototype,K=F.toString,Z=U.hasOwnProperty,$=RegExp("^"+K.call(Z).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),G=function(t){return!(!q(t)||H(t))&&(j(t)?$:L).test(function(t){if(null!=t){try{return M.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(t))},W=function(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return G(r)?r:void 0},J=W(Object,"create"),X=J,Y=J,Q=Object.prototype.hasOwnProperty,tt=J,et=Object.prototype.hasOwnProperty,rt=J,nt=function(){this.__data__=X?X(null):{},this.size=0},it=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},st=function(t){var e=this.__data__;if(Y){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return Q.call(e,t)?e[t]:void 0};function ot(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}ot.prototype.clear=nt,ot.prototype.delete=it,ot.prototype.get=st,ot.prototype.has=function(t){var e=this.__data__;return tt?void 0!==e[t]:et.call(e,t)},ot.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=rt&&void 0===e?"__lodash_hash_undefined__":e,this};var at=ot,ht=function(t,e){return t===e||t!=t&&e!=e},ut=function(t,e){for(var r=t.length;r--;)if(ht(t[r][0],e))return r;return-1},ct=ut,ft=Array.prototype.splice,lt=ut,pt=ut,dt=ut,gt=function(){this.__data__=[],this.size=0};function vt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}vt.prototype.clear=gt,vt.prototype.delete=function(t){var e=this.__data__,r=ct(e,t);return!(r<0||(r==e.length-1?e.pop():ft.call(e,r,1),--this.size,0))},vt.prototype.get=function(t){var e=this.__data__,r=lt(e,t);return r<0?void 0:e[r][1]},vt.prototype.has=function(t){return pt(this.__data__,t)>-1},vt.prototype.set=function(t,e){var r=this.__data__,n=dt(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this};var yt=vt,mt=W(v,"Map"),_t=at,bt=yt,wt=mt,St=function(t,r){var n=t.__data__;return function(t){var r=void 0===t?"undefined":e(t);return"string"==r||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==t:null===t}(r)?n["string"==typeof r?"string":"hash"]:n.map},xt=St,Tt=St,Et=St,Dt=St;function Bt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Bt.prototype.clear=function(){this.size=0,this.__data__={hash:new _t,map:new(wt||bt),string:new _t}},Bt.prototype.delete=function(t){var e=xt(this,t).delete(t);return this.size-=e?1:0,e},Bt.prototype.get=function(t){return Tt(this,t).get(t)},Bt.prototype.has=function(t){return Et(this,t).has(t)},Bt.prototype.set=function(t,e){var r=Dt(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this};var At=Bt;function kt(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function r(){var n=arguments,i=e?e.apply(this,n):n[0],s=r.cache;if(s.has(i))return s.get(i);var o=t.apply(this,n);return r.cache=s.set(i,o)||s,o};return r.cache=new(kt.Cache||At),r}kt.Cache=At;var Ot=kt,zt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,It=/\\(\\)?/g,Rt=function(t){var e=Ot((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(zt,(function(t,r,n,i){e.push(n?i.replace(It,"$1"):r||t)})),e}),(function(t){return 500===r.size&&r.clear(),t})),r=e.cache;return e}(),Ct=p,Nt=A,Vt=y?y.prototype:void 0,Pt=Vt?Vt.toString:void 0,Mt=function t(e){if("string"==typeof e)return e;if(Ct(e))return function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}(e,t)+"";if(Nt(e))return Pt?Pt.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r},jt=p,Ht=function(t,r){if(k(t))return!1;var n=void 0===t?"undefined":e(t);return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!O(t))||I.test(t)||!z.test(t)||null!=r&&t in Object(r)},qt=Rt,Lt=A,Ft=function(t,e){return jt(t)?t:Ht(t,e)?[t]:qt(function(t){return null==t?"":Mt(t)}(t))},Ut=function(t){if("string"==typeof t||Lt(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e},Kt=function(t,e,r){var n=null==t?void 0:function(t,e){for(var r=0,n=(e=Ft(e,t)).length;null!=t&&r<n;)t=t[Ut(e[r++])];return r&&r==n?t:void 0}(t,e);return void 0===n?r:n},Zt=new(function(){function t(){this.smInfo={storageName:"",SMID:"",smData:"",smEncryptedData:"",priId:"",ep:"",uid:"",retryCnt:0,isNeedStop:!1,smAesData:""}}return t.prototype.getSmInfo=function(t){return this.smInfo[t]},t.prototype.setSmInfo=function(t,e){this.smInfo[t]=e},t}());function $t(t,e,r,n){try{!function(t){try{o.request({url:t.url,data:JSON.stringify(t.data),header:t.header,method:t.method||"POST",responseType:t.responseType||"json",success:t.success||function(){},fail:t.fail||function(){}})}catch(e){t.fail()}}({url:t,data:e,method:"POST",responseType:"text",success:r,fail:n})}catch(t){}}function Gt(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function Wt(t,e){return t&e}function Jt(t,e){return t|e}function Xt(t,e){return t^e}function Yt(t,e){return t&~e}function Qt(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function te(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var ee,re="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function ne(t){var e,r,n="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),n+=re.charAt(r>>6)+re.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),n+=re.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),n+=re.charAt(r>>2)+re.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function ie(t){var e,r="",n=0,i=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var s=re.indexOf(t.charAt(e));s<0||(0==n?(r+=Gt(s>>2),i=3&s,n=1):1==n?(r+=Gt(i<<2|s>>4),i=15&s,n=2):2==n?(r+=Gt(i),r+=Gt(s>>2),i=3&s,n=3):(r+=Gt(i<<2|s>>4),r+=Gt(15&s),n=0))}return 1==n&&(r+=Gt(i<<2)),r}var se,oe={decode:function(t){var e;if(void 0===se){var r="= \f\n\r\t \u2028\u2029";for(se=Object.create(null),e=0;e<64;++e)se["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(se["-"]=62,se._=63,e=0;e<r.length;++e)se[r.charAt(e)]=-1}var n=[],i=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=se[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);i|=o,++s>=4?(n[n.length]=i>>16,n[n.length]=i>>8&255,n[n.length]=255&i,i=0,s=0):i<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:n[n.length]=i>>10;break;case 3:n[n.length]=i>>16,n[n.length]=i>>8&255}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=oe.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return oe.decode(t)}},ae=1e13,he=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,n,i=this.buf,s=i.length;for(r=0;r<s;++r)(n=i[r]*t+e)<ae?e=0:n-=(e=0|n/ae)*ae,i[r]=n;e>0&&(i[r]=e)},t.prototype.sub=function(t){var e,r,n=this.buf,i=n.length;for(e=0;e<i;++e)(r=n[e]-t)<0?(r+=ae,t=1):t=0,n[e]=r;for(;0===n[n.length-1];)n.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;n>=0;--n)r+=(ae+e[n]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*ae+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),ue=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,ce=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function fe(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var le,pe=function(){function t(e,r){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var n="",i=t;i<e;++i)if(n+=this.hexByte(this.get(i)),!0!==r)switch(15&i){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",n=t;n<e;){var i=this.get(n++);r+=i<128?String.fromCharCode(i):i>191&&i<224?String.fromCharCode((31&i)<<6|63&this.get(n++)):String.fromCharCode((15&i)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,n,i="",s=t;s<e;)r=this.get(s++),n=this.get(s++),i+=String.fromCharCode(r<<8|n);return i},t.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),i=(r?ue:ce).exec(n);return i?(r&&(i[1]=+i[1],i[1]+=+i[1]<70?2e3:1900),n=i[1]+"-"+i[2]+"-"+i[3]+" "+i[4],i[5]&&(n+=":"+i[5],i[6]&&(n+=":"+i[6],i[7]&&(n+="."+i[7]))),i[8]&&(n+=" UTC","Z"!=i[8]&&(n+=i[8],i[9]&&(n+=":"+i[9]))),n):"Unrecognized time: "+n},t.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),i=n>127,s=i?255:0,o="";n==s&&++t<e;)n=this.get(t);if(0==(r=e-t))return i?-1:0;if(r>4){for(o=n,r<<=3;0==(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}i&&(n-=256);for(var a=new he(n),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return o+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),i="("+((e-t-1<<3)-n)+" bit)\n",s="",o=t+1;o<e;++o){for(var a=this.get(o),h=o==e-1?n:0,u=7;u>=h;--u)s+=a>>u&1?"1":"0";if(s.length>r)return i+fe(s,r)}return i+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return fe(this.parseStringISO(t,e),r);var n=e-t,i="("+n+" byte)\n";n>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)i+=this.hexByte(this.get(s));return n>r&&(i+="…"),i},t.prototype.parseOID=function(t,e,r){for(var n="",i=new he,s=0,o=t;o<e;++o){var a=this.get(o);if(i.mulAdd(128,127&a),s+=7,!(128&a)){if(""===n)if((i=i.simplify())instanceof he)i.sub(80),n="2."+i.toString();else{var h=i<80?i<40?0:1:2;n=h+"."+(i-40*h)}else n+="."+i.toString();if(n.length>r)return fe(n,r);i=new he,s=0}}return s>0&&(n+=".incomplete"),n},t}(),de=function(){function t(t,e,r,n,i){if(!(n instanceof ge))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=i}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return fe(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return fe(this.stream.parseStringISO(e,e+r),t);case 30:return fe(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var n=0;n<r;++n)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof pe?e:new pe(e,0);var n=new pe(r),i=new ge(r),s=t.decodeLength(r),o=r.pos,a=o-n.pos,h=null,u=function(){var e=[];if(null!==s){for(var n=o+s;r.pos<n;)e[e.length]=t.decode(r);if(r.pos!=n)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var i=t.decode(r);if(i.tag.isEOC())break;e[e.length]=i}s=o-r.pos}catch(t){throw new Error("Exception while decoding undefined length content: "+t)}return e};if(i.tagConstructed)h=u();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=u();for(var c=0;c<h.length;++c)if(h[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(t){h=null}if(null===h){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);r.pos=o+Math.abs(s)}return new t(n,a,s,i,h)},t}(),ge=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new he;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),ve=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],ye=(1<<26)/ve[ve.length-1],me=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,s="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0)for(a<this.DB&&(r=this[o]>>a)>0&&(i=!0,s=Gt(r));o>=0;)a<e?(r=(this[o]&(1<<a)-1)<<e-a,r|=this[--o]>>(a+=this.DB-e)):(r=this[o]>>(a-=e)&n,a<=0&&(a+=this.DB,--o)),r>0&&(i=!0),i&&(s+=Gt(r));return i?s:"0"},t.prototype.negate=function(){var e=xe();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+ze(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=xe();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new be(e):new we(e),this.exp(t,r)},t.prototype.clone=function(){var t=xe();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=xe();return this.bitwiseTo(t,Wt,e),e},t.prototype.or=function(t){var e=xe();return this.bitwiseTo(t,Jt,e),e},t.prototype.xor=function(t){var e=xe();return this.bitwiseTo(t,Xt,e),e},t.prototype.andNot=function(t){var e=xe();return this.bitwiseTo(t,Yt,e),e},t.prototype.not=function(){for(var t=xe(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=xe();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=xe();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+Qt(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=te(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,Jt)},t.prototype.clearBit=function(t){return this.changeBit(t,Yt)},t.prototype.flipBit=function(t){return this.changeBit(t,Xt)},t.prototype.add=function(t){var e=xe();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=xe();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=xe();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=xe();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=xe();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=xe(),r=xe();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),s=Oe(1);if(i<=0)return s;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new be(e):e.isEven()?new Se(e):new we(e);var o=[],a=3,h=r-1,u=(1<<r)-1;if(o[1]=n.convert(this),r>1){var c=xe();for(n.sqrTo(o[1],c);a<=u;)o[a]=xe(),n.mulTo(c,o[a-2],o[a]),a+=2}var f,l,p=t.t-1,d=!0,g=xe();for(i=ze(t[p])-1;p>=0;){for(i>=h?f=t[p]>>i-h&u:(f=(t[p]&(1<<i+1)-1)<<h-i,p>0&&(f|=t[p-1]>>this.DB+i-h)),a=r;0==(1&f);)f>>=1,--a;if((i-=a)<0&&(i+=this.DB,--p),d)o[f].copyTo(s),d=!1;else{for(;a>1;)n.sqrTo(s,g),n.sqrTo(g,s),a-=2;a>0?n.sqrTo(s,g):(l=s,s=g,g=l),n.mulTo(g,o[f],s)}for(;p>=0&&0==(t[p]&1<<i);)n.sqrTo(s,g),l=s,s=g,g=l,--i<0&&(i=this.DB-1,--p)}return n.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var n=e.clone(),i=this.clone(),s=Oe(1),o=Oe(0),a=Oe(0),h=Oe(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;i.isEven();)i.rShiftTo(1,i),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);n.compareTo(i)>=0?(n.subTo(i,n),r&&s.subTo(a,s),o.subTo(h,o)):(i.subTo(n,i),r&&a.subTo(s,a),h.subTo(o,h))}return 0!=i.compareTo(t.ONE)?t.ZERO:h.compareTo(e)>=0?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new _e)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(i<s&&(s=i),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=ve[ve.length-1]){for(e=0;e<ve.length;++e)if(r[0]==ve[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<ve.length;){for(var n=ve[e],i=e+1;i<ve.length&&n<ye;)n*=ve[i++];for(n=r.modInt(n);e<i;)if(n%ve[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var n;if(16==r)n=4;else if(8==r)n=3;else if(256==r)n=8;else if(2==r)n=1;else if(32==r)n=5;else{if(4!=r)return void this.fromRadix(e,r);n=2}this.t=0,this.s=0;for(var i=e.length,s=!1,o=0;--i>=0;){var a=8==n?255&+e[i]:ke(e,i);a<0?"-"==e.charAt(i)&&(s=!0):(s=!1,0==o?this[this.t++]=a:o+n>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=n)>=this.DB&&(o-=this.DB))}8==n&&0!=(128&+e[0])&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,i=(1<<n)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+s+1]=this[a]>>n|o,o=(this[a]&i)<<r;for(a=s-1;a>=0;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,s=(1<<n)-1;e[0]=this[r]>>n;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<i,e[o-r]=this[o]>>n;n>0&&(e[this.t-r-1]|=(this.s&s)<<i),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var n=this.abs(),i=e.abs(),s=n.t;for(r.t=s+i.t;--s>=0;)r[s]=0;for(s=0;s<i.t;++s)r[s+n.t]=n.am(0,i[s],r,s,0,n.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,n){var i=e.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=r&&r.fromInt(0),void(null!=n&&this.copyTo(n));null==n&&(n=xe());var o=xe(),a=this.s,h=e.s,u=this.DB-ze(i[i.t-1]);u>0?(i.lShiftTo(u,o),s.lShiftTo(u,n)):(i.copyTo(o),s.copyTo(n));var c=o.t,f=o[c-1];if(0!=f){var l=f*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),p=this.FV/l,d=(1<<this.F1)/l,g=1<<this.F2,v=n.t,y=v-c,m=null==r?xe():r;for(o.dlShiftTo(y,m),n.compareTo(m)>=0&&(n[n.t++]=1,n.subTo(m,n)),t.ONE.dlShiftTo(c,m),m.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--y>=0;){var _=n[--v]==f?this.DM:Math.floor(n[v]*p+(n[v-1]+g)*d);if((n[v]+=o.am(0,_,n,y,0,c))<_)for(o.dlShiftTo(y,m),n.subTo(m,n);n[v]<--_;)n.subTo(m,n)}null!=r&&(n.drShiftTo(c,r),a!=h&&t.ZERO.subTo(r,r)),n.t=c,n.clamp(),u>0&&n.rShiftTo(u,n),a<0&&t.ZERO.subTo(n,n)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var n=xe(),i=xe(),s=r.convert(this),o=ze(e)-1;for(s.copyTo(n);--o>=0;)if(r.sqrTo(n,i),(e&1<<o)>0)r.mulTo(i,s,n);else{var a=n;n=i,i=a}return r.revert(n)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=Oe(r),i=xe(),s=xe(),o="";for(this.divRemTo(n,i,s);i.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,i.divRemTo(n,i,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var n=this.chunkSize(r),i=Math.pow(r,n),s=!1,o=0,a=0,h=0;h<e.length;++h){var u=ke(e,h);u<0?"-"==e.charAt(h)&&0==this.signum()&&(s=!0):(a=r*a+u,++o>=n&&(this.dMultiply(i),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(a,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,n){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),Jt,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var i=[],s=7&e;i.length=1+(e>>3),r.nextBytes(i),s>0?i[0]&=(1<<s)-1:i[0]=0,this.fromString(i,256)}},t.prototype.bitwiseTo=function(t,e,r){var n,i,s=Math.min(t.t,this.t);for(n=0;n<s;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=s;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=s;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var n=t.ONE.shiftLeft(e);return this.bitwiseTo(n,r,n),n},t.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var i=r.t-this.t;n<i;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(i=Math.min(t.t,e);n<i;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),n=r.getLowestSetBit();if(n<=0)return!1;var i=r.shiftRight(n);(e=e+1>>1)>ve.length&&(e=ve.length);for(var s=xe(),o=0;o<e;++o){s.fromInt(ve[Math.floor(Math.random()*ve.length)]);var a=s.modPow(i,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var h=1;h++<n&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=xe();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var i=r;r=n,n=i}var s=r.getLowestSetBit(),o=n.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),n.rShiftTo(o,n));setTimeout((function t(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=n.getLowestSetBit())>0&&n.rShiftTo(s,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(t,0):(o>0&&n.lShiftTo(o,n),setTimeout((function(){e(n)}),0))}),10)}},t.prototype.fromNumberAsync=function(e,r,n,i){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),Jt,this),this.isEven()&&this.dAddOffset(1,0);var s=this;setTimeout((function n(){s.dAddOffset(2,0),s.bitLength()>e&&s.subTo(t.ONE.shiftLeft(e-1),s),s.isProbablePrime(r)?setTimeout((function(){i()}),0):setTimeout(n,0)}),0)}else{var o=[],a=7&e;o.length=1+(e>>3),r.nextBytes(o),a>0?o[0]&=(1<<a)-1:o[0]=0,this.fromString(o,256)}},t}(),_e=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),be=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),we=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=xe();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(me.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=xe();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),Se=function(){function t(t){this.m=t,this.r2=xe(),this.q3=xe(),me.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=xe();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function xe(){return new me(null)}function Te(t,e){return new me(t,e)}var Ee="undefined"!=typeof navigator;Ee&&"Microsoft Internet Explorer"==navigator.appName?(me.prototype.am=function(t,e,r,n,i,s){for(var o=32767&e,a=e>>15;--s>=0;){var h=32767&this[t],u=this[t++]>>15,c=a*h+u*o;i=((h=o*h+((32767&c)<<15)+r[n]+(1073741823&i))>>>30)+(c>>>15)+a*u+(i>>>30),r[n++]=1073741823&h}return i},le=30):Ee&&"Netscape"!=navigator.appName?(me.prototype.am=function(t,e,r,n,i,s){for(;--s>=0;){var o=e*this[t++]+r[n]+i;i=Math.floor(o/67108864),r[n++]=67108863&o}return i},le=26):(me.prototype.am=function(t,e,r,n,i,s){for(var o=16383&e,a=e>>14;--s>=0;){var h=16383&this[t],u=this[t++]>>14,c=a*h+u*o;i=((h=o*h+((16383&c)<<14)+r[n]+i)>>28)+(c>>14)+a*u,r[n++]=268435455&h}return i},le=28),me.prototype.DB=le,me.prototype.DM=(1<<le)-1,me.prototype.DV=1<<le,me.prototype.FV=Math.pow(2,52),me.prototype.F1=52-le,me.prototype.F2=2*le-52;var De,Be,Ae=[];for(De="0".charCodeAt(0),Be=0;Be<=9;++Be)Ae[De++]=Be;for(De="a".charCodeAt(0),Be=10;Be<36;++Be)Ae[De++]=Be;for(De="A".charCodeAt(0),Be=10;Be<36;++Be)Ae[De++]=Be;function ke(t,e){var r=Ae[t.charCodeAt(e)];return null==r?-1:r}function Oe(t){var e=xe();return e.fromInt(t),e}function ze(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}me.ZERO=Oe(0),me.ONE=Oe(1);var Ie,Re,Ce=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}(),Ne=null;function Ve(){if(null==Ie){for(Ie=new Ce;Re<256;){var t=Math.floor(65536*Math.random());Ne[Re++]=255&t}for(Ie.init(Ne),Re=0;Re<Ne.length;++Re)Ne[Re]=0;Re=0}return Ie.next()}null==Ne&&(Ne=[],Re=0);var Pe=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Ve()},t}(),Me=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Te(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=t.length-1;n>=0&&e>0;){var i=t.charCodeAt(n--);i<128?r[--e]=i:i>127&&i<2048?(r[--e]=63&i|128,r[--e]=i>>6|192):(r[--e]=63&i|128,r[--e]=i>>6&63|128,r[--e]=i>>12|224)}r[--e]=0;for(var s=new Pe,o=[];e>2;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new me(r)}(t,e);if(null==r)return null;var n=this.doPublic(r);if(null==n)return null;for(var i=n.toString(16),s=i.length,o=0;o<2*e-s;o++)i="0"+i;return i},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Te(t,16),this.e=parseInt(e,16),this.d=Te(r,16)):console.error("Invalid RSA private key")},t.prototype.setPrivateEx=function(t,e,r,n,i,s,o,a){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Te(t,16),this.e=parseInt(e,16),this.d=Te(r,16),this.p=Te(n,16),this.q=Te(i,16),this.dmp1=Te(s,16),this.dmq1=Te(o,16),this.coeff=Te(a,16)):console.error("Invalid RSA private key")},t.prototype.generate=function(t,e){var r=new Pe,n=t>>1;this.e=parseInt(e,16);for(var i=new me(e,16);;){for(;this.p=new me(t-n,1,r),0!=this.p.subtract(me.ONE).gcd(i).compareTo(me.ONE)||!this.p.isProbablePrime(10););for(;this.q=new me(n,1,r),0!=this.q.subtract(me.ONE).gcd(i).compareTo(me.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(me.ONE),a=this.q.subtract(me.ONE),h=o.multiply(a);if(0==h.gcd(i).compareTo(me.ONE)){this.n=this.p.multiply(this.q),this.d=i.modInverse(h),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=Te(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){for(var r=t.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=e-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var i="";++n<r.length;){var s=255&r[n];s<128?i+=String.fromCharCode(s):s>191&&s<224?(i+=String.fromCharCode((31&s)<<6|63&r[n+1]),++n):(i+=String.fromCharCode((15&s)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return i}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var n=new Pe,i=t>>1;this.e=parseInt(e,16);var s=new me(e,16),o=this;setTimeout((function e(){var a=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var n=o.p.subtract(me.ONE),i=o.q.subtract(me.ONE),a=n.multiply(i);0==a.gcd(s).compareTo(me.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(a),o.dmp1=o.d.mod(n),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(e,0)},h=function t(){o.q=xe(),o.q.fromNumberAsync(i,1,n,(function(){o.q.subtract(me.ONE).gcda(s,(function(e){0==e.compareTo(me.ONE)&&o.q.isProbablePrime(10)?setTimeout(a,0):setTimeout(t,0)}))}))};setTimeout((function e(){o.p=xe(),o.p.fromNumberAsync(t-i,1,n,(function(){o.p.subtract(me.ONE).gcda(s,(function(t){0==t.compareTo(me.ONE)&&o.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(e,0)}))}))}),0)}),0)},t.prototype.sign=function(t,e,r){var n=function(t,e){if(e<t.length+22)return console.error("Message too long for RSA"),null;for(var r=e-t.length-6,n="",i=0;i<r;i+=2)n+="ff";return Te("0001"+n+"00"+t,16)}((je[r]||"")+e(t).toString(),this.n.bitLength()/4);if(null==n)return null;var i=this.doPrivate(n);if(null==i)return null;var s=i.toString(16);return 0==(1&s.length)?s:"0"+s},t.prototype.verify=function(t,e,r){var n=Te(e,16),i=this.doPublic(n);return null==i?null:function(t){for(var e in je)if(je.hasOwnProperty(e)){var r=je[e],n=r.length;if(t.substr(0,n)==r)return t.substr(n)}return t}(i.toString(16).replace(/^1f+00/,""))==r(t).toString()},t.prototype.encryptLong=function(t){var e=this,r="",n=(this.n.bitLength()+7>>3)-11;return this.setSplitChn(t,n).forEach((function(t){r+=e.encrypt(t)})),r},t.prototype.decryptLong=function(t){var e="",r=this.n.bitLength()+7>>3,n=2*r;if(t.length>n){for(var i=t.match(new RegExp(".{1,"+n+"}","g"))||[],s=[],o=0;o<i.length;o++){var a=Te(i[o],16),h=this.doPrivate(a);if(null==h)return null;s.push(h)}e=function(t,e){for(var r=[],n=0;n<t.length;n++){for(var i=t[n].toByteArray(),s=0;s<i.length&&0==i[s];)++s;if(i.length-s!=e-1||2!=i[s])return null;for(++s;0!=i[s];)if(++s>=i.length)return null;r=r.concat(i.slice(s+1))}for(var o=r,a=-1,h="";++a<o.length;){var u=255&o[a];u<128?h+=String.fromCharCode(u):u>191&&u<224?(h+=String.fromCharCode((31&u)<<6|63&o[a+1]),++a):(h+=String.fromCharCode((15&u)<<12|(63&o[a+1])<<6|63&o[a+2]),a+=2)}return h}(s,r)}else e=this.decrypt(t);return e},t.prototype.setSplitChn=function(t,e,r){void 0===r&&(r=[]);for(var n=t.split(""),i=0,s=0;s<n.length;s++){var o=n[s].charCodeAt(0);if((i+=o<=127?1:o<=2047?2:o<=65535?3:4)>e){var a=t.substring(0,s);return r.push(a),this.setSplitChn(t.substring(s),e,r)}}return r.push(t),r},t}(),je={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"},He={};He.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var n=function(){};if(n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var i;for(i in r)t.prototype[i]=r[i];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(i=0;i<o.length;i+=1){var r=o[i],n=e[r];"function"==typeof n&&n!=Object.prototype[r]&&(t[r]=n)}})}catch(t){}s(t.prototype,r)}}};var qe={};void 0!==qe.asn1&&qe.asn1||(qe.asn1={}),qe.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var n="",i=0;i<r;i++)n+="f";e=new me(n,16).xor(t).add(me.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=qe.asn1,r=e.DERBoolean,n=e.DERInteger,i=e.DERBitString,s=e.DEROctetString,o=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,f=e.DERPrintableString,l=e.DERTeletexString,p=e.DERIA5String,d=e.DERUTCTime,g=e.DERGeneralizedTime,v=e.DERSequence,y=e.DERSet,m=e.DERTaggedObject,_=e.ASN1Util.newObject,b=Object.keys(t);if(1!=b.length)throw"key of param shall be only one.";var w=b[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+w+":"))throw"undefined key: "+w;if("bool"==w)return new r(t[w]);if("int"==w)return new n(t[w]);if("bitstr"==w)return new i(t[w]);if("octstr"==w)return new s(t[w]);if("null"==w)return new o(t[w]);if("oid"==w)return new a(t[w]);if("enum"==w)return new h(t[w]);if("utf8str"==w)return new u(t[w]);if("numstr"==w)return new c(t[w]);if("prnstr"==w)return new f(t[w]);if("telstr"==w)return new l(t[w]);if("ia5str"==w)return new p(t[w]);if("utctime"==w)return new d(t[w]);if("gentime"==w)return new g(t[w]);if("seq"==w){for(var S=t[w],x=[],T=0;T<S.length;T++){var E=_(S[T]);x.push(E)}return new v({array:x})}if("set"==w){for(S=t[w],x=[],T=0;T<S.length;T++)E=_(S[T]),x.push(E);return new y({array:x})}if("tag"==w){var D=t[w];if("[object Array]"===Object.prototype.toString.call(D)&&3==D.length){var B=_(D[2]);return new m({tag:D[0],explicit:D[1],obj:B})}var A={};if(void 0!==D.explicit&&(A.explicit=D.explicit),void 0!==D.tag&&(A.tag=D.tag),void 0===D.obj)throw"obj shall be specified for 'tag'.";return A.obj=_(D.obj),new m(A)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},qe.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),n=(e=Math.floor(r/40)+"."+r%40,""),i=2;i<t.length;i+=2){var s=("00000000"+parseInt(t.substr(i,2),16).toString(2)).slice(-8);n+=s.substr(1,7),"0"==s.substr(0,1)&&(e=e+"."+new me(n,2).toString(10),n="")}return e},qe.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new me(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s="",o=0;o<i;o++)s+="0";for(n=s+n,o=0;o<n.length-1;o+=7){var a=n.substr(o,7);o!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var o=0;o<i.length;o++)n+=r(i[o]);return n},qe.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},qe.asn1.DERAbstractString=function(t){qe.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},He.lang.extend(qe.asn1.DERAbstractString,qe.asn1.ASN1Object),qe.asn1.DERAbstractTime=function(t){qe.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,i=this.localDateToUTC(t),s=String(i.getFullYear());"utc"==e&&(s=s.substr(2,2));var o=s+n(String(i.getMonth()+1),2)+n(String(i.getDate()),2)+n(String(i.getHours()),2)+n(String(i.getMinutes()),2)+n(String(i.getSeconds()),2);if(!0===r){var a=i.getMilliseconds();if(0!=a){var h=n(String(a),3);o=o+"."+(h=h.replace(/[0]+$/,""))}}return o+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,i,s){var o=new Date(Date.UTC(t,e-1,r,n,i,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},He.lang.extend(qe.asn1.DERAbstractTime,qe.asn1.ASN1Object),qe.asn1.DERAbstractStructured=function(t){qe.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},He.lang.extend(qe.asn1.DERAbstractStructured,qe.asn1.ASN1Object),qe.asn1.DERBoolean=function(){qe.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},He.lang.extend(qe.asn1.DERBoolean,qe.asn1.ASN1Object),qe.asn1.DERInteger=function(t){qe.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=qe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new me(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},He.lang.extend(qe.asn1.DERInteger,qe.asn1.ASN1Object),qe.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=qe.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}qe.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var n="";for(r=0;r<t.length-1;r+=8){var i=t.substr(r,8),s=parseInt(i,2).toString(16);1==s.length&&(s="0"+s),n+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+n},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},He.lang.extend(qe.asn1.DERBitString,qe.asn1.ASN1Object),qe.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=qe.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}qe.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},He.lang.extend(qe.asn1.DEROctetString,qe.asn1.DERAbstractString),qe.asn1.DERNull=function(){qe.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},He.lang.extend(qe.asn1.DERNull,qe.asn1.ASN1Object),qe.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new me(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s="",o=0;o<i;o++)s+="0";for(n=s+n,o=0;o<n.length-1;o+=7){var a=n.substr(o,7);o!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};qe.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var o=0;o<i.length;o++)n+=r(i[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(t){var e=qe.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},He.lang.extend(qe.asn1.DERObjectIdentifier,qe.asn1.ASN1Object),qe.asn1.DEREnumerated=function(t){qe.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=qe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new me(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},He.lang.extend(qe.asn1.DEREnumerated,qe.asn1.ASN1Object),qe.asn1.DERUTF8String=function(t){qe.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},He.lang.extend(qe.asn1.DERUTF8String,qe.asn1.DERAbstractString),qe.asn1.DERNumericString=function(t){qe.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},He.lang.extend(qe.asn1.DERNumericString,qe.asn1.DERAbstractString),qe.asn1.DERPrintableString=function(t){qe.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},He.lang.extend(qe.asn1.DERPrintableString,qe.asn1.DERAbstractString),qe.asn1.DERTeletexString=function(t){qe.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},He.lang.extend(qe.asn1.DERTeletexString,qe.asn1.DERAbstractString),qe.asn1.DERIA5String=function(t){qe.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},He.lang.extend(qe.asn1.DERIA5String,qe.asn1.DERAbstractString),qe.asn1.DERUTCTime=function(t){qe.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},He.lang.extend(qe.asn1.DERUTCTime,qe.asn1.DERAbstractTime),qe.asn1.DERGeneralizedTime=function(t){qe.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},He.lang.extend(qe.asn1.DERGeneralizedTime,qe.asn1.DERAbstractTime),qe.asn1.DERSequence=function(t){qe.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},He.lang.extend(qe.asn1.DERSequence,qe.asn1.DERAbstractStructured),qe.asn1.DERSet=function(t){qe.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},He.lang.extend(qe.asn1.DERSet,qe.asn1.DERAbstractStructured),qe.asn1.DERTaggedObject=function(t){qe.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},He.lang.extend(qe.asn1.DERTaggedObject,qe.asn1.ASN1Object);var Le,Fe,Ue,Ke=(Le=function(t,e){return(Le=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}Le(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),Ze=function(t){function e(r){var n=t.call(this)||this;return r&&("string"==typeof r?n.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&n.parsePropertiesFrom(r)),n}return Ke(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?function(t){var e;if(void 0===ee){var r="0123456789ABCDEF",n=" \f\n\r\t \u2028\u2029";for(ee={},e=0;e<16;++e)ee[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)ee[r.charAt(e)]=e;for(e=0;e<n.length;++e)ee[n.charAt(e)]=-1}var i=[],s=0,o=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=ee[a])){if(void 0===a)throw new Error("Illegal character at offset "+e);s|=a,++o>=2?(i[i.length]=s,s=0,o=0):s<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return i}(t):oe.unarmor(t),i=de.decode(n);if(3===i.sub.length&&(i=i.sub[2].sub[0]),9===i.sub.length){e=i.sub[1].getHexStringValue(),this.n=Te(e,16),r=i.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=i.sub[3].getHexStringValue();this.d=Te(s,16);var o=i.sub[4].getHexStringValue();this.p=Te(o,16);var a=i.sub[5].getHexStringValue();this.q=Te(a,16);var h=i.sub[6].getHexStringValue();this.dmp1=Te(h,16);var u=i.sub[7].getHexStringValue();this.dmq1=Te(u,16);var c=i.sub[8].getHexStringValue();this.coeff=Te(c,16)}else{if(2!==i.sub.length)return!1;var f=i.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=Te(e,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(t){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new qe.asn1.DERInteger({int:0}),new qe.asn1.DERInteger({bigint:this.n}),new qe.asn1.DERInteger({int:this.e}),new qe.asn1.DERInteger({bigint:this.d}),new qe.asn1.DERInteger({bigint:this.p}),new qe.asn1.DERInteger({bigint:this.q}),new qe.asn1.DERInteger({bigint:this.dmp1}),new qe.asn1.DERInteger({bigint:this.dmq1}),new qe.asn1.DERInteger({bigint:this.coeff})]};return new qe.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return ne(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new qe.asn1.DERSequence({array:[new qe.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new qe.asn1.DERNull]}),e=new qe.asn1.DERSequence({array:[new qe.asn1.DERInteger({bigint:this.n}),new qe.asn1.DERInteger({int:this.e})]}),r=new qe.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new qe.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return ne(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return(t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n")+"-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return(t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n")+"-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(Me),$e=function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Ze(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(ie(t))}catch(t){return!1}},t.prototype.encrypt=function(t){try{return ne(this.getKey().encrypt(t))}catch(t){return!1}},t.prototype.encryptLong=function(t){try{return ne(this.getKey().encryptLong(t))}catch(t){return!1}},t.prototype.decryptLong=function(t){try{return this.getKey().decryptLong(ie(t))}catch(t){return!1}},t.prototype.sign=function(t,e,r){try{return ne(this.getKey().sign(t,e,r))}catch(t){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,ie(e),r)}catch(t){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new Ze,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version="3.2.1",t}(),Ge=Ge||function(t,e){var r={},n=r.lib={},i=function(){},s=n.Base={extend:function(t){i.prototype=this;var e=new i;return t&&e.mixIn(t),e.hasOwnProperty("init")||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},o=n.WordArray=s.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||h).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes;if(t=t.sigBytes,this.clamp(),n%4)for(var i=0;i<t;i++)e[n+i>>>2]|=(r[i>>>2]>>>24-i%4*8&255)<<24-(n+i)%4*8;else if(65535<r.length)for(i=0;i<t;i+=4)e[n+i>>>2]=r[i>>>2];else e.push.apply(e,r);return this.sigBytes+=t,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=s.clone.call(this);return t.words=this.words.slice(0),t},random:function(e){for(var r=[],n=0;n<e;n+=4)r.push(4294967296*t.random()|0);return new o.init(r,e)}}),a=r.enc={},h=a.Hex={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],n=0;n<t;n++){var i=e[n>>>2]>>>24-n%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new o.init(r,e/2)}},u=a.Latin1={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],n=0;n<t;n++)r.push(String.fromCharCode(e[n>>>2]>>>24-n%4*8&255));return r.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new o.init(r,e)}},c=a.Utf8={stringify:function(t){try{return decodeURIComponent(escape(u.stringify(t)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(t){return u.parse(unescape(encodeURIComponent(t)))}},f=n.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=c.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r=this._data,n=r.words,i=r.sigBytes,s=this.blockSize,a=i/(4*s);if(e=(a=e?t.ceil(a):t.max((0|a)-this._minBufferSize,0))*s,i=t.min(4*e,i),e){for(var h=0;h<e;h+=s)this._doProcessBlock(n,h);h=n.splice(0,e),r.sigBytes-=i}return new o.init(h,i)},clone:function(){var t=s.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});n.Hasher=f.extend({cfg:s.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new l.HMAC.init(t,r).finalize(e)}}});var l=r.algo={};return r}(Math);Ue=(Fe=Ge).lib.WordArray,Fe.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp(),t=[];for(var i=0;i<r;i+=3)for(var s=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,o=0;4>o&&i+.75*o<r;o++)t.push(n.charAt(s>>>6*(3-o)&63));if(e=n.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var e=t.length,r=this._map;(n=r.charAt(64))&&-1!=(n=t.indexOf(n))&&(e=n);for(var n=[],i=0,s=0;s<e;s++)if(s%4){var o=r.indexOf(t.charAt(s-1))<<s%4*2,a=r.indexOf(t.charAt(s))>>>6-s%4*2;n[i>>>2]|=(o|a)<<24-i%4*8,i++}return Ue.create(n,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},function(t){function e(t,e,r,n,i,s,o){return((t=t+(e&r|~e&n)+i+o)<<s|t>>>32-s)+e}function r(t,e,r,n,i,s,o){return((t=t+(e&n|r&~n)+i+o)<<s|t>>>32-s)+e}function n(t,e,r,n,i,s,o){return((t=t+(e^r^n)+i+o)<<s|t>>>32-s)+e}function i(t,e,r,n,i,s,o){return((t=t+(r^(e|~n))+i+o)<<s|t>>>32-s)+e}for(var s=Ge,o=(h=s.lib).WordArray,a=h.Hasher,h=s.algo,u=[],c=0;64>c;c++)u[c]=4294967296*t.abs(t.sin(c+1))|0;h=h.MD5=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,s){for(var o=0;16>o;o++){var a=t[h=s+o];t[h]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}o=this._hash.words;var h=t[s+0],c=(a=t[s+1],t[s+2]),f=t[s+3],l=t[s+4],p=t[s+5],d=t[s+6],g=t[s+7],v=t[s+8],y=t[s+9],m=t[s+10],_=t[s+11],b=t[s+12],w=t[s+13],S=t[s+14],x=t[s+15],T=e(T=o[0],B=o[1],D=o[2],E=o[3],h,7,u[0]),E=e(E,T,B,D,a,12,u[1]),D=e(D,E,T,B,c,17,u[2]),B=e(B,D,E,T,f,22,u[3]);T=e(T,B,D,E,l,7,u[4]),E=e(E,T,B,D,p,12,u[5]),D=e(D,E,T,B,d,17,u[6]),B=e(B,D,E,T,g,22,u[7]),T=e(T,B,D,E,v,7,u[8]),E=e(E,T,B,D,y,12,u[9]),D=e(D,E,T,B,m,17,u[10]),B=e(B,D,E,T,_,22,u[11]),T=e(T,B,D,E,b,7,u[12]),E=e(E,T,B,D,w,12,u[13]),D=e(D,E,T,B,S,17,u[14]),T=r(T,B=e(B,D,E,T,x,22,u[15]),D,E,a,5,u[16]),E=r(E,T,B,D,d,9,u[17]),D=r(D,E,T,B,_,14,u[18]),B=r(B,D,E,T,h,20,u[19]),T=r(T,B,D,E,p,5,u[20]),E=r(E,T,B,D,m,9,u[21]),D=r(D,E,T,B,x,14,u[22]),B=r(B,D,E,T,l,20,u[23]),T=r(T,B,D,E,y,5,u[24]),E=r(E,T,B,D,S,9,u[25]),D=r(D,E,T,B,f,14,u[26]),B=r(B,D,E,T,v,20,u[27]),T=r(T,B,D,E,w,5,u[28]),E=r(E,T,B,D,c,9,u[29]),D=r(D,E,T,B,g,14,u[30]),T=n(T,B=r(B,D,E,T,b,20,u[31]),D,E,p,4,u[32]),E=n(E,T,B,D,v,11,u[33]),D=n(D,E,T,B,_,16,u[34]),B=n(B,D,E,T,S,23,u[35]),T=n(T,B,D,E,a,4,u[36]),E=n(E,T,B,D,l,11,u[37]),D=n(D,E,T,B,g,16,u[38]),B=n(B,D,E,T,m,23,u[39]),T=n(T,B,D,E,w,4,u[40]),E=n(E,T,B,D,h,11,u[41]),D=n(D,E,T,B,f,16,u[42]),B=n(B,D,E,T,d,23,u[43]),T=n(T,B,D,E,y,4,u[44]),E=n(E,T,B,D,b,11,u[45]),D=n(D,E,T,B,x,16,u[46]),T=i(T,B=n(B,D,E,T,c,23,u[47]),D,E,h,6,u[48]),E=i(E,T,B,D,g,10,u[49]),D=i(D,E,T,B,S,15,u[50]),B=i(B,D,E,T,p,21,u[51]),T=i(T,B,D,E,b,6,u[52]),E=i(E,T,B,D,f,10,u[53]),D=i(D,E,T,B,m,15,u[54]),B=i(B,D,E,T,a,21,u[55]),T=i(T,B,D,E,v,6,u[56]),E=i(E,T,B,D,x,10,u[57]),D=i(D,E,T,B,d,15,u[58]),B=i(B,D,E,T,w,21,u[59]),T=i(T,B,D,E,l,6,u[60]),E=i(E,T,B,D,_,10,u[61]),D=i(D,E,T,B,c,15,u[62]),B=i(B,D,E,T,y,21,u[63]),o[0]=o[0]+T|0,o[1]=o[1]+B|0,o[2]=o[2]+D|0,o[3]=o[3]+E|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var s=t.floor(n/4294967296);for(r[15+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(i+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(r.length+1),this._process(),r=(e=this._hash).words,n=0;4>n;n++)i=r[n],r[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8);return e},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}}),s.MD5=a._createHelper(h),s.HmacMD5=a._createHmacHelper(h)}(Math),function(){var t,e=Ge,r=(t=e.lib).Base,n=t.WordArray,i=(t=e.algo).EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:t.MD5,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=(a=this.cfg).hasher.create(),i=n.create(),s=i.words,o=a.keySize,a=a.iterations;s.length<o;){h&&r.update(h);var h=r.update(t).finalize(e);r.reset();for(var u=1;u<a;u++)h=r.finalize(h),r.reset();i.concat(h)}return i.sigBytes=4*o,i}});e.EvpKDF=function(t,e,r){return i.create(r).compute(t,e)}}(),Ge.lib.Cipher||function(t){var e=(p=Ge).lib,r=e.Base,n=e.WordArray,i=e.BufferedBlockAlgorithm,s=p.enc.Base64,o=p.algo.EvpKDF,a=e.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(t){return{encrypt:function(e,r,n){return("string"==typeof r?d:l).encrypt(t,e,r,n)},decrypt:function(e,r,n){return("string"==typeof r?d:l).decrypt(t,e,r,n)}}}});e.StreamCipher=a.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var h=p.mode={},u=function(t,e,r){var n=this._iv;n?this._iv=void 0:n=this._prevBlock;for(var i=0;i<r;i++)t[e+i]^=n[i]},c=(e.BlockCipherMode=r.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}})).extend();c.Encryptor=c.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize;u.call(this,t,e,n),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+n)}}),c.Decryptor=c.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=t.slice(e,e+n);r.decryptBlock(t,e),u.call(this,t,e,n),this._prevBlock=i}}),h=h.CBC=c,c=(p.pad={}).Pkcs7={pad:function(t,e){for(var r,i=(r=(r=4*e)-t.sigBytes%r)<<24|r<<16|r<<8|r,s=[],o=0;o<r;o+=4)s.push(i);r=n.create(s,r),t.concat(r)},unpad:function(t){t.sigBytes-=255&t.words[t.sigBytes-1>>>2]}},e.BlockCipher=a.extend({cfg:a.cfg.extend({mode:h,padding:c}),reset:function(){a.reset.call(this);var t=(e=this.cfg).iv,e=e.mode;if(this._xformMode==this._ENC_XFORM_MODE)var r=e.createEncryptor;else r=e.createDecryptor,this._minBufferSize=1;this._mode=r.call(e,this,t&&t.words)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var e=this._process(!0)}else e=this._process(!0),t.unpad(e);return e},blockSize:4});var f=e.CipherParams=r.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),l=(h=(p.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext;return((t=t.salt)?n.create([1398893684,1701076831]).concat(t).concat(e):e).toString(s)},parse:function(t){var e=(t=s.parse(t)).words;if(1398893684==e[0]&&1701076831==e[1]){var r=n.create(e.slice(2,4));e.splice(0,4),t.sigBytes-=16}return f.create({ciphertext:t,salt:r})}},e.SerializableCipher=r.extend({cfg:r.extend({format:h}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var i=t.createEncryptor(r,n);return e=i.finalize(e),i=i.cfg,f.create({ciphertext:e,key:r,iv:i.iv,algorithm:t,mode:i.mode,padding:i.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),t.createDecryptor(r,n).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}})),p=(p.kdf={}).OpenSSL={execute:function(t,e,r,i){return i||(i=n.random(8)),t=o.create({keySize:e+r}).compute(t,i),r=n.create(t.words.slice(e),4*r),t.sigBytes=4*e,f.create({key:t,iv:r,salt:i})}},d=e.PasswordBasedCipher=l.extend({cfg:l.cfg.extend({kdf:p}),encrypt:function(t,e,r,n){return r=(n=this.cfg.extend(n)).kdf.execute(r,t.keySize,t.ivSize),n.iv=r.iv,(t=l.encrypt.call(this,t,e,r.key,n)).mixIn(r),t},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),r=n.kdf.execute(r,t.keySize,t.ivSize,e.salt),n.iv=r.iv,l.decrypt.call(this,t,e,r.key,n)}})}(),function(){for(var t=Ge,e=t.lib.BlockCipher,r=t.algo,n=[],i=[],s=[],o=[],a=[],h=[],u=[],c=[],f=[],l=[],p=[],d=0;256>d;d++)p[d]=128>d?d<<1:d<<1^283;var g=0,v=0;for(d=0;256>d;d++){var y=(y=v^v<<1^v<<2^v<<3^v<<4)>>>8^255&y^99;n[g]=y,i[y]=g;var m=p[g],_=p[m],b=p[_],w=257*p[y]^16843008*y;s[g]=w<<24|w>>>8,o[g]=w<<16|w>>>16,a[g]=w<<8|w>>>24,h[g]=w,w=16843009*b^65537*_^257*m^16843008*g,u[y]=w<<24|w>>>8,c[y]=w<<16|w>>>16,f[y]=w<<8|w>>>24,l[y]=w,g?(g=m^p[p[p[b^m]]],v^=p[p[v]]):g=v=1}var S=[0,1,2,4,8,16,32,64,128,27,54];r=r.AES=e.extend({_doReset:function(){for(var t=(r=this._key).words,e=r.sigBytes/4,r=4*((this._nRounds=e+6)+1),i=this._keySchedule=[],s=0;s<r;s++)if(s<e)i[s]=t[s];else{var o=i[s-1];s%e?6<e&&4==s%e&&(o=n[o>>>24]<<24|n[o>>>16&255]<<16|n[o>>>8&255]<<8|n[255&o]):(o=n[(o=o<<8|o>>>24)>>>24]<<24|n[o>>>16&255]<<16|n[o>>>8&255]<<8|n[255&o],o^=S[s/e|0]<<24),i[s]=i[s-e]^o}for(t=this._invKeySchedule=[],e=0;e<r;e++)s=r-e,o=e%4?i[s]:i[s-4],t[e]=4>e||4>=s?o:u[n[o>>>24]]^c[n[o>>>16&255]]^f[n[o>>>8&255]]^l[n[255&o]]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,s,o,a,h,n)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,u,c,f,l,i),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,i,s,o,a){for(var h=this._nRounds,u=t[e]^r[0],c=t[e+1]^r[1],f=t[e+2]^r[2],l=t[e+3]^r[3],p=4,d=1;d<h;d++){var g=n[u>>>24]^i[c>>>16&255]^s[f>>>8&255]^o[255&l]^r[p++],v=n[c>>>24]^i[f>>>16&255]^s[l>>>8&255]^o[255&u]^r[p++],y=n[f>>>24]^i[l>>>16&255]^s[u>>>8&255]^o[255&c]^r[p++];l=n[l>>>24]^i[u>>>16&255]^s[c>>>8&255]^o[255&f]^r[p++],u=g,c=v,f=y}g=(a[u>>>24]<<24|a[c>>>16&255]<<16|a[f>>>8&255]<<8|a[255&l])^r[p++],v=(a[c>>>24]<<24|a[f>>>16&255]<<16|a[l>>>8&255]<<8|a[255&u])^r[p++],y=(a[f>>>24]<<24|a[l>>>16&255]<<16|a[u>>>8&255]<<8|a[255&c])^r[p++],l=(a[l>>>24]<<24|a[u>>>16&255]<<16|a[c>>>8&255]<<8|a[255&f])^r[p++],t[e]=g,t[e+1]=v,t[e+2]=y,t[e+3]=l},keySize:8}),t.AES=e._createHelper(r)}();var We={exports:{}}.exports=function t(e,r,n){function i(o,a){if(!r[o]){if(!e[o]){if(!a&&f)return f(o);if(s)return s(o,!0);var h=new Error("Cannot find module '"+o+"'");throw h.code="MODULE_NOT_FOUND",h}var u=r[o]={exports:{}};e[o][0].call(u.exports,(function(t){return i(e[o][1][t]||t)}),u,u.exports,t,e,r,n)}return r[o].exports}for(var s=f,o=0;o<n.length;o++)i(n[o]);return i}({1:[function(t,r,n){var i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;n.assign=function(t){for(var r,n,i=Array.prototype.slice.call(arguments,1);i.length;){var s=i.shift();if(s){if("object"!=(void 0===s?"undefined":e(s)))throw new TypeError(s+"must be non-object");for(var o in s)r=s,n=o,Object.prototype.hasOwnProperty.call(r,n)&&(t[o]=s[o])}}return t},n.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var s={arraySet:function(t,e,r,n,i){if(e.subarray&&t.subarray)t.set(e.subarray(r,r+n),i);else for(var s=0;s<n;s++)t[i+s]=e[r+s]},flattenChunks:function(t){var e,r,n,i,s,o;for(e=n=0,r=t.length;e<r;e++)n+=t[e].length;for(o=new Uint8Array(n),e=i=0,r=t.length;e<r;e++)s=t[e],o.set(s,i),i+=s.length;return o}},o={arraySet:function(t,e,r,n,i){for(var s=0;s<n;s++)t[i+s]=e[r+s]},flattenChunks:function(t){return[].concat.apply([],t)}};n.setTyped=function(t){t?(n.Buf8=Uint8Array,n.Buf16=Uint16Array,n.Buf32=Int32Array,n.assign(n,s)):(n.Buf8=Array,n.Buf16=Array,n.Buf32=Array,n.assign(n,o))},n.setTyped(i)},{}],2:[function(t,e,r){var n=t("./common"),i=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(t){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){s=!1}for(var o=new n.Buf8(256),a=0;a<256;a++)o[a]=252<=a?6:248<=a?5:240<=a?4:224<=a?3:192<=a?2:1;function h(t,e){if(e<65534&&(t.subarray&&s||!t.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(t,e));for(var r="",o=0;o<e;o++)r+=String.fromCharCode(t[o]);return r}o[254]=o[254]=1,r.string2buf=function(t){var e,r,i,s,o,a=t.length,h=0;for(s=0;s<a;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<a&&56320==(64512&(i=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(i-56320),s++),h+=r<128?1:r<2048?2:r<65536?3:4;for(e=new n.Buf8(h),s=o=0;o<h;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<a&&56320==(64512&(i=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(i-56320),s++),r<128?e[o++]=r:(r<2048?e[o++]=192|r>>>6:(r<65536?e[o++]=224|r>>>12:(e[o++]=240|r>>>18,e[o++]=128|r>>>12&63),e[o++]=128|r>>>6&63),e[o++]=128|63&r);return e},r.buf2binstring=function(t){return h(t,t.length)},r.binstring2buf=function(t){for(var e=new n.Buf8(t.length),r=0,i=e.length;r<i;r++)e[r]=t.charCodeAt(r);return e},r.buf2string=function(t,e){var r,n,i,s,a=e||t.length,u=new Array(2*a);for(r=n=0;r<a;)if((i=t[r++])<128)u[n++]=i;else if(4<(s=o[i]))u[n++]=65533,r+=s-1;else{for(i&=2===s?31:3===s?15:7;1<s&&r<a;)i=i<<6|63&t[r++],s--;1<s?u[n++]=65533:i<65536?u[n++]=i:(i-=65536,u[n++]=55296|i>>10&1023,u[n++]=56320|1023&i)}return h(u,n)},r.utf8border=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;0<=r&&128==(192&t[r]);)r--;return r<0||0===r?e:r+o[t[r]]>e?r:e}},{"./common":1}],3:[function(t,e,r){e.exports=function(t,e,r,n){for(var i=65535&t|0,s=t>>>16&65535|0,o=0;0!==r;){for(r-=o=2e3<r?2e3:r;s=s+(i=i+e[n++]|0)|0,--o;);i%=65521,s%=65521}return i|s<<16|0}},{}],4:[function(t,e,r){var n=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e}();e.exports=function(t,e,r,i){var s=n,o=i+r;t^=-1;for(var a=i;a<o;a++)t=t>>>8^s[255&(t^e[a])];return-1^t}},{}],5:[function(t,e,r){var n,i=t("../utils/common"),s=t("./trees"),o=t("./adler32"),a=t("./crc32"),h=t("./messages"),u=-2,c=258,f=262,l=113;function p(t,e){return t.msg=h[e],e}function d(t){return(t<<1)-(4<t?9:0)}function g(t){for(var e=t.length;0<=--e;)t[e]=0}function v(t){var e=t.state,r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(i.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))}function y(t,e){s._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,v(t.strm)}function m(t,e){t.pending_buf[t.pending++]=e}function _(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function b(t,e){var r,n,i=t.max_chain_length,s=t.strstart,o=t.prev_length,a=t.nice_match,h=t.strstart>t.w_size-f?t.strstart-(t.w_size-f):0,u=t.window,l=t.w_mask,p=t.prev,d=t.strstart+c,g=u[s+o-1],v=u[s+o];t.prev_length>=t.good_match&&(i>>=2),a>t.lookahead&&(a=t.lookahead);do{if(u[(r=e)+o]===v&&u[r+o-1]===g&&u[r]===u[s]&&u[++r]===u[s+1]){s+=2,r++;do{}while(u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&s<d);if(n=c-(d-s),s=d-c,o<n){if(t.match_start=e,a<=(o=n))break;g=u[s+o-1],v=u[s+o]}}}while((e=p[e&l])>h&&0!=--i);return o<=t.lookahead?o:t.lookahead}function w(t){var e,r,n,s,h,u,c,l,p,d,g=t.w_size;do{if(s=t.window_size-t.lookahead-t.strstart,t.strstart>=g+(g-f)){for(i.arraySet(t.window,t.window,g,g,0),t.match_start-=g,t.strstart-=g,t.block_start-=g,e=r=t.hash_size;n=t.head[--e],t.head[e]=g<=n?n-g:0,--r;);for(e=r=g;n=t.prev[--e],t.prev[e]=g<=n?n-g:0,--r;);s+=g}if(0===t.strm.avail_in)break;if(u=t.strm,c=t.window,l=t.strstart+t.lookahead,d=void 0,(p=s)<(d=u.avail_in)&&(d=p),r=0===d?0:(u.avail_in-=d,i.arraySet(c,u.input,u.next_in,d,l),1===u.state.wrap?u.adler=o(u.adler,c,d,l):2===u.state.wrap&&(u.adler=a(u.adler,c,d,l)),u.next_in+=d,u.total_in+=d,d),t.lookahead+=r,t.lookahead+t.insert>=3)for(h=t.strstart-t.insert,t.ins_h=t.window[h],t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+3-1])&t.hash_mask,t.prev[h&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=h,h++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<f&&0!==t.strm.avail_in)}function S(t,e){for(var r,n;;){if(t.lookahead<f){if(w(t),t.lookahead<f&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-f&&(t.match_length=b(t,r)),t.match_length>=3)if(n=s._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(y(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(y(t,!1),0===t.strm.avail_out)?1:2}function x(t,e){for(var r,n,i;;){if(t.lookahead<f){if(w(t),t.lookahead<f&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-f&&(t.match_length=b(t,r),t.match_length<=5&&(1===t.strategy||3===t.match_length&&4096<t.strstart-t.match_start)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){for(i=t.strstart+t.lookahead-3,n=s._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=i&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(y(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((n=s._tr_tally(t,0,t.window[t.strstart-1]))&&y(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=s._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(y(t,!1),0===t.strm.avail_out)?1:2}function T(t,e,r,n,i){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=i}function E(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(1146),this.dyn_dtree=new i.Buf16(122),this.bl_tree=new i.Buf16(78),g(this.dyn_ltree),g(this.dyn_dtree),g(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(16),this.heap=new i.Buf16(573),g(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(573),g(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function D(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:l,t.adler=2===e.wrap?0:1,e.last_flush=0,s._tr_init(e),0):p(t,u)}function B(t){var e,r=D(t);return 0===r&&((e=t.state).window_size=2*e.w_size,g(e.head),e.max_lazy_match=n[e.level].max_lazy,e.good_match=n[e.level].good_length,e.nice_match=n[e.level].nice_length,e.max_chain_length=n[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),r}function A(t,e,r,n,s,o){if(!t)return u;var a=1;if(-1===e&&(e=6),n<0?(a=0,n=-n):15<n&&(a=2,n-=16),s<1||9<s||8!==r||n<8||15<n||e<0||9<e||o<0||4<o)return p(t,u);8===n&&(n=9);var h=new E;return(t.state=h).strm=t,h.wrap=a,h.gzhead=null,h.w_bits=n,h.w_size=1<<h.w_bits,h.w_mask=h.w_size-1,h.hash_bits=s+7,h.hash_size=1<<h.hash_bits,h.hash_mask=h.hash_size-1,h.hash_shift=~~((h.hash_bits+3-1)/3),h.window=new i.Buf8(2*h.w_size),h.head=new i.Buf16(h.hash_size),h.prev=new i.Buf16(h.w_size),h.lit_bufsize=1<<s+6,h.pending_buf_size=4*h.lit_bufsize,h.pending_buf=new i.Buf8(h.pending_buf_size),h.d_buf=1*h.lit_bufsize,h.l_buf=3*h.lit_bufsize,h.level=e,h.strategy=o,h.method=r,B(t)}n=[new T(0,0,0,0,(function(t,e){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(w(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+r;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,y(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-f&&(y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(y(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(y(t,!1),t.strm.avail_out),1)})),new T(4,4,8,4,S),new T(4,5,16,8,S),new T(4,6,32,32,S),new T(4,4,16,16,x),new T(8,16,32,32,x),new T(8,16,128,128,x),new T(8,32,128,256,x),new T(32,128,258,1024,x),new T(32,258,258,4096,x)],r.deflateInit=function(t,e){return A(t,e,8,15,8,0)},r.deflateInit2=A,r.deflateReset=B,r.deflateResetKeep=D,r.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?u:(t.state.gzhead=e,0):u},r.deflate=function(t,e){var r,i,o,h;if(!t||!t.state||5<e||e<0)return t?p(t,u):u;if(i=t.state,!t.output||!t.input&&0!==t.avail_in||666===i.status&&4!==e)return p(t,0===t.avail_out?-5:u);if(i.strm=t,r=i.last_flush,i.last_flush=e,42===i.status)if(2===i.wrap)t.adler=0,m(i,31),m(i,139),m(i,8),i.gzhead?(m(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),m(i,255&i.gzhead.time),m(i,i.gzhead.time>>8&255),m(i,i.gzhead.time>>16&255),m(i,i.gzhead.time>>24&255),m(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),m(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(m(i,255&i.gzhead.extra.length),m(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(t.adler=a(t.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=69):(m(i,0),m(i,0),m(i,0),m(i,0),m(i,0),m(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),m(i,3),i.status=l);else{var f=8+(i.w_bits-8<<4)<<8;f|=(2<=i.strategy||i.level<2?0:i.level<6?1:6===i.level?2:3)<<6,0!==i.strstart&&(f|=32),f+=31-f%31,i.status=l,_(i,f),0!==i.strstart&&(_(i,t.adler>>>16),_(i,65535&t.adler)),t.adler=1}if(69===i.status)if(i.gzhead.extra){for(o=i.pending;i.gzindex<(65535&i.gzhead.extra.length)&&(i.pending!==i.pending_buf_size||(i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),v(t),o=i.pending,i.pending!==i.pending_buf_size));)m(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++;i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=73)}else i.status=73;if(73===i.status)if(i.gzhead.name){o=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),v(t),o=i.pending,i.pending===i.pending_buf_size)){h=1;break}m(i,h=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0)}while(0!==h);i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),0===h&&(i.gzindex=0,i.status=91)}else i.status=91;if(91===i.status)if(i.gzhead.comment){o=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),v(t),o=i.pending,i.pending===i.pending_buf_size)){h=1;break}m(i,h=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0)}while(0!==h);i.gzhead.hcrc&&i.pending>o&&(t.adler=a(t.adler,i.pending_buf,i.pending-o,o)),0===h&&(i.status=103)}else i.status=103;if(103===i.status&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&v(t),i.pending+2<=i.pending_buf_size&&(m(i,255&t.adler),m(i,t.adler>>8&255),t.adler=0,i.status=l)):i.status=l),0!==i.pending){if(v(t),0===t.avail_out)return i.last_flush=-1,0}else if(0===t.avail_in&&d(e)<=d(r)&&4!==e)return p(t,-5);if(666===i.status&&0!==t.avail_in)return p(t,-5);if(0!==t.avail_in||0!==i.lookahead||0!==e&&666!==i.status){var b=2===i.strategy?function(t,e){for(var r;;){if(0===t.lookahead&&(w(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(y(t,!1),0===t.strm.avail_out)?1:2}(i,e):3===i.strategy?function(t,e){for(var r,n,i,o,a=t.window;;){if(t.lookahead<=c){if(w(t),t.lookahead<=c&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&0<t.strstart&&(n=a[i=t.strstart-1])===a[++i]&&n===a[++i]&&n===a[++i]){o=t.strstart+c;do{}while(n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&i<o);t.match_length=c-(o-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(r=s._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(y(t,!1),0===t.strm.avail_out)?1:2}(i,e):n[i.level].func(i,e);if(3!==b&&4!==b||(i.status=666),1===b||3===b)return 0===t.avail_out&&(i.last_flush=-1),0;if(2===b&&(1===e?s._tr_align(i):5!==e&&(s._tr_stored_block(i,0,0,!1),3===e&&(g(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),v(t),0===t.avail_out))return i.last_flush=-1,0}return 4!==e?0:i.wrap<=0?1:(2===i.wrap?(m(i,255&t.adler),m(i,t.adler>>8&255),m(i,t.adler>>16&255),m(i,t.adler>>24&255),m(i,255&t.total_in),m(i,t.total_in>>8&255),m(i,t.total_in>>16&255),m(i,t.total_in>>24&255)):(_(i,t.adler>>>16),_(i,65535&t.adler)),v(t),0<i.wrap&&(i.wrap=-i.wrap),0!==i.pending?0:1)},r.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&e!==l&&666!==e?p(t,u):(t.state=null,e===l?p(t,-3):0):u},r.deflateSetDictionary=function(t,e){var r,n,s,a,h,c,f,l,p=e.length;if(!t||!t.state)return u;if(2===(a=(r=t.state).wrap)||1===a&&42!==r.status||r.lookahead)return u;for(1===a&&(t.adler=o(t.adler,e,p,0)),r.wrap=0,p>=r.w_size&&(0===a&&(g(r.head),r.strstart=0,r.block_start=0,r.insert=0),l=new i.Buf8(r.w_size),i.arraySet(l,e,p-r.w_size,r.w_size,0),e=l,p=r.w_size),h=t.avail_in,c=t.next_in,f=t.input,t.avail_in=p,t.next_in=0,t.input=e,w(r);r.lookahead>=3;){for(n=r.strstart,s=r.lookahead-2;r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+3-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--s;);r.strstart=n,r.lookahead=2,w(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,t.next_in=c,t.input=f,t.avail_in=h,r.wrap=a,0},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":1,"./adler32":3,"./crc32":4,"./messages":6,"./trees":7}],6:[function(t,e,r){e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],7:[function(t,e,r){var n=t("../utils/common");function i(t){for(var e=t.length;0<=--e;)t[e]=0}var s=256,o=286,a=30,h=15,u=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],c=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],l=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],p=new Array(576);i(p);var d=new Array(60);i(d);var g=new Array(512);i(g);var v=new Array(256);i(v);var y=new Array(29);i(y);var m,_,b,w=new Array(a);function S(t,e,r,n,i){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=t&&t.length}function x(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function T(t){return t<256?g[t]:g[256+(t>>>7)]}function E(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function D(t,e,r){t.bi_valid>16-r?(t.bi_buf|=e<<t.bi_valid&65535,E(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=r-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function B(t,e,r){D(t,r[2*e],r[2*e+1])}function A(t,e){for(var r=0;r|=1&t,t>>>=1,r<<=1,0<--e;);return r>>>1}function k(t,e,r){var n,i,s=new Array(16),o=0;for(n=1;n<=h;n++)s[n]=o=o+r[n-1]<<1;for(i=0;i<=e;i++){var a=t[2*i+1];0!==a&&(t[2*i]=A(s[a]++,a))}}function O(t){var e;for(e=0;e<o;e++)t.dyn_ltree[2*e]=0;for(e=0;e<a;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function z(t){8<t.bi_valid?E(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function I(t,e,r,n){var i=2*e,s=2*r;return t[i]<t[s]||t[i]===t[s]&&n[e]<=n[r]}function R(t,e,r){for(var n=t.heap[r],i=r<<1;i<=t.heap_len&&(i<t.heap_len&&I(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!I(e,n,t.heap[i],t.depth));)t.heap[r]=t.heap[i],r=i,i<<=1;t.heap[r]=n}function C(t,e,r){var n,i,o,a,h=0;if(0!==t.last_lit)for(;n=t.pending_buf[t.d_buf+2*h]<<8|t.pending_buf[t.d_buf+2*h+1],i=t.pending_buf[t.l_buf+h],h++,0===n?B(t,i,e):(B(t,(o=v[i])+s+1,e),0!==(a=u[o])&&D(t,i-=y[o],a),B(t,o=T(--n),r),0!==(a=c[o])&&D(t,n-=w[o],a)),h<t.last_lit;);B(t,256,e)}function N(t,e){var r,n,i,s=e.dyn_tree,o=e.stat_desc.static_tree,a=e.stat_desc.has_stree,u=e.stat_desc.elems,c=-1;for(t.heap_len=0,t.heap_max=573,r=0;r<u;r++)0!==s[2*r]?(t.heap[++t.heap_len]=c=r,t.depth[r]=0):s[2*r+1]=0;for(;t.heap_len<2;)s[2*(i=t.heap[++t.heap_len]=c<2?++c:0)]=1,t.depth[i]=0,t.opt_len--,a&&(t.static_len-=o[2*i+1]);for(e.max_code=c,r=t.heap_len>>1;1<=r;r--)R(t,s,r);for(i=u;r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],R(t,s,1),n=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=n,s[2*i]=s[2*r]+s[2*n],t.depth[i]=(t.depth[r]>=t.depth[n]?t.depth[r]:t.depth[n])+1,s[2*r+1]=s[2*n+1]=i,t.heap[1]=i++,R(t,s,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t,e){var r,n,i,s,o,a,u=e.dyn_tree,c=e.max_code,f=e.stat_desc.static_tree,l=e.stat_desc.has_stree,p=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,g=e.stat_desc.max_length,v=0;for(s=0;s<=h;s++)t.bl_count[s]=0;for(u[2*t.heap[t.heap_max]+1]=0,r=t.heap_max+1;r<573;r++)g<(s=u[2*u[2*(n=t.heap[r])+1]+1]+1)&&(s=g,v++),u[2*n+1]=s,c<n||(t.bl_count[s]++,o=0,d<=n&&(o=p[n-d]),a=u[2*n],t.opt_len+=a*(s+o),l&&(t.static_len+=a*(f[2*n+1]+o)));if(0!==v){do{for(s=g-1;0===t.bl_count[s];)s--;t.bl_count[s]--,t.bl_count[s+1]+=2,t.bl_count[g]--,v-=2}while(0<v);for(s=g;0!==s;s--)for(n=t.bl_count[s];0!==n;)c<(i=t.heap[--r])||(u[2*i+1]!==s&&(t.opt_len+=(s-u[2*i+1])*u[2*i],u[2*i+1]=s),n--)}}(t,e),k(s,c,t.bl_count)}function V(t,e,r){var n,i,s=-1,o=e[1],a=0,h=7,u=4;for(0===o&&(h=138,u=3),e[2*(r+1)+1]=65535,n=0;n<=r;n++)i=o,o=e[2*(n+1)+1],++a<h&&i===o||(a<u?t.bl_tree[2*i]+=a:0!==i?(i!==s&&t.bl_tree[2*i]++,t.bl_tree[32]++):a<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=i,(a=0)===o?(h=138,u=3):i===o?(h=6,u=3):(h=7,u=4))}function P(t,e,r){var n,i,s=-1,o=e[1],a=0,h=7,u=4;for(0===o&&(h=138,u=3),n=0;n<=r;n++)if(i=o,o=e[2*(n+1)+1],!(++a<h&&i===o)){if(a<u)for(;B(t,i,t.bl_tree),0!=--a;);else 0!==i?(i!==s&&(B(t,i,t.bl_tree),a--),B(t,16,t.bl_tree),D(t,a-3,2)):a<=10?(B(t,17,t.bl_tree),D(t,a-3,3)):(B(t,18,t.bl_tree),D(t,a-11,7));s=i,(a=0)===o?(h=138,u=3):i===o?(h=6,u=3):(h=7,u=4)}}i(w);var M=!1;function j(t,e,r,i){var s,o,a;D(t,0+(i?1:0),3),o=e,a=r,z(s=t),E(s,a),E(s,~a),n.arraySet(s.pending_buf,s.window,o,a,s.pending),s.pending+=a}r._tr_init=function(t){M||(function(){var t,e,r,n,i,s=new Array(16);for(n=r=0;n<28;n++)for(y[n]=r,t=0;t<1<<u[n];t++)v[r++]=n;for(v[r-1]=n,n=i=0;n<16;n++)for(w[n]=i,t=0;t<1<<c[n];t++)g[i++]=n;for(i>>=7;n<a;n++)for(w[n]=i<<7,t=0;t<1<<c[n]-7;t++)g[256+i++]=n;for(e=0;e<=h;e++)s[e]=0;for(t=0;t<=143;)p[2*t+1]=8,t++,s[8]++;for(;t<=255;)p[2*t+1]=9,t++,s[9]++;for(;t<=279;)p[2*t+1]=7,t++,s[7]++;for(;t<=287;)p[2*t+1]=8,t++,s[8]++;for(k(p,287,s),t=0;t<a;t++)d[2*t+1]=5,d[2*t]=A(t,5);m=new S(p,u,257,o,h),_=new S(d,c,0,a,h),b=new S(new Array(0),f,0,19,7)}(),M=!0),t.l_desc=new x(t.dyn_ltree,m),t.d_desc=new x(t.dyn_dtree,_),t.bl_desc=new x(t.bl_tree,b),t.bi_buf=0,t.bi_valid=0,O(t)},r._tr_stored_block=j,r._tr_flush_block=function(t,e,r,n){var i,o,a=0;0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<s;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),N(t,t.l_desc),N(t,t.d_desc),a=function(t){var e;for(V(t,t.dyn_ltree,t.l_desc.max_code),V(t,t.dyn_dtree,t.d_desc.max_code),N(t,t.bl_desc),e=18;3<=e&&0===t.bl_tree[2*l[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(o=t.static_len+3+7>>>3)<=i&&(i=o)):i=o=r+5,r+4<=i&&-1!==e?j(t,e,r,n):4===t.strategy||o===i?(D(t,2+(n?1:0),3),C(t,p,d)):(D(t,4+(n?1:0),3),function(t,e,r,n){var i;for(D(t,e-257,5),D(t,r-1,5),D(t,n-4,4),i=0;i<n;i++)D(t,t.bl_tree[2*l[i]+1],3);P(t,t.dyn_ltree,e-1),P(t,t.dyn_dtree,r-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),C(t,t.dyn_ltree,t.dyn_dtree)),O(t),n&&z(t)},r._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(v[r]+s+1)]++,t.dyn_dtree[2*T(e)]++),t.last_lit===t.lit_bufsize-1},r._tr_align=function(t){var e;D(t,2,3),B(t,256,p),16===(e=t).bi_valid?(E(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":1}],8:[function(t,e,r){e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],"/lib/deflate.js":[function(t,e,r){var n=t("./zlib/deflate"),i=t("./utils/common"),s=t("./utils/strings"),o=t("./zlib/messages"),a=t("./zlib/zstream"),h=Object.prototype.toString;function u(t){if(!(this instanceof u))return new u(t);this.options=i.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e=this.options;e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;var r=n.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(0!==r)throw new Error(o[r]);if(e.header&&n.deflateSetHeader(this.strm,e.header),e.dictionary){var c;if(c="string"==typeof e.dictionary?s.string2buf(e.dictionary):"[object ArrayBuffer]"===h.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,0!==(r=n.deflateSetDictionary(this.strm,c)))throw new Error(o[r]);this._dict_set=!0}}function c(t,e){var r=new u(e);if(r.push(t,!0),r.err)throw r.msg||o[r.err];return r.result}u.prototype.push=function(t,e){var r,o,a=this.strm,u=this.options.chunkSize;if(this.ended)return!1;o=e===~~e?e:!0===e?4:0,"string"==typeof t?a.input=s.string2buf(t):"[object ArrayBuffer]"===h.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;do{if(0===a.avail_out&&(a.output=new i.Buf8(u),a.next_out=0,a.avail_out=u),1!==(r=n.deflate(a,o))&&0!==r)return this.onEnd(r),!(this.ended=!0);0!==a.avail_out&&(0!==a.avail_in||4!==o&&2!==o)||("string"===this.options.to?this.onData(s.buf2binstring(i.shrinkBuf(a.output,a.next_out))):this.onData(i.shrinkBuf(a.output,a.next_out)))}while((0<a.avail_in||0===a.avail_out)&&1!==r);return 4===o?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):2!==o||(this.onEnd(0),!(a.avail_out=0))},u.prototype.onData=function(t){this.chunks.push(t)},u.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Deflate=u,r.deflate=c,r.deflateRaw=function(t,e){return(e=e||{}).raw=!0,c(t,e)},r.gzip=function(t,e){return(e=e||{}).gzip=!0,c(t,e)}},{"./utils/common":1,"./utils/strings":2,"./zlib/deflate":5,"./zlib/messages":6,"./zlib/zstream":8}]},{},[])("/lib/deflate.js");function Je(t){this.message=t}function Xe(t){for(var e,r,n=String(t),i=0,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o="";n.charAt(0|i)||(s="=",i%1);o+=s.charAt(63&e>>8-i%1*8)){if((r=n.charCodeAt(i+=3/4))>255)throw new Je("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");e=e<<8|r}return o}function Ye(t,e){var r=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(r>>16)<<16|65535&r}function Qe(t,e,r,n,i,s){return Ye((o=Ye(Ye(e,t),Ye(n,s)))<<(a=i)|o>>>32-a,r);var o,a}function tr(t,e,r,n,i,s,o){return Qe(e&r|~e&n,t,e,i,s,o)}function er(t,e,r,n,i,s,o){return Qe(e&n|r&~n,t,e,i,s,o)}function rr(t,e,r,n,i,s,o){return Qe(e^r^n,t,e,i,s,o)}function nr(t,e,r,n,i,s,o){return Qe(r^(e|~n),t,e,i,s,o)}function ir(t,e){var r=void 0,n=void 0,i=void 0,s=void 0,o=void 0;t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var a=1732584193,h=-271733879,u=-1732584194,c=271733878;for(r=0;r<t.length;r+=16)n=a,i=h,s=u,o=c,a=tr(a,h,u,c,t[r],7,-680876936),c=tr(c,a,h,u,t[r+1],12,-389564586),u=tr(u,c,a,h,t[r+2],17,606105819),h=tr(h,u,c,a,t[r+3],22,-1044525330),a=tr(a,h,u,c,t[r+4],7,-176418897),c=tr(c,a,h,u,t[r+5],12,1200080426),u=tr(u,c,a,h,t[r+6],17,-1473231341),h=tr(h,u,c,a,t[r+7],22,-45705983),a=tr(a,h,u,c,t[r+8],7,1770035416),c=tr(c,a,h,u,t[r+9],12,-1958414417),u=tr(u,c,a,h,t[r+10],17,-42063),h=tr(h,u,c,a,t[r+11],22,-1990404162),a=tr(a,h,u,c,t[r+12],7,1804603682),c=tr(c,a,h,u,t[r+13],12,-40341101),u=tr(u,c,a,h,t[r+14],17,-1502002290),a=er(a,h=tr(h,u,c,a,t[r+15],22,1236535329),u,c,t[r+1],5,-165796510),c=er(c,a,h,u,t[r+6],9,-1069501632),u=er(u,c,a,h,t[r+11],14,643717713),h=er(h,u,c,a,t[r],20,-373897302),a=er(a,h,u,c,t[r+5],5,-701558691),c=er(c,a,h,u,t[r+10],9,38016083),u=er(u,c,a,h,t[r+15],14,-660478335),h=er(h,u,c,a,t[r+4],20,-405537848),a=er(a,h,u,c,t[r+9],5,568446438),c=er(c,a,h,u,t[r+14],9,-1019803690),u=er(u,c,a,h,t[r+3],14,-187363961),h=er(h,u,c,a,t[r+8],20,1163531501),a=er(a,h,u,c,t[r+13],5,-1444681467),c=er(c,a,h,u,t[r+2],9,-51403784),u=er(u,c,a,h,t[r+7],14,1735328473),a=rr(a,h=er(h,u,c,a,t[r+12],20,-1926607734),u,c,t[r+5],4,-378558),c=rr(c,a,h,u,t[r+8],11,-2022574463),u=rr(u,c,a,h,t[r+11],16,1839030562),h=rr(h,u,c,a,t[r+14],23,-35309556),a=rr(a,h,u,c,t[r+1],4,-1530992060),c=rr(c,a,h,u,t[r+4],11,1272893353),u=rr(u,c,a,h,t[r+7],16,-155497632),h=rr(h,u,c,a,t[r+10],23,-1094730640),a=rr(a,h,u,c,t[r+13],4,681279174),c=rr(c,a,h,u,t[r],11,-358537222),u=rr(u,c,a,h,t[r+3],16,-722521979),h=rr(h,u,c,a,t[r+6],23,76029189),a=rr(a,h,u,c,t[r+9],4,-640364487),c=rr(c,a,h,u,t[r+12],11,-421815835),u=rr(u,c,a,h,t[r+15],16,530742520),a=nr(a,h=rr(h,u,c,a,t[r+2],23,-995338651),u,c,t[r],6,-198630844),c=nr(c,a,h,u,t[r+7],10,1126891415),u=nr(u,c,a,h,t[r+14],15,-1416354905),h=nr(h,u,c,a,t[r+5],21,-57434055),a=nr(a,h,u,c,t[r+12],6,1700485571),c=nr(c,a,h,u,t[r+3],10,-1894986606),u=nr(u,c,a,h,t[r+10],15,-1051523),h=nr(h,u,c,a,t[r+1],21,-2054922799),a=nr(a,h,u,c,t[r+8],6,1873313359),c=nr(c,a,h,u,t[r+15],10,-30611744),u=nr(u,c,a,h,t[r+6],15,-1560198380),h=nr(h,u,c,a,t[r+13],21,1309151649),a=nr(a,h,u,c,t[r+4],6,-145523070),c=nr(c,a,h,u,t[r+11],10,-1120210379),u=nr(u,c,a,h,t[r+2],15,718787259),h=nr(h,u,c,a,t[r+9],21,-343485551),a=Ye(a,n),h=Ye(h,i),u=Ye(u,s),c=Ye(c,o);return[a,h,u,c]}function sr(t){var e=void 0,r="",n=32*t.length;for(e=0;e<n;e+=8)r+=String.fromCharCode(t[e>>5]>>>e%32&255);return r}function or(t){var e=void 0,r=[];for(r[(t.length>>2)-1]=void 0,e=0;e<r.length;e+=1)r[e]=0;var n=8*t.length;for(e=0;e<n;e+=8)r[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return r}function ar(t){var e=void 0,r=void 0,n="0123456789abcdef",i="";for(r=0;r<t.length;r+=1)e=t.charCodeAt(r),i+=n.charAt(e>>>4&15)+n.charAt(15&e);return i}function hr(t){return unescape(encodeURIComponent(t))}function ur(t){return function(t){return sr(ir(or(t),8*t.length))}(hr(t))}function cr(t,e){return function(t,e){var r,n=void 0,i=or(t),s=[],o=[];for(s[15]=o[15]=void 0,i.length>16&&(i=ir(i,8*t.length)),n=0;n<16;n+=1)s[n]=909522486^i[n],o[n]=1549556828^i[n];return r=ir(s.concat(or(e)),512+8*e.length),sr(ir(o.concat(r),640))}(hr(t),hr(e))}function fr(t,e,r){return e?r?cr(e,t):ar(cr(e,t)):r?ur(t):ar(ur(t))}function lr(t,e){try{o.setStorage({key:t,data:e})}catch(t){}}function pr(t){try{return s?o.getStorageSync({key:t}).data:o.getStorageSync(t)}catch(t){}}function dr(t,e){try{o.getFileSystemManager().writeFile({filePath:"".concat(o.env.USER_DATA_PATH,"/").concat(t,".txt"),data:e,encoding:"utf8",success:function(){},fail:function(){}})}catch(t){}}function gr(t){try{var e="".concat(o.env.USER_DATA_PATH,"/").concat(t,".txt"),r=o.getFileSystemManager().readFileSync(e,"utf8");return s?r.data:r}catch(t){}}function vr(t){Zt.setSmInfo("SMID",t);var e=Zt.smInfo.storageName;lr(e,t),dr(e,t)}function yr(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}function mr(t){return fr(function t(e){if(R(e)){var r="";return Object.keys(e).sort().forEach((function(n){r+=t(e[n])})),r}return e?e.toString().replace(/,/g,""):""}(t))}function _r(){var t="SMFP",e="smidV2",r=pr(e)||gr(t);if(r)return r;var n=function(){var t,e,r,n,i,s,o=(t=new Date).getFullYear().toString()+(e=(e=(t.getMonth()+1).toString())<=9?"0"+e:e)+(r=(r=t.getDate().toString())<=9?"0"+r:r)+(n=(n=t.getHours().toString())<=9?"0"+n:n)+(i=(i=t.getMinutes().toString())<=9?"0"+i:i)+(s=(s=t.getSeconds().toString())<=9?"0"+s:s)+fr(yr())+"00";return o+fr("smsk_weapp_"+o).substr(0,14)+0}();return lr(e,n),dr(t,n),n}Je.prototype=new Error,Je.prototype.name="InvalidCharacterError";var br={timer:null,timeStamp:0},wr=+new Date,Sr=+new Date+"-"+Math.floor(1e8*Math.random());function xr(t){var e=Zt.smConf,r=e.apiProtocol+"://"+e.apiHost+e.apiPath,n=Tr(t),i=n.errObj,s=n.encrypedData;Zt.setSmInfo("smEncryptedData",s),$t(r,i)}function Tr(t){var e=Zt.smConf,r=e.appId,n=e.organization,i=Zt.smInfo,s=i.SMID,o=i.ep,a={appId:r,organization:n,os:"weapp",version:"3.0.0",sdkVer:"1.0.0",rtype:"exception",smid:_r(),box:s,gBox:"",tn:"",e:t},h={appId:r,organization:n,ep:o,data:Xe(JSON.stringify(a)),os:"weapp",encode:1,compress:0};return{errObj:h,encrypedData:Xe(JSON.stringify(h))}}function Er(t){var e=Zt.smConf,r=e.apiProtocol+"://"+e.apiHost+e.apiPath,n=Ar(t);Zt.setSmInfo("smEncryptedData",Xe(JSON.stringify(n))),$t(r,n,Dr,Dr)}function Dr(t){if(1100===Kt(t,"data.code"))return clearTimeout(br.timer),Zt.setSmInfo("retryCnt",0),void vr(Kt(t,"data.detail.deviceId",""));var e=Zt.getSmInfo("retryCnt");e>12?br.timeStamp=3e4:e>9?br.timeStamp=15e3:e>6?br.timeStamp=5e3:e>3&&(br.timeStamp=2e3),br.timer=setTimeout((function(){e++,Zt.setSmInfo("retryCnt",e),1902===Kt(t,"data.code")&&e<4?u().then((function(t){var e=Br(t);Zt.smInfo.isNeedStop||Er(e)})):Er()}),br.timeStamp)}function Br(t){for(var r=Zt.smConf,n=r.appId,i=r.organization,s=r.channel,o=Zt.smInfo,h=o.SMID,u=o.priId,c=+new Date-wr,f={},l=0;l<t.length;l++)Object.assign(f,t[l]);Object.assign(f,{organization:i,appId:n,os:"weapp",version:"3.0.0",sdkVer:"1.0.0",rtype:"all",smid:_r(),gBox:"",box:h,res:"".concat(f.screenWidth,"_").concat(f.screenHeight),channel:s,time:c,sessionId:Sr,subVersion:"1.0.0",weAppVerion:f.version,launchOptions:a()}),Object.assign(f,{tn:mr(f)});try{f=function(t){var e=JSON.stringify(t);return Xe(We.gzip(e,{to:"string"}))}(f)}catch(t){return xr("gzip_failed"),void Zt.setSmInfo("isNeedStop",!0)}try{f=function(t,r){Ge.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){for(var e=t.words,r=t.sigBytes-1;!(e[r>>>2]>>>24-r%4*8&255);)r--;t.sigBytes=r+1}};var n=Ge.enc.Utf8.parse("0102030405060708"),i=Ge.enc.Utf8.parse(r),s="object"==(void 0===t?"undefined":e(t))?JSON.stringify(t):t;return Ge.AES.encrypt(s,i,{iv:n,mode:Ge.mode.CBC,padding:Ge.pad.ZeroPadding}).ciphertext.toString()}(f,u),Zt.setSmInfo("smAesData",f)}catch(t){return xr("aes_failed"),void Zt.setSmInfo("isNeedStop",!0)}return f}function Ar(t){var e=Zt.smConf,r=e.appId,n=e.organization,i=Zt.smInfo,s=i.ep,o=i.smAesData;return{appId:r,organization:n,ep:s,data:t||o,os:"weapp",encode:6,compress:2}}var kr=u();function Or(t){Zt.smConf=Object.assign({},r,t),t||(xr("config_empty"),Zt.setSmInfo("isNeedStop",!0));var e=Zt.smConf,n=e.publicKey,i=e.organization;""===n&&(xr("publicKey_empty"),Zt.setSmInfo("isNeedStop",!0)),""===i&&(xr("organization_empty"),Zt.setSmInfo("isNeedStop",!0));var s=yr();Zt.setSmInfo("uid",s);var o=fr(s).slice(0,16),a=".thumbcache_".concat(fr(i));Zt.setSmInfo("priId",o),Zt.setSmInfo("storageName",a);try{var h=function(t,e){var r=new $e;return r.setPublicKey(e),r.encryptLong(t)}(s,n);Zt.setSmInfo("ep",h)}catch(t){xr("rsa_failed"),Zt.setSmInfo("isNeedStop",!0)}}var zr={initConf:function(t){Or(t);var e=function(){var t=Zt.getSmInfo("SMID");if(t)return t;var e=Zt.smInfo.storageName;return pr(e)||gr(e)}();e&&vr(e),kr.then((function(t){var e=Br(t);Zt.smInfo.isNeedStop||Er(e)}))},getDeviceId:function(){return new Promise((function(t){var e=function(){var e,r,n=(e=Zt.getSmInfo("SMID"))?"B"+e:(r=Zt.getSmInfo("smEncryptedData"))?"D"+r:"";if(n)return t(n),n};e()||(kr.then((function(r){if(!e()){var n=Ar(Br(r)),i=Xe(JSON.stringify(n));Zt.setSmInfo("smEncryptedData",i),t("D"+i)}})),setTimeout((function(){var e=Tr("getDeviceId_timeout").encrypedData;t("D"+e)}),1500))}))}};exports.default=zr;