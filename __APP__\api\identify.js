Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,r,t=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),n=c(require("./../npm/wepy/lib/wepy.js")),u=c(require("./../utils/ForumPage.js")),a=c(require("./../utils/ForumCommentPage.js")),o=c(require("./../utils/ReplayPostPage.js")),i=c(require("./../utils/ForumHttp.js")),s=c(require("./../utils/DateUtils.js"));function c(e){return e&&e.__esModule?e:{default:e}}function f(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(u,a){try{var o=r[u](a),i=o.value}catch(e){return void t(e)}if(!o.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}function p(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function l(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var m=(r=e=function(e){function r(){return p(this,r),l(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}var c,m,h,v,y,d,g,w,k,R,b,U,x,B,P,D,C,F,j,L,O,_,I,M,T,q,E,S,N,Z,J,A,G,H,$,z,K,Q,V,W,X,Y,ee,re,te,ne,ue,ae,oe,ie,se,ce,fe,pe;return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(r,i.default),t(r,null,[{key:"brandList",value:(pe=f(regeneratorRuntime.mark((function e(){var r,t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/brand/list",e.next=3,this.post(r,{});case 3:return t=e.sent,e.abrupt("return",t.rows);case 5:case"end":return e.stop()}}),e,this)}))),function(){return pe.apply(this,arguments)})},{key:"indentifylist",value:(fe=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/evaluation/query/by/status/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return fe.apply(this,arguments)})},{key:"saveidentify",value:(ce=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/evaluation/upload",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return ce.apply(this,arguments)})},{key:"guideList",value:(se=f(regeneratorRuntime.mark((function e(){var r,t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/conten/list/type/0",e.next=3,this.get(r,{});case 3:return t=e.sent,e.abrupt("return",t.data);case 5:case"end":return e.stop()}}),e,this)}))),function(){return se.apply(this,arguments)})},{key:"guideNewerList",value:(ie=f(regeneratorRuntime.mark((function e(){var r,t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/conten/list/type/1",e.next=3,this.get(r,{});case 3:return t=e.sent,e.abrupt("return",t.data);case 5:case"end":return e.stop()}}),e,this)}))),function(){return ie.apply(this,arguments)})},{key:"topicList",value:(oe=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/type/list",e.abrupt("return",new u.default(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return oe.apply(this,arguments)})},{key:"alltopicList",value:(ae=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/type/list",e.abrupt("return",new o.default(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return ae.apply(this,arguments)})},{key:"savePost",value:(ue=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic",e.abrupt("return",this.post(t,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return ue.apply(this,arguments)})},{key:"validateContent",value:(ne=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/content/validate?content="+r,e.abrupt("return",this.post(t));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return ne.apply(this,arguments)})},{key:"postList",value:(te=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/list",e.abrupt("return",new u.default(t,r,(function(e){return e.formatCreateDate=s.default.formatDate(e.createDate),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return te.apply(this,arguments)})},{key:"officialTopic",value:(re=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/official/details",e.abrupt("return",this.get(r).then((function(e){return e.data.records[0]&&(e.data.records[0].formatCreateDate=s.default.formatDate(e.data.records[0].createDate)),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return re.apply(this,arguments)})},{key:"isOfficial",value:(ee=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/user/details",e.abrupt("return",this.get(r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return ee.apply(this,arguments)})},{key:"postDetailList",value:(Y=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/details/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return Y.apply(this,arguments)})},{key:"sharePost",value:(X=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/share/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return X.apply(this,arguments)})},{key:"dianZanPost",value:(W=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/like/"+r+"/1",e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return W.apply(this,arguments)})},{key:"cancelDianZanPost",value:(V=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/like/cancel/"+r+"/1",e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return V.apply(this,arguments)})},{key:"dianZanComment",value:(Q=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/like/"+r+"/2",e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return Q.apply(this,arguments)})},{key:"cancelDianZanComment",value:(K=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/like/cancel/"+r+"/2",e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return K.apply(this,arguments)})},{key:"deleteMyComment",value:(z=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/comment/delete/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return z.apply(this,arguments)})},{key:"deletePost",value:($=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/delete/"+r,e.abrupt("return",this.post(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return $.apply(this,arguments)})},{key:"followPost",value:(H=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/flow/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return H.apply(this,arguments)})},{key:"cancelFollowPost",value:(G=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/flow/cancel/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return G.apply(this,arguments)})},{key:"followUser",value:(A=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/frans/user/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return A.apply(this,arguments)})},{key:"cancelFollowUser",value:(J=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/frans/cancel/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return J.apply(this,arguments)})},{key:"followTopic",value:(Z=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/type/flow/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return Z.apply(this,arguments)})},{key:"cancelfollowTopic",value:(N=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/type/flow/cancel/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return N.apply(this,arguments)})},{key:"followFans",value:(S=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/frans/user/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return S.apply(this,arguments)})},{key:"cancelFollowFans",value:(E=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/frans/cancel/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return E.apply(this,arguments)})},{key:"addComment",value:(q=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/comment",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return q.apply(this,arguments)})},{key:"childrenCommentDetailList",value:(T=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/comment/details/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return T.apply(this,arguments)})},{key:"browse",value:(M=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/conten/list/content/view/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return M.apply(this,arguments)})},{key:"indentifydetaillist",value:(I=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/evaluation/query/by/id/"+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return I.apply(this,arguments)})},{key:"uploadReturnId",value:(_=f(regeneratorRuntime.mark((function e(r){var t,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/upload",e.next=3,n.default.uploadFile({url:t,filePath:r,name:"file"}).catch((function(e){console.log("uploadImgErr",e)}));case 3:return u=e.sent,e.abrupt("return",JSON.parse(u));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return _.apply(this,arguments)})},{key:"uploadImg",value:(O=f(regeneratorRuntime.mark((function e(r){var t,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/evaluation/upload",e.next=3,n.default.uploadFile({url:t,filePath:r,name:"file"}).catch((function(e){console.log("uploadImgErr",e)}));case 3:return u=e.sent,e.abrupt("return",JSON.parse(u));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return O.apply(this,arguments)})},{key:"commonUploadFile",value:(L=f(regeneratorRuntime.mark((function e(r){var t,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/public/file/upload",e.next=3,n.default.uploadFile({url:t,filePath:r,name:"file"}).catch((function(e){console.log("uploadImgErr",e)}));case 3:return u=e.sent,e.abrupt("return",JSON.parse(u));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return L.apply(this,arguments)})},{key:"uploadImgList",value:function(e){var r=this.forumBaseUrl+"/forum/imageType/evaluation/type/"+e;return this.get(r).then((function(e){return e.data}))}},{key:"personMyPost",value:(j=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/home/"+r,e.abrupt("return",this.get(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return j.apply(this,arguments)})},{key:"myPostList",value:(F=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/list/"+r,e.abrupt("return",new a.default(t,(function(e){return e.formatCreateDate=s.default.formatDate(e.createDate),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return F.apply(this,arguments)})},{key:"myReplyPostList",value:(C=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/reback/list/"+r,e.abrupt("return",new o.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return C.apply(this,arguments)})},{key:"myFans",value:(D=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/frans/attention/me/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return D.apply(this,arguments)})},{key:"myFocusUser",value:(P=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/frans/our/care/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return P.apply(this,arguments)})},{key:"myIdentification",value:(B=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/list/"+r,e.abrupt("return",new a.default(t,(function(e){return e.formatCreateDate=s.default.formatDate(e.createDate),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return B.apply(this,arguments)})},{key:"deleteMyPost",value:(x=f(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={},n=this.forumBaseUrl+"/forum/topic/delete/"+r,e.abrupt("return",this.post(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return x.apply(this,arguments)})},{key:"myIdentificationAndsendPost",value:(U=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/release/list/"+r,e.abrupt("return",new o.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return U.apply(this,arguments)})},{key:"myFocusePost",value:(b=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/topic/list/"+r,e.abrupt("return",new o.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return b.apply(this,arguments)})},{key:"myCareTopic",value:(R=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/topic/type/list/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return R.apply(this,arguments)})},{key:"myTopicGetPostList",value:(k=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/topic/typeid/list/"+r,e.abrupt("return",new a.default(t,(function(e){return e.formatCreateDate=s.default.formatDate(e.createDate),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"topicPostTopCount",value:(w=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/topic/type/allcount/and/topictype/"+r+"/-1",e.abrupt("return",this.post(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return w.apply(this,arguments)})},{key:"personMyFeedback",value:(g=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/user/topic/typeid/list/-1",e.abrupt("return",new a.default(r,(function(e){return e.formatCreateDate=s.default.formatDate(e.createDate),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return g.apply(this,arguments)})},{key:"personMyFeedbacktopicPostTopCount",value:(d=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/type/allcount/and/topictype/-1/-1",e.abrupt("return",this.post(r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"myMsgCount",value:(y=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/unread/message/count/"+r,e.abrupt("return",this.post(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"myUnreadMsg",value:(v=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/unread/message/list/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"myAllMessage",value:(h=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/all/message/list/"+r,e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return h.apply(this,arguments)})},{key:"messageChangeStatus",value:(m=f(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/user/update/message/topic/type/"+r,e.abrupt("return",this.post(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"complaintList",value:(c=f(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/dic/content/items/topicOff",e.abrupt("return",this.get(r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"uninterested",value:function(e){var r=this.forumBaseUrl+"/forum/blacklist/add";return this.post(r,e).then((function(e){return e}))}},{key:"otherUninterested",value:function(e){var r=this.forumBaseUrl+"/forum/topic";return this.post(r,e)}},{key:"reportLaji",value:function(e){var r=this.forumBaseUrl+"/forum/tip/off";return this.post(r,e).then((function(e){return e}))}},{key:"available",value:function(e){var r=this,t=this.baseUrl+"/coupons/order_available",n={orderGoodsInfos:e};return this.post(t,n).then((function(e){return e?e.map((function(e){return r._processCouponItem(e)})):[]}))}}]),r}(),e.forumBaseUrl=n.default.$instance.globalData.forumBaseUrl,r);exports.default=m;