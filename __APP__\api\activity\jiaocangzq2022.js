Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../base.js"),n=(e=r)&&e.__esModule?e:{default:e};function i(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,o){try{var a=t[i](o),u=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function r(){return o(this,r),a(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}var u,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default),t(r,null,[{key:"save",value:(s=i(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/jiaocang/zq2022/save",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"help",value:(u=i(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/jiaocang/zq2022/help",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"detail",value:function(e){var t=this.jifenShopApiUrl+"/jiaocang/zq2022/read?member_id="+e;return this.get(t)}},{key:"ranks",value:function(){var e=this.jifenShopApiUrl+"/jiaocang/zq2022/ranks";return this.get(e)}},{key:"notice",value:function(){var e=this.jifenShopApiUrl+"/jiaocang/zq2022/notice";return this.get(e)}},{key:"detailForWinner",value:function(){var e=this.jifenShopApiUrl+"/jiaocang/zq2022/detail";return this.get(e)}},{key:"confirmAddress",value:function(e){var t=this.jifenShopApiUrl+"/jiaocang/zq2022/confirmAddress";return this.post(t,e)}}]),r}();exports.default=u;