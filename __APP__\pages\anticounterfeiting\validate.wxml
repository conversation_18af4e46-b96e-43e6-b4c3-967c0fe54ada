<view class="container">
    <view wx:if="{{!showOld&&isFwInvisibleCode=='true'}}">
        <view class="btn-area">
            <button bindtap="goToValidate" class="btn" plain="true">
                <text>进行标签验证</text>
            </button>
            <button bindtap="showOldBox" class="btn" plain="true" style="background:#006d5e8d;">
                <text>进行验证码验证</text>
            </button>
        </view>
    </view>
    <view class="" wx:if="{{showOld||isFwInvisibleCode!='true'}}">
        <view style="width:90%;margin:20rpx auto;">
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/1-v1.jpg" style="width:100%;"></image>
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/2-v1.jpg" style="width:100%;"></image>
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/3-v1.jpg" style="width:100%;"></image>
        </view>
        <form bindsubmit="confirm">
            <input class="weui-input" name="qrCode" style="display:none" value="{{validatenum.qrCode}}"></input>
            <view class="page-section" style="width:85%;margin:20rpx auto;">
                <input class="weui-input" id="securitycode" maxlength="4" minlength="4" name="theLast4Digits" placeholder="请选择一种方式输入数字进行验证" style="padding-left:20rpx;" value="{{validatenum.theLast4Digits}}"></input>
            </view>
            <view class="btn-area">
                <button class="btn" formType="submit" plain="true">
                    <text>立即验证</text>
                </button>
                <button bindtap="goToValidate" class="btn" plain="true" style="background:#006d5e8d;" wx:if="{{isFwInvisibleCode=='true'}}">
                    <text>进行标签验证</text>
                </button>
            </view>
        </form>
    </view>
</view>
