Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),n=u(require("./base.js")),s=(u(require("./goods.js")),u(require("./../npm/wepy/lib/wepy.js"))),i=u(require("./../utils/Page.js"));function u(e){return e&&e.__esModule?e:{default:e}}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(s,i){try{var u=t[s](i),o=u.value}catch(e){return void r(e)}if(!u.done)return Promise.resolve(o).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(o)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var f=(t=e=function(e){function t(){return a(this,t),c(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}var u,f,l;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,n.default),r(t,null,[{key:"visit",value:function(e,t){var r=this,n=this.baseUrl+"/visit_shops";s.default.getSystemInfo().then((function(s){return s.customScene=e,s.scene=t,r.post(n,s)})).then((function(e){}))}},{key:"info",value:(l=o(regeneratorRuntime.mark((function e(){var t,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/shops",e.next=3,this.get(t).then((function(e){return r._processInfo(e)}));case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"type",value:function(){var e=s.default.$instance.globalData.shopType;return this.TYPE[e]}},{key:"getStatus",value:(f=o(regeneratorRuntime.mark((function e(){var t,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/shops/status",e.abrupt("return",this.get(t).then((function(e){return r._processStatus(e)})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"isStatusOpen",value:(u=o(regeneratorRuntime.mark((function e(){var t,r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/shops/status",e.next=3,this.get(t);case 3:return r=e.sent,n=r.open,e.abrupt("return",n);case 6:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})},{key:"notices",value:function(){var e=this,t=this.baseUrl+"/notices";return this.get(t).then((function(t){return e._processNotices(t)}))}},{key:"reduces",value:function(){var e=this,t=this.baseUrl+"/reduce_rule";return this.get(t).then((function(t){return e._processReduce(t)}))}},{key:"chargeLimit",value:function(){var e=this,t=this.baseUrl+"/shop_charge_limit";return this.get(t).then((function(t){return e._precoessVersion(t)}))}},{key:"reportFormId",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e3;try{var n=this.baseUrl+"/visit_shops/form_id",s=[{formId:e}];r>0?setTimeout((function(){t.post(n,s,!1)}),r):this.post(n,s,!1)}catch(e){console.warn("formid上报错误",e)}}},{key:"signList",value:function(e){var t=this.baseUrl+"/member_sign?member_id="+e;return this.get(t)}},{key:"sign",value:function(e){var t=this.baseUrl+"/member_sign?member_id="+e;return this.post(t)}},{key:"signHistory",value:function(){var e=this.baseUrl+"/member_sign/history";return new i.default(e,this._processSignData.bind(this))}},{key:"_processInfo",value:function(e){return e.type=this.type(),e}},{key:"_processStatus",value:function(e){if(null!=e.beginTime&&null!=e.endTime)return e.timeText="周一至周日 "+e.beginTime+"至"+e.endTime,"CLOSE"==e.status?e.closeTips="店铺已休息，请稍后再来":"NORMAL"!=e.status||e.open||(e.closeTips="店铺已休息，请稍后再来"),e}},{key:"_precoessVersion",value:function(e){if(null==e)return{isMember:!0,isOrder:!0};var t=e.chargeVersion;return e.isMember=[2,3,6,7].some((function(e){return e==t})),e.isOrder=[4,5,6,7].some((function(e){return e==t})),e}},{key:"_processReduce",value:function(e){e.forEach((function(e){e.showText="满"+e.limitPrice+"减"+e.fee}));var t=e.map((function(e){return e.showText})).join(",");return{list:e,showText:t}}},{key:"_processNotices",value:function(e){return null==e||e.length<1?[{content:"暂无公告"}]:e}},{key:"_processSignData",value:function(e){var t={};return t.createTime=e.createTime,0==e.bonusType?(t.typeDesc="积分",t.addBonus=e.bonusResult):(t.typeDesc="优惠券",t.couponName=e.coupon.name),t.orderId=0,t}}]),t}(),e.TYPE={1:{key:"1",name:"在线商城",badgeText:"商城",basicName:"商品展示",basicBadgeText:"商城"},2:{key:"2",name:"点外卖",badgeText:"外卖",basicName:"在线菜单",basicBadgeText:"菜单"}},t);exports.default=f;