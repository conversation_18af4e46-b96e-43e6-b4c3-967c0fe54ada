Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=i(require("./../../npm/wepy/lib/wepy.js")),e=i(require("./discount_badge.js")),n=i(require("./../zanui/quantity.js"));function i(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var a=function(i){function a(){var t,i,c;o(this,a);for(var u=arguments.length,s=Array(u),l=0;l<u;l++)s[l]=arguments[l];return i=c=r(this,(t=a.__proto__||Object.getPrototypeOf(a)).call.apply(t,[this].concat(s))),c.props={cart:{},index:{}},c.data={txtStyle:[],startX:0,delBtnWidth:60},c.methods={num:function(t){this.$emit("num",t)},check:function(t){this.$emit("check",t)},remove:function(t){this.$emit("remove",t)},detail:function(t){this.$root.$navigate("detail?goodsId="+t)},touchS:function(t){1==t.touches.length&&(this.startX=t.touches[0].clientX)},touchM:function(t){if(1==t.touches.length){var e=t.touches[0].clientX,n=this.startX-e-30,i=this.delBtnWidth,o="";0==n||n<0?o="left:0px":n>0&&(o="left:-"+n+"px",n>=i&&(o="left:-"+i+"px")),this.txtStyle[this.index]=o}},touchE:function(t){if(1==t.changedTouches.length){var e=t.changedTouches[0].clientX,n=this.startX-e-30,i=this.delBtnWidth,o=n>i/2?"left:-"+i+"px":"left:0px";this.txtStyle[this.index]=o}}},c.$repeat={"[cart]":{com:"DiscountBadge",props:"goods.sync"},"[cart":{com:"ZanQuantity",props:"quantity.sync"}},c.$props={DiscountBadge:{"xmlns:v-bind":{value:"",for:"[cart]",item:"item",index:"i",key:"i"},"v-bind:goods.sync":{value:"item",type:"item",for:"[cart]",item:"item",index:"i",key:"i"}},ZanQuantity:{"v-bind:quantity.sync":{value:"item",type:"item",for:"[cart.quantity]",item:"item",index:"i",key:"i"},"xmlns:v-on":{value:"",for:"[cart.quantity]",item:"item",index:"i",key:"i"}}},c.$events={ZanQuantity:{"v-on:change":"num"}},c.components={ZanQuantity:n.default,DiscountBadge:e.default},r(c,i)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(a,t.default.component),a}();exports.default=a;