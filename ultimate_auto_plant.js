/**
 * 终极自动种植系统
 * 结合微信环境模拟，解决所有认证和环境问题
 */

const WeChatEnvironmentSimulator = require('./wechat_environment_simulator');

class UltimateAutoPlant extends WeChatEnvironmentSimulator {
    constructor() {
        super();
        
        // 种植配置
        this.plantConfig = {
            autoPlant: true,
            autoWater: true,
            autoFertilize: true,
            autoHarvest: true,
            cropType: 1, // 1=高粱, 2=小麦
            checkInterval: 30, // 检查间隔(分钟)
            reentryInterval: 120 // 重新进入页面间隔(分钟)
        };
        
        // 运行状态
        this.isRunning = false;
        this.plantInterval = null;
        this.reentryInterval = null;
        this.lastReentryTime = 0;
        this.operationCount = 0;
        
        console.log('🌱 终极自动种植系统初始化完成');
        console.log('🎯 功能: 微信环境模拟 + 自动种植 + 智能重入');
        console.log('✅ 返回首页功能已验证通过');
    }

    /**
     * 检查是否需要重新进入页面
     */
    shouldReenterPage() {
        const now = Date.now();
        const timeSinceLastReentry = now - this.lastReentryTime;
        const reentryIntervalMs = this.plantConfig.reentryInterval * 60 * 1000;
        
        return timeSinceLastReentry > reentryIntervalMs;
    }

    /**
     * 智能重新进入页面
     */
    async smartPageReentry() {
        console.log('\n🔄 执行智能页面重入...');
        
        try {
            // 执行完整的页面重新进入流程
            const success = await this.simulatePageReentry();
            
            if (success) {
                this.lastReentryTime = Date.now();
                console.log('✅ 智能页面重入成功');
                return true;
            } else {
                console.log('❌ 智能页面重入失败');
                return false;
            }
            
        } catch (error) {
            console.log('❌ 智能页面重入异常:', error.message);
            return false;
        }
    }

    /**
     * 增强的种植操作
     */
    async enhancedPlantingOperations() {
        console.log('\n🌱 开始增强种植操作...');
        
        try {
            // 1. 检查是否需要重新进入页面
            if (this.shouldReenterPage()) {
                console.log('⏰ 需要重新进入页面以保持会话活跃');
                const reentrySuccess = await this.smartPageReentry();
                if (!reentrySuccess) {
                    console.log('❌ 页面重入失败，跳过本次种植');
                    return false;
                }
            }
            
            // 2. 确保在种植页面
            if (this.currentPage !== 'pages/plant/index') {
                console.log('📱 当前不在种植页面，导航到种植页面');
                const navSuccess = await this.navigateToPlantPage();
                if (!navSuccess) {
                    console.log('❌ 导航到种植页面失败');
                    return false;
                }
            }
            
            // 3. 执行种植操作
            const plantingSuccess = await this.performPlantingOperations();
            
            if (plantingSuccess) {
                this.operationCount++;
                console.log(`✅ 增强种植操作完成 (第${this.operationCount}次)`);
                return true;
            } else {
                console.log('❌ 增强种植操作失败');
                return false;
            }
            
        } catch (error) {
            console.log('❌ 增强种植操作异常:', error.message);
            return false;
        }
    }

    /**
     * 处理单块土地（增强版）
     */
    async processSoil(soil) {
        const { id, status, type } = soil;
        
        // 跳过无效土地
        if (!id || id === 'undefined' || status === -1) {
            return;
        }
        
        console.log(`🌱 处理土地${id} (状态: ${status}, 类型: ${type || '未知'})`);
        
        try {
            let operationSuccess = false;
            
            switch (status) {
                case 0: // 空地，可以种植
                    if (this.plantConfig.autoPlant) {
                        const plantResult = await this.makeRequest('/garden/sorghum/seed', 'POST', { 
                            id: id, 
                            type: this.plantConfig.cropType 
                        });
                        if (plantResult.success) {
                            console.log(`🌱 土地${id} 种植成功`);
                            operationSuccess = true;
                        } else {
                            console.log(`🌱 土地${id} 种植失败: ${plantResult.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 2: // 成熟，可以收获
                    if (this.plantConfig.autoHarvest) {
                        const harvestResult = await this.makeRequest('/garden/sorghum/harvest', 'POST', { id: id });
                        if (harvestResult.success) {
                            console.log(`🎉 土地${id} 收获成功`);
                            operationSuccess = true;
                            
                            // 收获后重新种植
                            if (this.plantConfig.autoPlant) {
                                await this.sleep(1000);
                                const replantResult = await this.makeRequest('/garden/sorghum/seed', 'POST', { 
                                    id: id, 
                                    type: this.plantConfig.cropType 
                                });
                                if (replantResult.success) {
                                    console.log(`🌱 土地${id} 重新种植成功`);
                                }
                            }
                        } else {
                            console.log(`🎉 土地${id} 收获失败: ${harvestResult.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 10: // 需要浇水
                    if (this.plantConfig.autoWater) {
                        const waterResult = await this.makeRequest('/garden/sorghum/watering', 'POST', { id: id });
                        if (waterResult.success) {
                            console.log(`💧 土地${id} 浇水成功`);
                            operationSuccess = true;
                        } else {
                            console.log(`💧 土地${id} 浇水失败: ${waterResult.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 11: // 需要施肥
                    if (this.plantConfig.autoFertilize) {
                        const fertilizeResult = await this.makeRequest('/garden/sorghum/manuring', 'POST', { id: id });
                        if (fertilizeResult.success) {
                            console.log(`🌿 土地${id} 施肥成功`);
                            operationSuccess = true;
                        } else {
                            console.log(`🌿 土地${id} 施肥失败: ${fertilizeResult.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                default:
                    console.log(`🌱 土地${id} 状态${status} 无需操作`);
            }
            
            return operationSuccess;
            
        } catch (error) {
            console.log(`❌ 土地${id} 操作失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 启动终极自动种植系统
     */
    async start() {
        if (this.isRunning) {
            console.log('⚠️ 终极自动种植系统已在运行中');
            return;
        }
        
        console.log('🚀 启动终极自动种植系统...');
        
        try {
            // 1. 初始化微信环境
            console.log('📱 初始化微信环境...');
            const initSuccess = await this.smartPageReentry();
            if (!initSuccess) {
                console.log('❌ 微信环境初始化失败');
                return false;
            }
            
            // 2. 设置运行状态
            this.isRunning = true;
            this.lastReentryTime = Date.now();
            
            // 3. 立即执行一次种植操作
            await this.enhancedPlantingOperations();
            
            // 4. 设置定时种植任务
            console.log(`\n🔄 设置定时种植任务，间隔${this.plantConfig.checkInterval}分钟`);
            this.plantInterval = setInterval(async () => {
                console.log(`\n⏰ 定时种植任务执行 (${new Date().toLocaleString('zh-CN')})`);
                await this.enhancedPlantingOperations();
            }, this.plantConfig.checkInterval * 60 * 1000);
            
            // 5. 设置定时重入任务
            console.log(`🔄 设置定时重入任务，间隔${this.plantConfig.reentryInterval}分钟`);
            this.reentryInterval = setInterval(async () => {
                console.log(`\n🔄 定时重入任务执行 (${new Date().toLocaleString('zh-CN')})`);
                await this.smartPageReentry();
            }, this.plantConfig.reentryInterval * 60 * 1000);
            
            console.log('\n🎉 终极自动种植系统启动成功！');
            console.log('📊 系统配置:');
            console.log(`  ✅ 微信环境模拟: 开启`);
            console.log(`  ✅ 智能页面重入: 开启`);
            console.log(`  ✅ 自动种植: ${this.plantConfig.autoPlant ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动浇水: ${this.plantConfig.autoWater ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动施肥: ${this.plantConfig.autoFertilize ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动收获: ${this.plantConfig.autoHarvest ? '开启' : '关闭'}`);
            console.log(`  ✅ 种植检查间隔: ${this.plantConfig.checkInterval}分钟`);
            console.log(`  ✅ 页面重入间隔: ${this.plantConfig.reentryInterval}分钟`);
            console.log('\n🛑 按 Ctrl+C 停止系统');
            
            return true;
            
        } catch (error) {
            console.error('❌ 终极自动种植系统启动失败:', error.message);
            return false;
        }
    }

    /**
     * 停止终极自动种植系统
     */
    async stop() {
        if (!this.isRunning) {
            console.log('⚠️ 终极自动种植系统未在运行');
            return;
        }
        
        console.log('\n🛑 正在停止终极自动种植系统...');
        
        // 清理定时器
        if (this.plantInterval) {
            clearInterval(this.plantInterval);
            this.plantInterval = null;
        }
        
        if (this.reentryInterval) {
            clearInterval(this.reentryInterval);
            this.reentryInterval = null;
        }
        
        // 设置状态
        this.isRunning = false;
        
        console.log('✅ 终极自动种植系统已停止');
        console.log(`📊 运行统计: 共执行 ${this.operationCount} 次种植操作`);
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            isRunning: this.isRunning,
            operationCount: this.operationCount,
            lastReentryTime: this.lastReentryTime,
            currentPage: this.currentPage,
            plantConfig: this.plantConfig,
            deviceInfo: {
                deviceId: this.deviceFingerprint.deviceId,
                model: this.wechatEnv.model,
                wechatVersion: this.wechatEnv.wechatVersion
            },
            intervals: {
                plantInterval: !!this.plantInterval,
                reentryInterval: !!this.reentryInterval
            },
            uptime: this.isRunning ? Date.now() - this.lastReentryTime : 0
        };
    }
}

// 导出类
module.exports = UltimateAutoPlant;

// 如果直接运行此文件
if (require.main === module) {
    const autoPlant = new UltimateAutoPlant();
    
    console.log('🌱 终极自动种植系统');
    console.log('🎯 功能: 微信环境模拟 + 智能重入 + 自动种植');
    console.log('🔧 解决"请在手机微信内操作"问题');
    console.log('');
    
    // 启动系统
    autoPlant.start().then(success => {
        if (!success) {
            console.log('❌ 系统启动失败');
            process.exit(1);
        }
    });
    
    // 处理退出信号
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到退出信号...');
        await autoPlant.stop();
        process.exit(0);
    });
    
    // 定期输出状态
    setInterval(() => {
        if (autoPlant.isRunning) {
            const status = autoPlant.getSystemStatus();
            console.log(`\n📊 系统状态 (${new Date().toLocaleString('zh-CN')})`);
            console.log(`  🔄 运行中: ${status.isRunning}`);
            console.log(`  📱 当前页面: ${status.currentPage}`);
            console.log(`  🌱 操作次数: ${status.operationCount}`);
            console.log(`  ⏱️ 运行时长: ${Math.floor(status.uptime / 60000)}分钟`);
        }
    }, 10 * 60 * 1000); // 每10分钟输出一次状态
}
