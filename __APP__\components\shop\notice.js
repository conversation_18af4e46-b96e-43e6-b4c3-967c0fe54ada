Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=n(require("./../../npm/wepy/lib/wepy.js")),e=n(require("./../common/cover_panel.js"));function n(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var i=function(n){function i(){var t,n,s;o(this,i);for(var a=arguments.length,c=Array(a),l=0;l<a;l++)c[l]=arguments[l];return n=s=r(this,(t=i.__proto__||Object.getPrototypeOf(i)).call.apply(t,[this].concat(c))),s.data={content:"",display:!1,title:"公告",btnText:"我知道了"},s.methods={open:function(t){var e=t.title,n=t.btnText,o=t.content;this.display=!0,this.title=e,this.btnText=n,this.content=o},close:function(){this.display=!1}},s.$repeat={},s.$props={CoverPanel:{"xmlns:v-bind":"","v-bind:display.sync":"display"}},s.$events={},s.components={CoverPanel:e.default},r(s,n)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,t.default.component),i}();exports.default=i;