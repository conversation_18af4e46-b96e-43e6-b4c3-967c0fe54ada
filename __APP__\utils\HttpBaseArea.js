Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),n=a(require("./../npm/wepy/lib/wepy.js")),u=a(require("./Http.js"));function a(e){return e&&e.__esModule?e:{default:e}}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(u,a){try{var o=t[u](a),i=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}var i=(t=e=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}var a,i,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,u.default),r(t,null,[{key:"request",value:(s=o(regeneratorRuntime.mark((function e(t,r,u){var a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a={url:""+this.baseUrl+r,method:t,data:u},e.next=3,n.default.request(a);case 3:return o=e.sent,e.abrupt("return",this.interceptor(o));case 5:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return s.apply(this,arguments)})},{key:"isSuccess",value:(i=o(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(200===t.statusCode){e.next=3;break}return e.abrupt("return",!1);case 3:return r=t.data,e.abrupt("return","A00000"==r.code);case 5:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"interceptor",value:(a=o(regeneratorRuntime.mark((function e(t,r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isSuccess(t)){e.next=4;break}return e.abrupt("return",t.data.data);case 4:throw this.requestException(t);case 5:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"requestException",value:function(e){var t={};t.statusCode=e.statusCode;var r=e.data,n=r.data;return t.serverCode=r.code,t.message=r.message,t.serverData=n,t}},{key:"custom",value:function(e,t){var r=this;return n.default.request(e).then((function(e){return r.interceptor(e,t),e}))}}]),t}(),e.baseUrl=n.default.$instance.globalData.baseAreaHost+"/api",t);exports.default=i;