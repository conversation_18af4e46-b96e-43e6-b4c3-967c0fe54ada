/**
 * 精准解决方案
 * 基于深度代码分析结果的精确实现
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class PrecisionSolution {
    constructor() {
        // 基于分析结果，Authorization是关键
        this.authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 基于分析结果的API域名
        this.workingDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apimallwm.exijiu.com/api',
            'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2'
        ];
        
        // 小程序配置
        this.appConfig = {
            appId: 'wx489f950decfeb93e',
            version: 'v3.2.6'
        };
        
        // 种植配置
        this.plantConfig = {
            autoPlant: true,
            autoWater: true,
            autoFertilize: true,
            autoHarvest: true,
            cropType: 1,
            checkInterval: 30 // 分钟
        };
        
        // 运行状态
        this.isRunning = false;
        this.plantInterval = null;
        this.operationCount = 0;
        
        console.log('🎯 精准解决方案初始化完成');
        console.log('✅ 基于深度代码分析结果');
        console.log('🔑 使用Authorization作为主要认证方式');
    }

    /**
     * 构建精准的请求头
     */
    buildPrecisionHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
            'Referer': `https://servicewechat.com/${this.appConfig.appId}/${this.appConfig.version}/page-frame.html`,
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            
            // 关键认证头部
            'Authorization': this.authToken,
            'login_code': this.loginCode
        };
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        const headers = this.buildPrecisionHeaders();
        
        for (const baseUrl of this.workingDomains) {
            try {
                const result = await this.tryRequest(baseUrl, path, method, data, headers);
                if (result.success) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 验证认证状态
     */
    async validateAuth() {
        console.log('\n🔍 验证认证状态...');
        
        try {
            const result = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            
            if (result.success) {
                console.log('✅ 认证状态有效');
                console.log('👤 用户:', result.data.nick_name || '未知用户');
                return true;
            } else {
                console.log('❌ 认证状态无效');
                return false;
            }
        } catch (error) {
            console.log('❌ 认证验证失败:', error.message);
            return false;
        }
    }

    /**
     * 获取土地信息
     */
    async getSoilList() {
        try {
            const result = await this.makeRequest('/garden/sorghum/index');
            if (result.success && result.data && result.data.data) {
                return result.data.data;
            }
            return [];
        } catch (error) {
            console.log('❌ 获取土地列表失败:', error.message);
            return [];
        }
    }

    /**
     * 处理单块土地
     */
    async processSoil(soil) {
        const { id, status, type } = soil;
        
        if (!id || id === 'undefined') {
            return false;
        }
        
        console.log(`🌱 处理土地${id} (状态: ${status}, 类型: ${type || '未知'})`);
        
        try {
            switch (status) {
                case 0: // 空地，可以种植
                    if (this.plantConfig.autoPlant) {
                        const result = await this.makeRequest('/garden/sorghum/seed', 'POST', { 
                            id: id, 
                            type: this.plantConfig.cropType 
                        });
                        if (result.success) {
                            console.log(`🌱 土地${id} 种植成功`);
                            return true;
                        } else {
                            console.log(`🌱 土地${id} 种植失败: ${result.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 2: // 成熟，可以收获
                    if (this.plantConfig.autoHarvest) {
                        const result = await this.makeRequest('/garden/sorghum/harvest', 'POST', { id: id });
                        if (result.success) {
                            console.log(`🎉 土地${id} 收获成功`);
                            // 收获后重新种植
                            if (this.plantConfig.autoPlant) {
                                await this.sleep(1000);
                                const replantResult = await this.makeRequest('/garden/sorghum/seed', 'POST', { 
                                    id: id, 
                                    type: this.plantConfig.cropType 
                                });
                                if (replantResult.success) {
                                    console.log(`🌱 土地${id} 重新种植成功`);
                                }
                            }
                            return true;
                        } else {
                            console.log(`🎉 土地${id} 收获失败: ${result.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 10: // 需要浇水
                    if (this.plantConfig.autoWater) {
                        const result = await this.makeRequest('/garden/sorghum/watering', 'POST', { id: id });
                        if (result.success) {
                            console.log(`💧 土地${id} 浇水成功`);
                            return true;
                        } else {
                            console.log(`💧 土地${id} 浇水失败: ${result.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                case 11: // 需要施肥
                    if (this.plantConfig.autoFertilize) {
                        const result = await this.makeRequest('/garden/sorghum/manuring', 'POST', { id: id });
                        if (result.success) {
                            console.log(`🌿 土地${id} 施肥成功`);
                            return true;
                        } else {
                            console.log(`🌿 土地${id} 施肥失败: ${result.data?.msg || '未知错误'}`);
                        }
                    }
                    break;
                    
                default:
                    console.log(`🌱 土地${id} 状态${status} 无需操作`);
            }
            
            return false;
        } catch (error) {
            console.log(`❌ 土地${id} 操作失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 执行种植任务
     */
    async performPlantingTasks() {
        console.log('\n🚀 开始执行种植任务...');
        
        try {
            // 1. 验证认证状态
            const authValid = await this.validateAuth();
            if (!authValid) {
                console.log('❌ 认证状态无效，跳过种植任务');
                return false;
            }
            
            // 2. 获取土地信息
            const soilList = await this.getSoilList();
            if (soilList.length === 0) {
                console.log('❌ 没有土地信息，跳过种植任务');
                return false;
            }
            
            console.log(`🌱 找到 ${soilList.length} 块土地`);
            
            // 3. 处理每块土地
            let successCount = 0;
            for (const soil of soilList) {
                const success = await this.processSoil(soil);
                if (success) {
                    successCount++;
                }
                await this.sleep(1000); // 避免请求过快
            }
            
            console.log(`✅ 种植任务完成，成功处理 ${successCount}/${soilList.length} 块土地`);
            this.operationCount++;
            
            return true;
            
        } catch (error) {
            console.error('❌ 种植任务执行失败:', error.message);
            return false;
        }
    }

    /**
     * 启动精准解决方案
     */
    async start() {
        if (this.isRunning) {
            console.log('⚠️ 精准解决方案已在运行中');
            return;
        }
        
        console.log('🚀 启动精准解决方案...');
        
        try {
            // 1. 验证认证状态
            const authValid = await this.validateAuth();
            if (!authValid) {
                console.log('❌ 认证状态无效，无法启动');
                return false;
            }
            
            // 2. 设置运行状态
            this.isRunning = true;
            
            // 3. 立即执行一次种植任务
            await this.performPlantingTasks();
            
            // 4. 设置定时任务
            console.log(`\n🔄 设置定时种植任务，间隔${this.plantConfig.checkInterval}分钟`);
            this.plantInterval = setInterval(async () => {
                console.log(`\n⏰ 定时种植任务执行 (${new Date().toLocaleString('zh-CN')})`);
                await this.performPlantingTasks();
            }, this.plantConfig.checkInterval * 60 * 1000);
            
            console.log('\n🎉 精准解决方案启动成功！');
            console.log('📊 系统配置:');
            console.log(`  ✅ 认证方式: Authorization + login_code`);
            console.log(`  ✅ 自动种植: ${this.plantConfig.autoPlant ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动浇水: ${this.plantConfig.autoWater ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动施肥: ${this.plantConfig.autoFertilize ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动收获: ${this.plantConfig.autoHarvest ? '开启' : '关闭'}`);
            console.log(`  ✅ 检查间隔: ${this.plantConfig.checkInterval}分钟`);
            console.log('\n🛑 按 Ctrl+C 停止系统');
            
            return true;
            
        } catch (error) {
            console.error('❌ 精准解决方案启动失败:', error.message);
            return false;
        }
    }

    /**
     * 停止精准解决方案
     */
    async stop() {
        if (!this.isRunning) {
            console.log('⚠️ 精准解决方案未在运行');
            return;
        }
        
        console.log('\n🛑 正在停止精准解决方案...');
        
        // 清理定时器
        if (this.plantInterval) {
            clearInterval(this.plantInterval);
            this.plantInterval = null;
        }
        
        // 设置状态
        this.isRunning = false;
        
        console.log('✅ 精准解决方案已停止');
        console.log(`📊 运行统计: 共执行 ${this.operationCount} 次种植操作`);
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出类
module.exports = PrecisionSolution;

// 如果直接运行此文件
if (require.main === module) {
    const solution = new PrecisionSolution();
    
    console.log('🎯 精准解决方案');
    console.log('✅ 基于深度代码分析结果');
    console.log('🔑 使用正确的认证机制');
    console.log('');
    
    // 启动解决方案
    solution.start().then(success => {
        if (!success) {
            console.log('❌ 解决方案启动失败');
            process.exit(1);
        }
    });
    
    // 处理退出信号
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到退出信号...');
        await solution.stop();
        process.exit(0);
    });
    
    // 定期输出状态
    setInterval(() => {
        if (solution.isRunning) {
            console.log(`\n📊 系统状态 (${new Date().toLocaleString('zh-CN')})`);
            console.log(`  🔄 运行中: ${solution.isRunning}`);
            console.log(`  🌱 操作次数: ${solution.operationCount}`);
        }
    }, 10 * 60 * 1000); // 每10分钟输出一次状态
}
