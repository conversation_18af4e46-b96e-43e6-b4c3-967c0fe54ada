Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),r=o(require("./../../api/order.js")),n=o(require("./../../utils/Event.js"));function o(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(o,u){try{var i=t[o](u),a=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(o){function s(){var e,t,r;i(this,s);for(var n=arguments.length,o=Array(n),c=0;c<n;c++)o[c]=arguments[c];return t=r=a(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(o))),r.props={},r.data={count:null},r.methods={list:function(e){this.$root.$navigate("/pages/order/list?status="+e)}},r.events={refresh:function(){var e=this;return u(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.load();case 2:case"end":return t.stop()}}),t,e)})))()}},a(r,t)}var c,f;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,t.default.component),e(s,[{key:"onLoad",value:(f=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.default.listen(n.default.ORDER_LIST_UPDATE,this.load.bind(this),this),e.next=3,this.load();case 3:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"load",value:(c=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.default.count();case 2:this.count=e.sent,this.$apply();case 4:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})}]),s}();exports.default=s;