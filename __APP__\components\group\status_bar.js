Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=r(require("./../../npm/wepy/lib/wepy.js")),e=r(require("./../../mixins/router.js")),o=r(require("./../../mixins/countdown.js"));function r(t){return t&&t.__esModule?t:{default:t}}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var u=function(r){function u(){var t,r,a;i(this,u);for(var s=arguments.length,l=Array(s),p=0;p<s;p++)l[p]=arguments[p];return r=a=n(this,(t=u.__proto__||Object.getPrototypeOf(u)).call.apply(t,[this].concat(l))),a.props={detail:{}},a.data={init:!1},a.methods={open:function(){this.$root.$navigate("/pages/group/goods_detail?ruldId="+this.detail.rule.id)},join:function(){var t=this.detail.rule,e=t.goods,o=t.goodsGroupSkuDetails,r=t.id;this.group(e,o,r,"join",this.detail.id)}},a.watch={detail:function(t){null!==t&&!1===this.init&&(this.countdowm(t.groupTime.replace(/-/g,"/"),"groupTime"),this.init=!0)}},a.mixins=[e.default,o.default],n(a,r)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,t.default.component),u}();exports.default=u;