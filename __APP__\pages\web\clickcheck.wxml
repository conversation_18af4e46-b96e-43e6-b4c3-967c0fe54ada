<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view class="container" wx:if="{{init}}">
    <view class="goods-info-box column">
        <view class="row mt10">
            <text class="goodsinfo">生产单号：{{shengchanpihaoCode}}</text>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb;" wx:for="{{detail}}" wx:for-index="key" wx:key="id">
        <view class="tr bg-w">
            <view class="title">发货单号</view>
            <view class="value" wx:if="{{item.billNo!=null}}">{{item.billNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">品号</view>
            <view class="value">{{item.productErpId}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">品名</view>
            <view class="value" wx:if="{{item.productName!=null}}">{{item.productName}}</view>
            <view class="value" wx:else></view>
        </view>
        <view class="tr bg-w">
            <view class="title">客户代码</view>
            <view class="value" wx:if="{{item.customerId!=null}}">{{item.customerId}}</view>
            <view class="value" wx:else></view>
        </view>
        <view class="tr bg-w">
            <view class="title">客户名称</view>
            <view class="value" wx:if="{{item.customerName!=null}}">{{item.customerName}}</view>
            <view class="value" wx:else></view>
        </view>
        <view class="tr bg-w">
            <view class="title">发货总数</view>
            <view class="value" wx:if="{{item.totalQty!=null}}">{{item.totalQty}}</view>
            <view class="value" wx:else></view>
        </view>
        <view class="tr bg-w">
            <view class="title">包含数量</view>
            <view class="value" wx:if="{{item.partlyQty!=null}}">{{item.partlyQty}}</view>
            <view class="value" wx:else></view>
        </view>
    </view>
</view>
