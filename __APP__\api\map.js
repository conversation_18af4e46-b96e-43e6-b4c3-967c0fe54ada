Object.defineProperty(exports,"__esModule",{value:!0});var e,n,t=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}();var r=require("./../utils/QQMap.js"),o=(n=e=function(){function e(){!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e)}return t(e,null,[{key:"search",value:function(e){var n=this;return new Promise((function(t,r){n.map.search({boundary:"region("+n.API_REGION+",0)",keyword:e,success:function(e){var r=e.data,o=[];r.forEach((function(e){var t=n.processPOI(e);o.push(t)})),t(o)},fail:function(e){r(e)}})}))}},{key:"reverse",value:function(e,n){var t=this;return new Promise((function(r,o){t.map.reverseGeocoder({location:{latitude:e,longitude:n},get_poi:1,poi_options:"policy=2",success:function(e){var n=e.result,o={};o.display=n.formatted_addresses.recommend,o.province=n.ad_info.province,o.city=n.ad_info.city,o.country=n.ad_info.district,o.town=n.address_reference.town.title,o.address=n.address,o.detail=n.address+o.display,o.latitude=n.location.lat,o.longitude=n.location.lng;var i=[];n.pois.forEach((function(e){var n=t.processPOI(e);n.town=o.town,i.push(n)})),r({current:o,nearby:i})},fail:function(e){o(e)}})}))}},{key:"processPOI",value:function(e){var n={};return n.display=e.title,n.province=e.ad_info.province,n.city=e.ad_info.city,n.country=e.ad_info.district,n.detail=e.address+e.title,n.latitude=e.location.lat,n.longitude=e.location.lng,n.address=e.address,n}}]),e}(),e.API_KEY="TRZBZ-TSJ3U-ROQVP-4EMUK-EQA52-V5FWT",e.API_REGION="福州市",e.map=new r({key:"TRZBZ-TSJ3U-ROQVP-4EMUK-EQA52-V5FWT"}),n);exports.default=o;