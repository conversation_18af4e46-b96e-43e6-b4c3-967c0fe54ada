var e;Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=i(require("./../../npm/wepy/lib/wepy.js")),o=i(require("./../../utils/WxUtils.js")),r=require("./../../npm/wepy-redux/lib/index.js"),n=i(require("./../../store/utils.js"));function i(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=(0,r.connect)({shop:n.default.get("shop"),notices:n.default.get("notices")})(e=function(e){function r(){var e,t,n;u(this,r);for(var i=arguments.length,c=Array(i),a=0;a<i;a++)c[a]=arguments[a];return t=n=s(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(c))),n.props={},n.methods={home:function(){o.default.backOrRedirect("/pages/home/<USER>")}},s(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,t.default.component),r}())||e;exports.default=c;