Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var s=r[t];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(r,t,s){return t&&e(r.prototype,t),s&&e(r,s),r}}(),r=i(require("./base.js")),t=i(require("./../utils/Page.js")),s=require("./order_const.js"),o=i(require("./../utils/WxUtils.js"));function i(e){return e&&e.__esModule?e:{default:e}}function n(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function u(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var a=function(i){function a(){return n(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(a,r.default),e(a,null,[{key:"page",value:function(){var e=this.baseUrl+"/orders";return new t.default(e,this._processOrderListItem.bind(this))}},{key:"count",value:function(){var e=this.baseUrl+"/orders/count";return this.get(e).then((function(e){var r={};return e.forEach((function(e){var t=e.status,s=e.total;r[t]=s})),r}))}},{key:"getInfo",value:function(e){var r=this,t=this.baseUrl+"/orders/"+e;return this.get(t,{}).then((function(e){return r._processOrderDetail(e)}))}},{key:"prepayOrder",value:function(e){var r=this.baseUrl+"/wxpay/orders/"+e;return this.get(r,{})}},{key:"wxpayOrder",value:function(e){return o.default.wxPay({timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.packageValue,signType:"MD5",paySign:e.paySign})}},{key:"createOrder",value:function(e,r){var t=this.baseUrl+"/orders";return this._processOrderAddress(e,r),this.post(t,e)}},{key:"refundOrder",value:function(e,r){var t=this.baseUrl+"/orders/"+e+"/status/refund";return this.put(t,r)}},{key:"cancelRefundOrder",value:function(e,r){var t=this.baseUrl+"/orders/"+e+"/status/cancel_refund_money",s={refundUuid:r};return this.put(t,s)}},{key:"closeOrder",value:function(e){var r=this.baseUrl+"/orders/"+e+"/status/close";return this.put(r,"买家关闭")}},{key:"confirmOrder",value:function(e){var r=this.baseUrl+"/orders/"+e+"/status/comments";return this.put(r)}},{key:"offline",value:function(e){var r=this.baseUrl+"/orders/offline";return this.post(r,e)}},{key:"createCartTrade",value:function(e,r){var t=[],s=0;for(var o in e){var i=e[o],n={goodsId:i.goodsId,goodsName:i.goodsName,imageUrl:i.goodsImage,goodsPrice:i.goodsPrice,count:i.goodsNum,innerCid:i.innerCid,skuText:i.skuText,goodsSku:i.goodsSku,goodsSellPrice:i.originalPrice,discount:i.discount,discountRate:i.discountRate,discountText:i.discountText};t.push(n),s+=i.goodsPrice*i.goodsNum}var u=s,a=0;r&&r.reduce&&(u-=a=r.reduce.fee)<0&&(u=0),u=u.toFixed(2);var c={orderType:r.orderType,dealPrice:s.toFixed(2),reduceFee:a,finalPrice:u,postFee:(0).toFixed(2),paymentType:"1",paymentText:"在线支付",orderGoodsInfos:t,shopName:this.shopName};return this._processTypeFlag(c),c.isInShopOrder&&(c.arriveTime="立即出餐"),c}},{key:"createOrderRefund",value:function(e){return{orderId:e.orderId,uuid:e.uuid,type:0,contactName:e.receiveName,contactPhone:e.receivePhone,price:e.finalPrice}}},{key:"createOrderRefundSetps",value:function(e){var r=[],t=e.createTime;t&&(r.push(this._createRefundSetp("您的取消申请已提交，请耐心等待",t)),r.push(this._createRefundSetp("等待卖家处理中,卖家24小时未处理将自动退款",t)));var s=e.sellerDealtime;s&&(1==e.isAgree?(r.push(this._createRefundSetp("卖家已同意退款",s)),r.push(this._createRefundSetp("款项已原路退回中，请注意查收",s))):r.push(this._createRefundSetp("卖家不同意退款，原因："+e.disagreeCause,s)));var o=e.finishTime;o&&(1==e.isAgree?r.push(this._createRefundSetp("退款成功",o)):r.push(this._createRefundSetp("退款关闭，请联系卖家处理",o)));var i=e.closeTime;i&&(2==e.isAgree?r.push(this._createRefundSetp("退款关闭，请联系卖家处理",o)):1==e.isAgree||r.push(this._createRefundSetp("买家取消退款，交易恢复",i)));var n=r[r.length-1];return n.done=!0,n.current=!0,r=r.reverse()}},{key:"_createRefundSetp",value:function(e,r){return{text:e,timestape:r,done:!1,current:!1}}},{key:"_processOrderAction",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=[];e.curRefund&&t.push(s.ACTION.REFUND_DETAIL);var o=e.orderType,i=e.paymentType,n=e.status,u=s.orderUtils.statusActions(o,i,n);if(u){var a=r?u.filter((function(e){return 1!=e.inner})):u;e.actions=t.concat(a)}else e.actions=t}},{key:"_processOrderAddress",value:function(e,r){s.orderUtils.isDeliveryOrder(e.orderType)&&(e.receiveName=r.name+" "+r.sexText,e.receivePhone=r.phone,e.address=r.fullAddress)}},{key:"_processOrderListItem",value:function(e){e.shopName=this.shopName,this._processOrderStatusDesc(e),this._processOrderPrice(e),this._processOrderAction(e,!0);var r=e.orderGoodsInfos;this._processOrderGoods(r),this._processOfflinePayment(e)}},{key:"_processOrderDetail",value:function(e){return e.shopName=this.shopName,this._processOrderPaymentText(e),this._processOrderStatusDesc(e),this._processOrderRefund(e),this._processOrderTrace(e),this._processOrderDetailDelivery(e),this._processOrderPrice(e),this._processOrderAction(e),this._processOrderGoods(e.orderGoodsInfos),this._processTypeFlag(e),this._processOfflinePayment(e),e}},{key:"_processOfflinePayment",value:function(e){if(e.orderType==s.TYPE.OFFLINE)return e.orderGoodsInfos=[{imageUrl:"http://img.leshare.shop/shop/other/wechat_pay.png",goodsName:"微信支付 "+e.finalPrice+"元",goodsPrice:e.finalPrice,count:1}],e}},{key:"_processTypeFlag",value:function(e){var r=e.orderType;return e.isFoodOrder=s.orderUtils.isFoodOrder(r),e.isDeliveryOrder=s.orderUtils.isDeliveryOrder(r),e.isInShopOrder=s.orderUtils.isInShopOrder(r),e.isMallOrder=s.orderUtils.isMallOrder(r),e}},{key:"_processOrderPaymentText",value:function(e){e.paymentText=s.orderUtils.paymentType(e.paymentType)}},{key:"_processOrderPrice",value:function(e){e.postFee=this._fixedPrice(e.postFee),e.dealPrice=this._fixedPrice(e.dealPrice),e.finalPrice=this._fixedPrice(e.finalPrice),e.couponPrice=this._fixedPrice(e.couponPrice),e.reduceFee=this._fixedPrice(e.reduceFee),e.bonusPrice=this._fixedPrice(e.bonusPrice)}},{key:"_fixedPrice",value:function(e){return null==e||isNaN(Number(e))?null:e.toFixed(2)}},{key:"_processOrderStatusDesc",value:function(e){var r=e.status,t=e.orderType;if(e.statusText=s.orderUtils.statusName(t,r),e.statusDesc=s.orderUtils.statusDesc(e,r),7==e.status&&e.orderCloseNote){var o=e.orderCloseNote;e.statusDesc="订单已关闭，关闭原因："+o.note}}},{key:"_processOrderDetailDelivery",value:function(e){e.deliveryText=s.orderUtils.deliveryType(e.deliveryType)}},{key:"_processOrderTrace",value:function(e){null!=e.orderExpress&&(e.isExpress=!0)}},{key:"_processOrderRefund",value:function(e){var r=e.orderRefunds;null==r||r.length<1||(e.curRefund=r[r.length-1])}},{key:"_processOrderGoods",value:function(e){var r=this;null==e||e.length<1||(e.forEach((function(e){e.imageUrl+="/small"})),null==e||e.length<1||e.forEach((function(e){var t=e.goodsSku;e.skuText=r._processOrderSku(t)})))}},{key:"_processOrderSku",value:function(e){var r="";return e&&""!=e&&(r=e.replace(/:/g,",")),r}}]),a}();exports.default=a;