Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=r(require("./base.js")),n=r(require("./../utils/PageForMallwm.js"));function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(r){function u(){return i(this,u),o(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default),e(u,null,[{key:"detail",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/detail?id="+e;return this.get(t).then((function(e){return e}))}},{key:"apply",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/apply";return this.post(t,e).then((function(e){return e}))}},{key:"sign",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/sign";return this.post(t,e).then((function(e){return e}))}},{key:"record",value:function(){var e=this.jifenShopApiUrl+"/pjh/pjh/record";return new n.default(e)}},{key:"recordForStaff",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/recordForStaff?id="+e;return this.get(t).then((function(e){return e}))}},{key:"checkSign",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/checkSign";return this.post(t,e).then((function(e){return e}))}},{key:"checkLeaderIdentity",value:function(){var e=this.jifenShopApiUrl+"/pjh/pjhleader/checkLeaderIdentity";return this.get(e).then((function(e){return e}))}},{key:"getSessionList",value:function(){var e=this.jifenShopApiUrl+"/pjh/pjhleader/getSessionList";return this.get(e).then((function(e){return e}))}},{key:"addSession",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/addSession";return this.post(t,e).then((function(e){return e}))}},{key:"editSessionTraffic",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/editSessionTraffic";return this.post(t,e).then((function(e){return e}))}},{key:"editSessionProgress",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/editSessionProgress";return this.post(t,e).then((function(e){return e}))}},{key:"delSession",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/delSession";return this.post(t,e).then((function(e){return e}))}},{key:"editSession",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/editSession";return this.post(t,e).then((function(e){return e}))}},{key:"addLocation",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/addLocation";return this.post(t,e).then((function(e){return e}))}},{key:"pjhGetSessionList",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjh/getSessionList";return this.post(t,e).then((function(e){return e}))}},{key:"pjhGetSessionLocation",value:function(e){var t=this.jifenShopApiUrl+"/pjh/pjhleader/getSessionLocation";return this.get(t,e).then((function(e){return e}))}}]),u}();exports.default=u;