<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view class="container" wx:if="{{init}}">
    <view class="detail-box" style="background:#56a2cf">
        <view class="detail-title row-center">
            <text class="primary lg" style="height:100rpx;font-size:14px;line-height:100rpx;color:#fff;">产品批号：{{lotNoCode}}</text>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb" wx:for="{{detail}}" wx:for-index="key" wx:key="id">
        <view class="tr bg-w">
            <view class="title">批号</view>
            <view class="value">{{item.sLotNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">生产单号</view>
            <view class="value">{{item.productionErpNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">产品代码</view>
            <view class="value">{{item.productID}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">产品名称</view>
            <view class="value">{{item.productName}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">建单时间</view>
            <view class="value">{{item.planDate}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">生产时间</view>
            <view class="value">{{item.productionDate}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">完成时间</view>
            <view class="value">{{item.productionDateEnd}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">生产数量</view>
            <view class="value">{{item.productionAmount}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">操作员</view>
            <view class="value">{{item.operator}}</view>
        </view>
        <view bindtap="clickCheck" class="tr bg-w" id="{{key}}">
            <view class="title">操作</view>
            <view class="value" style="color:#21b327">点击查看</view>
        </view>
    </view>
</view>
