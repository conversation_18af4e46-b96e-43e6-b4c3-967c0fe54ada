/**
 * 主动调试探索器
 * 发现新方法时主动调试，直到成功
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class ActiveDebugExplorer {
    constructor() {
        // 已知的认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';

        // 从源码中发现的新API端点和方法
        this.newDiscoveries = {
            // 从源码中发现的更多API
            apis: [
                'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt',
                'https://wap.exijiu.com/index.php/API/Member/getJwt',
                'https://apimallwm.exijiu.com/api/v2/jifenCrm/createJwt',
                'https://apimallwm.exijiu.com/api/v2/jifenCrm/getBindInfo',
                'https://apimallwm.exijiu.com/api/v2/jifenCrm/sureBind',
                'https://apimallwm.exijiu.com/api/v2/jifenCrm/getUpgradeStatus',
                'https://statistics.exijiu.com/garden/wechat/login',
                'https://statistics.exijiu.com/garden/wechat/auth',
                'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                'https://wap.exijiu.com/index.php/API/Member/saveLastAuth',
                'https://wap.exijiu.com/index.php/API/Member/hasDataMsCenterUser',
                'https://wap.exijiu.com/index.php/API/Member/getYonyouMemberInfo',
                'https://wap.exijiu.com/index.php/API/Member/pointsRecord',
                'https://apimallwm.exijiu.com/api/member/member/isSubscribe',
                'https://apimallwm.exijiu.com/api/member/member/unbindwechat',
                'https://apimallwm.exijiu.com/api/member/birthday/is_show',
                'https://apimallwm.exijiu.com/api/member/birthday/receive'
            ],

            // 从源码中发现的参数和字段
            parameters: [
                'login_code', 'Authorization', 'authorized_token', 'third_session',
                'expire_time', 'expireTime', 'jwt', 'auth', 'crmtoken',
                'unionid', 'openid', 'user_id', 'member_id', 'memberInfo',
                'encryptedData', 'iv', 'code', 'session_key', 'rawData',
                'signature', 'appId', 'secret'
            ],

            // 从源码中发现的方法
            methods: [
                'getJifenShopJwt', 'jifenCrmCreateJwt', 'getAuthData', 'isJifenLogined',
                'checkLoginCode', 'doLogin', 'register', 'checkSession',
                'saveLastAuth', 'getYonyouMemberInfo', 'hasDataMsCenterUser'
            ]
        };

        this.debugResults = {
            successfulMethods: [],
            failedMethods: [],
            newTokens: [],
            workingEndpoints: []
        };

        console.log('🔧 主动调试探索器初始化完成');
        console.log('🎯 将主动调试所有发现的新方法，直到成功');
    }

    /**
     * 主动探索和调试所有新发现的API
     */
    async activelyExploreAllAPIs() {
        console.log('\n🚀 开始主动探索所有新发现的API...');
        console.log(`📊 将测试 ${this.newDiscoveries.apis.length} 个API端点`);

        for (let i = 0; i < this.newDiscoveries.apis.length; i++) {
            const api = this.newDiscoveries.apis[i];
            console.log(`\n🔍 [${i+1}/${this.newDiscoveries.apis.length}] 主动调试API: ${api}`);

            const result = await this.debugAPIEndpoint(api);

            if (result.success) {
                console.log('✅ 调试成功！');
                this.debugResults.successfulMethods.push(result);
                this.debugResults.workingEndpoints.push(api);

                // 如果发现新的token，立即验证
                if (result.newToken) {
                    console.log('🔑 发现新Token，立即验证...');
                    await this.validateNewToken(result.newToken, api);
                }
            } else {
                console.log('❌ 调试失败，尝试其他方法...');
                this.debugResults.failedMethods.push({api, reason: result.reason});
            }
        }

        return this.debugResults;
    }

    /**
     * 调试单个API端点
     */
    async debugAPIEndpoint(api) {
        console.log(`🔧 开始调试: ${api}`);

        // 尝试多种认证方法
        const authMethods = [
            { name: '无认证', headers: this.getBaseHeaders() },
            { name: '仅login_code', headers: {...this.getBaseHeaders(), 'login_code': this.loginCode} },
            { name: '仅Authorization', headers: {...this.getBaseHeaders(), 'Authorization': this.validAuth} },
            { name: '完整认证', headers: {...this.getBaseHeaders(), 'login_code': this.loginCode, 'Authorization': this.validAuth} },
            { name: 'Bearer格式', headers: {...this.getBaseHeaders(), 'Authorization': `Bearer ${this.validAuth}`, 'login_code': this.loginCode} },
            { name: '自定义头', headers: {...this.getBaseHeaders(), 'X-Auth-Token': this.validAuth, 'X-Login-Code': this.loginCode} }
        ];

        // 尝试不同的HTTP方法
        const httpMethods = ['GET', 'POST'];

        for (const httpMethod of httpMethods) {
            console.log(`  🧪 尝试 ${httpMethod} 方法...`);

            for (const authMethod of authMethods) {
                console.log(`    🔐 尝试认证方式: ${authMethod.name}`);

                try {
                    const result = await this.makeRequest(api, httpMethod, null, authMethod.headers);

                    if (result.success || result.status === 200) {
                        console.log(`    ✅ 成功！方法: ${httpMethod}, 认证: ${authMethod.name}`);

                        // 分析响应数据
                        const analysis = this.analyzeResponse(result.data);

                        return {
                            success: true,
                            api: api,
                            method: httpMethod,
                            auth: authMethod.name,
                            data: result.data,
                            analysis: analysis,
                            newToken: analysis.extractedToken
                        };
                    } else {
                        console.log(`    ❌ 失败 (${result.status}): ${result.data?.msg || '未知错误'}`);
                    }

                } catch (error) {
                    console.log(`    💥 异常: ${error.message}`);
                }
            }
        }

        return { success: false, reason: '所有方法都失败' };
    }

    /**
     * 分析API响应数据
     */
    analyzeResponse(data) {
        const analysis = {
            hasToken: false,
            extractedToken: null,
            tokenFields: [],
            hasUserInfo: false,
            userInfo: {},
            hasError: false,
            errorInfo: null
        };

        if (!data || typeof data !== 'object') {
            return analysis;
        }

        // 检查是否有错误
        if (data.err && data.err !== 0) {
            analysis.hasError = true;
            analysis.errorInfo = { code: data.err, message: data.msg };
        }

        // 提取可能的token字段
        const tokenFields = ['jwt', 'token', 'auth', 'authorization', 'access_token', 'refresh_token', 'crmtoken'];

        for (const field of tokenFields) {
            if (data[field] && typeof data[field] === 'string' && data[field].length > 20) {
                analysis.hasToken = true;
                analysis.extractedToken = data[field];
                analysis.tokenFields.push(field);
                console.log(`    🔑 发现Token字段: ${field} = ${data[field].substring(0, 50)}...`);
            }
        }

        // 提取用户信息
        if (data.memberInfo || data.user || data.member) {
            analysis.hasUserInfo = true;
            analysis.userInfo = data.memberInfo || data.user || data.member;
            console.log(`    👤 发现用户信息: ${JSON.stringify(analysis.userInfo)}`);
        }

        return analysis;
    }

    /**
     * 验证新发现的Token
     */
    async validateNewToken(token, sourceAPI) {
        console.log(`🔍 验证新Token (来源: ${sourceAPI})`);
        console.log(`🔑 Token: ${token.substring(0, 50)}...`);

        // 尝试用新Token访问各种API
        const testAPIs = [
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
            'https://wap.exijiu.com/index.php/API/Member/getJifenShopMemberInfo',
            'https://apimallwm.exijiu.com/api/member/birthday/is_show'
        ];

        let validationResults = [];

        for (const testAPI of testAPIs) {
            console.log(`  🧪 测试API: ${testAPI}`);

            try {
                const result = await this.makeRequest(testAPI, 'GET', null, {
                    ...this.getBaseHeaders(),
                    'Authorization': token,
                    'login_code': this.loginCode
                });

                if (result.success) {
                    console.log(`  ✅ 新Token有效！`);
                    validationResults.push({ api: testAPI, success: true, data: result.data });

                    // 保存新Token
                    this.debugResults.newTokens.push({
                        token: token,
                        source: sourceAPI,
                        validatedWith: testAPI,
                        timestamp: new Date().toISOString()
                    });

                    return true;
                } else {
                    console.log(`  ❌ 新Token无效 (${result.status})`);
                    validationResults.push({ api: testAPI, success: false, status: result.status });
                }

            } catch (error) {
                console.log(`  💥 验证异常: ${error.message}`);
            }
        }

        return false;
    }

    /**
     * 尝试生成新的Authorization
     */
    async attemptNewAuthorizationGeneration() {
        console.log('\n🔄 尝试生成新的Authorization...');

        // 方法1: 使用getJwt API
        console.log('📋 方法1: 使用getJwt API');
        try {
            const result1 = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API/Member/getJwt',
                'GET',
                null,
                {...this.getBaseHeaders(), 'login_code': this.loginCode}
            );

            if (result1.success && result1.data.jwt) {
                console.log('✅ 方法1成功！生成新Authorization');
                const newAuth = result1.data.jwt;
                console.log(`🔑 新Authorization: ${newAuth.substring(0, 50)}...`);

                // 立即验证新Authorization
                const isValid = await this.validateNewToken(newAuth, 'getJwt API');
                if (isValid) {
                    console.log('🎉 新Authorization验证成功！');
                    return newAuth;
                }
            }
        } catch (error) {
            console.log('❌ 方法1失败:', error.message);
        }

        // 方法2: 使用jifenCrm API
        console.log('\n📋 方法2: 使用jifenCrm API');
        try {
            const result2 = await this.makeRequest(
                'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt',
                'GET',
                null,
                {...this.getBaseHeaders(), 'login_code': this.loginCode, 'Authorization': this.validAuth}
            );

            if (result2.success && (result2.data.auth || result2.data.jwt)) {
                console.log('✅ 方法2成功！生成新Token');
                const newToken = result2.data.auth || result2.data.jwt;
                console.log(`🔑 新Token: ${newToken.substring(0, 50)}...`);

                const isValid = await this.validateNewToken(newToken, 'jifenCrm API');
                if (isValid) {
                    console.log('🎉 新Token验证成功！');
                    return newToken;
                }
            }
        } catch (error) {
            console.log('❌ 方法2失败:', error.message);
        }

        // 方法3: 尝试微信登录流程
        console.log('\n📋 方法3: 尝试微信登录流程');
        await this.attemptWechatLoginFlow();

        return null;
    }

    /**
     * 尝试微信登录流程
     */
    async attemptWechatLoginFlow() {
        console.log('🔍 尝试微信登录流程...');

        // 模拟微信登录参数
        const wechatParams = {
            code: 'mock_wx_code_' + Date.now(),
            encryptedData: 'mock_encrypted_data',
            iv: 'mock_iv',
            rawData: JSON.stringify({
                nickName: 'TestUser',
                gender: 1,
                language: 'zh_CN',
                city: 'Beijing',
                province: 'Beijing',
                country: 'China',
                avatarUrl: 'https://example.com/avatar.jpg'
            }),
            signature: 'mock_signature'
        };

        const wechatAPIs = [
            'https://statistics.exijiu.com/garden/wechat/login',
            'https://statistics.exijiu.com/garden/wechat/auth'
        ];

        for (const api of wechatAPIs) {
            console.log(`🧪 测试微信API: ${api}`);

            try {
                // 尝试GET方法
                const getResult = await this.makeRequest(api, 'GET', null, {
                    ...this.getBaseHeaders(),
                    'login_code': this.loginCode
                });

                console.log(`  GET结果: ${getResult.status} - ${getResult.data?.msg || '无消息'}`);

                // 尝试POST方法
                const postResult = await this.makeRequest(api, 'POST', wechatParams, {
                    ...this.getBaseHeaders(),
                    'login_code': this.loginCode
                });

                console.log(`  POST结果: ${postResult.status} - ${postResult.data?.msg || '无消息'}`);

                if (postResult.success || getResult.success) {
                    const successResult = postResult.success ? postResult : getResult;
                    const analysis = this.analyzeResponse(successResult.data);

                    if (analysis.hasToken) {
                        console.log('🔑 微信登录流程发现新Token！');
                        await this.validateNewToken(analysis.extractedToken, api);
                    }
                }

            } catch (error) {
                console.log(`  💥 微信API异常: ${error.message}`);
            }
        }
    }

    /**
     * 深度参数探测
     */
    async deepParameterProbing() {
        console.log('\n🔍 开始深度参数探测...');

        const targetAPI = 'https://wap.exijiu.com/index.php/API/Member/getJwt';

        // 尝试各种参数组合
        const paramCombinations = [
            // 基础参数
            { login_code: this.loginCode },
            { token: this.loginCode },
            { auth: this.loginCode },
            { code: this.loginCode },

            // 组合参数
            { login_code: this.loginCode, Authorization: this.validAuth },
            { login_code: this.loginCode, token: this.validAuth },
            { login_code: this.loginCode, refresh: true },
            { login_code: this.loginCode, force: true },

            // 微信相关参数
            { login_code: this.loginCode, platform: 'wechat' },
            { login_code: this.loginCode, source: 'miniprogram' },
            { login_code: this.loginCode, appid: 'wx489f950decfeb93e' },

            // 时间戳参数
            { login_code: this.loginCode, timestamp: Date.now() },
            { login_code: this.loginCode, t: Date.now() },
            { login_code: this.loginCode, _t: Date.now() },

            // 版本参数
            { login_code: this.loginCode, version: '3.2.6' },
            { login_code: this.loginCode, v: '3.2.6' },
            { login_code: this.loginCode, api_version: '2' }
        ];

        for (const params of paramCombinations) {
            console.log(`🧪 测试参数组合: ${JSON.stringify(params)}`);

            try {
                // 作为查询参数
                const queryResult = await this.makeRequestWithQuery(targetAPI, params);
                if (queryResult.success && queryResult.data.jwt) {
                    console.log('✅ 查询参数成功！发现新JWT');
                    await this.validateNewToken(queryResult.data.jwt, 'getJwt with query params');
                }

                // 作为POST数据
                const postResult = await this.makeRequest(targetAPI, 'POST', params, this.getBaseHeaders());
                if (postResult.success && postResult.data.jwt) {
                    console.log('✅ POST数据成功！发现新JWT');
                    await this.validateNewToken(postResult.data.jwt, 'getJwt with POST data');
                }

            } catch (error) {
                console.log(`  💥 参数测试异常: ${error.message}`);
            }
        }
    }

    /**
     * 智能重试机制
     */
    async intelligentRetry() {
        console.log('\n🔄 启动智能重试机制...');

        const retryStrategies = [
            {
                name: '延迟重试',
                action: async () => {
                    console.log('⏰ 等待5秒后重试...');
                    await this.sleep(5000);
                    return await this.attemptNewAuthorizationGeneration();
                }
            },
            {
                name: '更换User-Agent',
                action: async () => {
                    console.log('🔄 更换User-Agent重试...');
                    const oldUA = this.getBaseHeaders()['User-Agent'];
                    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
                    const result = await this.attemptNewAuthorizationGeneration();
                    this.userAgent = oldUA; // 恢复原来的
                    return result;
                }
            },
            {
                name: '模拟不同设备',
                action: async () => {
                    console.log('📱 模拟不同设备重试...');
                    const deviceHeaders = {
                        ...this.getBaseHeaders(),
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50',
                        'X-Requested-With': 'com.tencent.mm',
                        'Sec-Fetch-Site': 'cross-site',
                        'Sec-Fetch-Mode': 'cors'
                    };

                    const result = await this.makeRequest(
                        'https://wap.exijiu.com/index.php/API/Member/getJwt',
                        'GET',
                        null,
                        deviceHeaders
                    );

                    if (result.success && result.data.jwt) {
                        await this.validateNewToken(result.data.jwt, 'device simulation');
                        return result.data.jwt;
                    }
                    return null;
                }
            }
        ];

        for (const strategy of retryStrategies) {
            console.log(`🧪 尝试策略: ${strategy.name}`);

            try {
                const result = await strategy.action();
                if (result) {
                    console.log(`✅ 策略 ${strategy.name} 成功！`);
                    return result;
                }
            } catch (error) {
                console.log(`❌ 策略 ${strategy.name} 失败: ${error.message}`);
            }
        }

        return null;
    }

    /**
     * 获取基础请求头
     */
    getBaseHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': this.userAgent || 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
    }

    /**
     * 带查询参数的请求
     */
    async makeRequestWithQuery(baseUrl, params) {
        const queryString = Object.keys(params)
            .map(key => `${key}=${encodeURIComponent(params[key])}`)
            .join('&');

        const fullUrl = `${baseUrl}?${queryString}`;

        return this.makeRequest(fullUrl, 'GET', null, this.getBaseHeaders());
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || this.getBaseHeaders();

        return new Promise((resolve) => {
            const urlObj = new URL(url);

            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            data: responseData,
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的主动调试探索
     */
    async runCompleteActiveDebug() {
        console.log('🚀 开始完整的主动调试探索...');
        console.log('🎯 发现新方法时主动调试，直到成功');

        try {
            // 第一阶段：探索所有API
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 主动探索所有API');
            console.log('='.repeat(60));
            const apiResults = await this.activelyExploreAllAPIs();

            // 第二阶段：尝试生成新Authorization
            console.log('\n' + '='.repeat(60));
            console.log('🔄 第二阶段: 尝试生成新Authorization');
            console.log('='.repeat(60));
            const newAuth = await this.attemptNewAuthorizationGeneration();

            // 第三阶段：深度参数探测
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第三阶段: 深度参数探测');
            console.log('='.repeat(60));
            await this.deepParameterProbing();

            // 第四阶段：智能重试
            if (!newAuth && this.debugResults.newTokens.length === 0) {
                console.log('\n' + '='.repeat(60));
                console.log('🔄 第四阶段: 智能重试机制');
                console.log('='.repeat(60));
                const retryResult = await this.intelligentRetry();

                if (retryResult) {
                    console.log('🎉 智能重试成功！');
                }
            }

            // 输出最终结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 主动调试探索结果');
            console.log('='.repeat(60));

            console.log(`\n📈 探索统计:`);
            console.log(`  ✅ 成功的方法: ${this.debugResults.successfulMethods.length}`);
            console.log(`  ❌ 失败的方法: ${this.debugResults.failedMethods.length}`);
            console.log(`  🔑 发现的新Token: ${this.debugResults.newTokens.length}`);
            console.log(`  🌐 可用的端点: ${this.debugResults.workingEndpoints.length}`);

            if (this.debugResults.successfulMethods.length > 0) {
                console.log('\n🎉 成功的方法详情:');
                this.debugResults.successfulMethods.forEach((method, index) => {
                    console.log(`  ${index + 1}. ${method.api}`);
                    console.log(`     方法: ${method.method}, 认证: ${method.auth}`);
                    if (method.newToken) {
                        console.log(`     新Token: ${method.newToken.substring(0, 50)}...`);
                    }
                });
            }

            if (this.debugResults.newTokens.length > 0) {
                console.log('\n🔑 发现的新Token:');
                this.debugResults.newTokens.forEach((tokenInfo, index) => {
                    console.log(`  ${index + 1}. 来源: ${tokenInfo.source}`);
                    console.log(`     Token: ${tokenInfo.token.substring(0, 50)}...`);
                    console.log(`     验证API: ${tokenInfo.validatedWith}`);
                    console.log(`     时间: ${tokenInfo.timestamp}`);
                });
            }

            if (this.debugResults.workingEndpoints.length > 0) {
                console.log('\n🌐 可用的API端点:');
                this.debugResults.workingEndpoints.forEach((endpoint, index) => {
                    console.log(`  ${index + 1}. ${endpoint}`);
                });
            }

            // 成功判断
            const hasNewTokens = this.debugResults.newTokens.length > 0;
            const hasWorkingEndpoints = this.debugResults.workingEndpoints.length > 0;
            const hasSuccessfulMethods = this.debugResults.successfulMethods.length > 0;

            if (hasNewTokens || hasWorkingEndpoints || hasSuccessfulMethods) {
                console.log('\n🎊 主动调试探索成功！');
                console.log('🔑 发现了新的认证方法或Token！');

                if (hasNewTokens) {
                    console.log('💡 建议: 使用新发现的Token进行后续操作');
                }

                if (hasWorkingEndpoints) {
                    console.log('💡 建议: 进一步探索可用的API端点');
                }

                return {
                    success: true,
                    newTokens: this.debugResults.newTokens,
                    workingEndpoints: this.debugResults.workingEndpoints,
                    successfulMethods: this.debugResults.successfulMethods
                };
            } else {
                console.log('\n😔 主动调试探索未发现新的有效方法');
                console.log('💡 但获得了大量有价值的调试信息');

                return {
                    success: false,
                    debugInfo: this.debugResults
                };
            }

        } catch (error) {
            console.log('\n❌ 主动调试探索异常:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = ActiveDebugExplorer;

// 如果直接运行此文件
if (require.main === module) {
    const explorer = new ActiveDebugExplorer();

    console.log('🔍 主动调试探索器');
    console.log('🎯 发现新方法时主动调试，直到成功');
    console.log('🚀 将尝试所有可能的方法和参数组合');
    console.log('');

    // 运行完整的主动调试
    explorer.runCompleteActiveDebug().then(result => {
        if (result.success) {
            console.log('\n🎉 主动调试探索圆满成功！');
            console.log('🔑 发现了新的认证方法或有效Token！');

            if (result.newTokens && result.newTokens.length > 0) {
                console.log('\n🎊 最重要的发现 - 新Token:');
                result.newTokens.forEach(token => {
                    console.log(`🔑 ${token.token.substring(0, 100)}...`);
                });
            }
        } else {
            console.log('\n🤔 主动调试探索未找到新的有效方法');
            console.log('💡 但这个过程本身就是有价值的学习');
        }
    }).catch(error => {
        console.error('💥 主动调试异常:', error);
    });
}