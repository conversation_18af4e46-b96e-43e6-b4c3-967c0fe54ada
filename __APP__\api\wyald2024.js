Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=r(require("./base.js"));r(require("./../utils/Page.js")),r(require("./../utils/Pages.js")),r(require("./../npm/wepy/lib/wepy.js"));function r(e){return e&&e.__esModule?e:{default:e}}function n(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,u){try{var o=t[i](u),a=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var o=function(r){function o(){return i(this,o),u(this,(o.__proto__||Object.getPrototypeOf(o)).apply(this,arguments))}var a,s,p,c;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,t.default),e(o,null,[{key:"getMyCiphertext",value:(c=n(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/wuyiailaodong2024/shengguang/getMyCiphertext",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"getMyCiphertextForFanfestival",value:(p=n(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/fanfestival2024/duiba/getMyCiphertext",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"getMyCiphertextForMidautumnfestival",value:(s=n(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/activity/midautumnfestival2024/getMyCiphertext",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"getMyCiphertextForChineseYear2025",value:(a=n(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/activity/chineseyear2025/getMyCiphertext",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})}]),o}();exports.default=o;