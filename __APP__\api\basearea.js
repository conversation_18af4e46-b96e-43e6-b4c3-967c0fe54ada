Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=require("./../utils/HttpBaseArea.js"),r=(e=n)&&e.__esModule?e:{default:e};function u(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(u,o){try{var i=t[u](o),a=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(a).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(a)}("next")}))}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(e){function n(){return o(this,n),i(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}var a,s,c,p,f,h,l,v,g;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,r.default),t(n,null,[{key:"banners",value:(g=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/open/banner/page/top/3").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return g.apply(this,arguments)})},{key:"eventLogE0",value:(v=u(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/open/event-log/E0",t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"eventLogE1",value:(l=u(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/open/event-log/E1",t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"eventLogE2",value:(h=u(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/open/event-log/E2",t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return h.apply(this,arguments)})},{key:"eventLogE3",value:(f=u(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/open/event-log/E3",t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"roamingCount",value:(p=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/open/event-log/roamingCount").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"videos",value:(c=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/open/banner/video/list").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"views",value:(s=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/open/banner/view/list").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"userVip",value:(a=u(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/open/user-vip",t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})}]),n}();exports.default=a;