Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=l(require("./../../npm/wepy/lib/wepy.js")),n=l(require("./../common/cover_panel.js")),r=l(require("./../../utils/Sku.js")),o=l(require("./../../utils/Tips.js")),s=l(require("./discount_badge.js")),i=l(require("./../../utils/Event.js")),u=l(require("./../../utils/Cart.js"));function l(e){return e&&e.__esModule?e:{default:e}}function a(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(o,s){try{var i=t[o](s),u=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(u)}("next")}))}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var f=void 0,p=function(l){function p(){var e,t,i;c(this,p);for(var l=arguments.length,h=Array(l),g=0;g<l;g++)h[g]=arguments[g];return t=i=d(this,(e=p.__proto__||Object.getPrototypeOf(p)).call.apply(e,[this].concat(h))),i.props={},i.data={goods:null,selected:null,price:null,originalPrice:null,display:!1,source:null},i.cartManager=u.default.create(),i.methods={close:function(){this.clear()},select:function(e){var t=e.sku;e.max||(f.select("规格",t),this.selected=t,this.price=f.detail.price,this.originalPrice=f.detail.originalPrice,this.$apply())},confirm:function(){var e=this;return a(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null!=e.selected){t.next=3;break}return o.default.alert("请选择"),t.abrupt("return");case 3:(e.goods.isTips||null==e.goods.isTips)&&o.default.success("已加入购物车"),e.$root.$apply(),e.cartManager.plus(e.goods,e.selected),e.clear();case 7:case"end":return t.stop()}}),t,e)})))()}},i.computed={carts:function(){if(null!=this.cartManager)return this.cartManager.export().carts},sku:function(){var e=this;if(null!=this.goods&&null!=this.carts){var t=(f=new r.default(this.goods)).skuStocks;return t.forEach((function(t){if(0!=t.stock){var n=e.carts.find((function(n){return n.goodsId==e.goods.id&&n.goodsSku==t.sku}));t.max=null!=n&&n.goodsNum>=t.stock}else t.max=!0})),t}}},i.$repeat={},i.$props={DisplayBadge:{"xmlns:v-bind":"","v-bind:goods.sync":"goods"},CoverPanel:{"xmlns:v-bind":"","v-bind:display.sync":"display",zIndex:"1000000","xmlns:v-on":""}},i.$events={CoverPanel:{"v-on:tap":"close"}},i.components={DisplayBadge:s.default,CoverPanel:n.default},d(i,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,t.default.component),e(p,[{key:"onLoad",value:function(){this.source=this.$root.$wxpage.route,i.default.listen(i.default.GOODS_PANEL_PLUS,this.plus.bind(this),this),i.default.listen(i.default.GOODS_PANEL_MINUS,this.minus.bind(this),this)}},{key:"onUnload",value:function(){console.info("popup unload")}},{key:"plus",value:function(e){var t=e.goods,n=e.goodsSku;e.source==this.source&&(this.goods=t,t.goodsSkuInfo&&(null==n||""==n)?this.display=!0:((t.isTips||null==t.isTips)&&o.default.success("已加入购物车"),this.cartManager.plus(t,n),this.clear()),this.$apply())}},{key:"minus",value:function(e){var t=e.goodsId,n=e.goodsSku;if(e.source==this.source){var r=this.cartManager.findByGoodsId(t);if(r.length<1)console.warn("移除购物车的商品不存在");else if(r.length>1&&(null==n||""==n))o.default.alert("请在购物车操作");else{var s=r[0];null==n&&null!=s.goodsSku&&(n=s.goodsSku),this.cartManager.minus(t,n),this.$apply()}}}},{key:"clear",value:function(){f=null,this.selected=null,this.originalPrice=null,this.price=null,this.goods=null,this.display=!1}}]),p}();exports.default=p;