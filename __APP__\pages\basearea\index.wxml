<view>
    <view class="container">
        <view class="bannerBox" wx:if="{{bannerList}}">
            <swiper autoplay="{{autoplay}}" class="swiperBox" duration="{{duration}}" indicatorActiveColor="#ffffff" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
                <swiper-item wx:for="{{bannerList}}" wx:key="index">
                    <view bindtap="bannerJump" class="swiper-item" data-item="{{item}}">
                        <image mode="aspectFill" src="{{item.bgImage.url}}"></image>
                    </view>
                </swiper-item>
            </swiper>
        </view>
        <view class="page-bottom">
            <view class="nav" wx:if="{{!isLogin||!userInfo}}">
                <button bindgetphonenumber="getPhoneNumber" class="nav-item" data-type="1" openType="getPhoneNumber">
                    <image class="nav_pic" mode="aspectFill" src="{{wineStaticImg}}"></image>
                </button>
                <button bindgetphonenumber="getPhoneNumber" class="nav-item" data-type="2" openType="getPhoneNumber">
                    <image class="nav_pic" mode="aspectFill" src="{{orderStaticImg}}"></image>
                </button>
            </view>
            <view class="nav" wx:if="{{isLogin&&userInfo}}">
                <view bindtap="onTapGoWine" class="nav-item">
                    <image class="nav_pic" mode="aspectFill" src="{{wineStaticImg}}"></image>
                </view>
                <view bindtap="onTapGoOrder" class="nav-item">
                    <image class="nav_pic" mode="aspectFill" src="{{orderStaticImg}}"></image>
                </view>
            </view>
            <view class="title">
                <view class="title-top">智慧导览</view>
                <view bindtap="playVideo" class="title-more" data-index="0" wx:if="{{videoList}}">更多>></view>
            </view>
            <view class="video-box">
                <view bindtap="playVideo" class="video-box-left" data-index="0" wx:if="{{videoList[0]}}">
                    <image class="video_poster" mode="aspectFill" src="{{videoList[0]['bgImage']['miniUrl']}}"></image>
                    <image class="play_btn" mode="aspectFill" src="/images/basearea/play.png"></image>
                </view>
                <view class="video-box-right">
                    <view bindtap="playVideo" class="video-box-right-top" data-index="1" wx:if="{{videoList[1]}}">
                        <image class="video_poster" mode="aspectFill" src="{{videoList[1]['bgImage']['cropUrl']}}"></image>
                        <image class="play_btn" mode="aspectFill" src="/images/basearea/play.png"></image>
                    </view>
                    <view bindtap="playVideo" class="video-box-right-bottom" data-index="2" wx:if="{{videoList[2]}}">
                        <image class="video_poster" mode="aspectFill" src="{{videoList[2]['bgImage']['cropUrl']}}"></image>
                        <image class="play_btn" mode="aspectFill" src="/images/basearea/play.png"></image>
                    </view>
                </view>
            </view>
            <view bindtap="goMap" class="live-action-box">
                <image mode="aspectFill" src="{{mapStaticImg}}"></image>
            </view>
            <view class="title">
                <view class="title-top">实景漫游</view>
            </view>
            <view class="list-box">
                <view bindtap="goLiveAction" class="list-item" data-item="{{item}}" wx:for="{{liveActionList}}" wx:key="index">
                    <view class="list-left">
                        <image mode="aspectFill" src="{{item.bgImage.url}}"></image>
                    </view>
                    <view class="list-right">
                        <view class="list-title">{{item.title}}</view>
                        <view class="list-desc">{{item.subTitle}}</view>
                        <view class="list-view">
                            <image mode="aspectFill" src="/images/basearea/view.png"></image>
                            <text>{{item.viewCount}}</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="footer">
                <view class="icon">
                    <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/footer.png?v=111"></image>
                </view>
                <view class="text">公安备案 备案证书编号：52030099007-18004</view>
            </view>
        </view>
    </view>
</view>
