/**
 * 简化版微信协议测试
 */

const https = require('https');
const http = require('http');

// 你的Token信息
const authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
const loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';

console.log('开始测试微信协议...');

// 解析Token
function decodeJWT(token) {
    try {
        const parts = token.split('.');
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        return payload;
    } catch (e) {
        return null;
    }
}

const authPayload = decodeJWT(authToken);
const loginPayload = decodeJWT(loginCode);

console.log('会员ID:', authPayload.memberInfo.id);
console.log('UnionID:', loginPayload.unionid);
console.log('Authorization过期时间:', new Date(authPayload.expireTime * 1000).toLocaleString('zh-CN'));
console.log('Login Code过期时间:', new Date(loginPayload.expire * 1000).toLocaleString('zh-CN'));

// 构建请求头
const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
    'Authorization': authToken,
    'login_code': loginCode
};

console.log('\n请求头信息:');
console.log(JSON.stringify(headers, null, 2));

// 测试API调用
function testAPI(path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'library.hankin.ufutx.cn',
            port: 80,
            path: '/api' + path,
            method: 'GET',
            headers: headers
        };

        console.log(`\n测试API: ${path}`);
        console.log('完整URL:', `http://library.hankin.ufutx.cn/api${path}`);

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                try {
                    const jsonData = JSON.parse(data);
                    console.log('响应:', JSON.stringify(jsonData, null, 2));
                    resolve(jsonData);
                } catch (e) {
                    console.log('原始响应:', data);
                    resolve(data);
                }
            });
        });

        req.on('error', (error) => {
            console.error('请求错误:', error.message);
            reject(error);
        });

        req.setTimeout(10000, () => {
            console.log('请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

// 执行测试
async function runTests() {
    try {
        console.log('\n=== 开始API测试 ===');
        
        // 测试1: 获取用户信息
        await testAPI('/garden/Gardenmemberinfo/getMemberInfo');
        
        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 测试2: 获取土地信息
        await testAPI('/garden/sorghum/index');
        
        console.log('\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

runTests();
