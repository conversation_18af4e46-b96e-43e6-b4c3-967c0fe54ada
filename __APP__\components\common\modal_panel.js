Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=o(require("./../../npm/wepy/lib/wepy.js")),t=o(require("./cover_panel.js"));function o(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(o){function a(){var e,o,u;r(this,a);for(var c=arguments.length,i=Array(c),p=0;p<c;p++)i[p]=arguments[p];return o=u=n(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(i))),u.props={sku:{}},u.data={},u.methods={},u.components={CoverPanel:t.default},n(u,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,e.default.component),a}();exports.default=a;