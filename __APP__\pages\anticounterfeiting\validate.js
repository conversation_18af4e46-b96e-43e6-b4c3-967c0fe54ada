Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=s(require("./../../npm/wepy/lib/wepy.js")),n=s(require("./../../api/member.js")),r=s(require("./../../mixins/input.js")),a=(s(require("./../../api/comment.js")),s(require("./../../mixins/base.js"))),o=s(require("./../../api/product.js")),i=s(require("./../../utils/Tips.js")),u=s(require("./../../components/common/loading.js"));function s(e){return e&&e.__esModule?e:{default:e}}function l(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(a,o){try{var i=t[a](o),u=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(u)}("next")}))}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(s){function d(){var e,t,s;c(this,d);for(var p=arguments.length,g=Array(p),m=0;m<p;m++)g[m]=arguments[m];return t=s=f(this,(e=d.__proto__||Object.getPrototypeOf(d)).call.apply(e,[this].concat(g))),s.data={validatenum:{},dataMsCenterUser:!1,code:"",userInfo:!1,isFwInvisibleCode:!1,fwValidateTimeLogId:0,showOld:!1,os:"",phone_model:"",brower:"",networkType:""},s.methods={showOldBox:function(){this.showOld=!0,this.$apply()},confirm:function(e){var t=this,r=e.detail;return l(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.dataMsCenterUser){e.next=3;break}return t.goIndexLogin(),e.abrupt("return");case 3:return e.next=5,n.default.buryingPoint(153,t.userInfo.id?t.userInfo.id:"",t.os,t.brower,t.phone_model,t.networkType);case 5:if(Object.assign(t.validatenum,r.value),0!=t.data.validatenum.theLast4Digits.length){e.next=10;break}i.default.confirm("您好！防伪码不能为空。"),e.next=37;break;case 10:if(!(t.data.validatenum.theLast4Digits.length<4)){e.next=14;break}i.default.confirm("您好！防伪码不能少于4位!"),e.next=37;break;case 14:if(!(t.data.validatenum.theLast4Digits.length>=5)){e.next=18;break}i.default.confirm("您好！您输入的防伪码超过4位，请您核对!"),e.next=37;break;case 18:if(/^[0-9]*$/.test(t.data.validatenum.theLast4Digits)){e.next=22;break}i.default.confirm("您好！您输入的防伪码格式不正确，请您核对!"),e.next=37;break;case 22:return i.default.longLoading("验证中..."),e.prev=23,e.next=26,o.default.validateAndGetScore(t.validatenum);case 26:return a=e.sent,i.default.loaded(),e.next=30,i.default.confirm(a.tips);case 30:e.next=37;break;case 32:return e.prev=32,e.t0=e.catch(23),i.default.loaded(),e.next=37,i.default.confirm(e.t0.message);case 37:case"end":return e.stop()}}),e,t,[[23,32]])})))()},goToValidate:function(){var e=this;return l(regeneratorRuntime.mark((function t(){var n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.dataMsCenterUser){t.next=3;break}return e.goIndexLogin(),t.abrupt("return");case 3:return t.next=5,o.default.upValidateResult(0,0,e.validatenum.qrCode);case 5:if(n=t.sent,console.log("goToValidate ret",n),e.fwValidateTimeLogId=n.id,!e.isFwInvisibleCode){t.next=10;break}return t.abrupt("return",wx.openEmbeddedMiniProgram({appId:"wx847c3c9ebcc8de1b",path:"pages/landing/landing?prd=Xijiu Spirits&backSuccess=true&backCancel=false&backTimeout=true",envVersion:"release",success:function(e){console.log(e)},fail:function(e){console.log(e,"测试")},complete:function(e){console.log(e,"结束回调")}}));case 10:case"end":return t.stop()}}),t,e)})))()}},s.computed={},s.components={Loading:u.default},s.mixins=[a.default,r.default],s.config={navigationBarTitleText:"进一步验证",navigationBarBackgroundColor:"#f3f3f3",navigationBarTextStyle:"black"},f(s,t)}var p,g;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,t.default.page),e(d,[{key:"onLoad",value:(g=l(regeneratorRuntime.mark((function e(r){var a,u=this,s=r.qrCode,l=r.isFwInvisibleCode;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.userInfo=t.default.getStorageSync("userInfo"),e.next=3,n.default.hasDataMsCenterUser();case 3:(a=e.sent)&&(this.dataMsCenterUser=a),this.validatenum.qrCode=s,this.isFwInvisibleCode=l,this.getUserSystemInfo(),this.getNetwork(),this.$apply(),this.loaded(),wx.onAppShow((function(e){if(console.log("onAppShow",u.fwValidateTimeLogId,e),"wx847c3c9ebcc8de1b"==e.referrerInfo.appId)try{1==e.referrerInfo.extraData.authentic?(i.default.confirm("产品防伪验证成功"),o.default.upValidateResult(u.fwValidateTimeLogId,1,"")):"timed out"==e.referrerInfo.extraData.sessionResult?(i.default.confirm("验证超时，请重试！"),o.default.upValidateResult(u.fwValidateTimeLogId,3,"")):(i.default.confirm("防伪验证失败"),o.default.upValidateResult(u.fwValidateTimeLogId,2,""))}finally{o.default.upValidateResult(u.fwValidateTimeLogId,4,""),console.log("finally",u.fwValidateTimeLogId),u.fwValidateTimeLogId=0}}));case 12:case"end":return e.stop()}}),e,this)}))),function(e){return g.apply(this,arguments)})},{key:"onUnload",value:function(){}},{key:"getUserSystemInfo",value:function(){var e=this;wx.getSystemInfo({success:function(t){console.log(t),e.os=t.system?t.system:"",e.phone_model=t.model?t.model:"",e.brower="iPhone"==t.model?"safari":"chrome",e.$apply()}})}},{key:"getNetwork",value:function(){var e=this;wx.getNetworkType({success:function(t){var n=t.networkType?t.networkType:"";e.networkType=n,e.$apply()}})}},{key:"goIndexLogin",value:(p=l(regeneratorRuntime.mark((function e(){var n,r,a,o,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.default.$instance.globalData.regScene="2",n=getCurrentPages(),r=n[n.length-1],a=r.route,o=r.options,i=[],Object.keys(o).forEach((function(e){o[e]&&i.push(e+"="+o[e])})),i=""+i.join("&"),e.next=10,t.default.setStorageSync("logined_target_url","/"+a+"?"+i);case 10:wx.navigateTo({url:"/pages/customer/index"});case 11:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})}]),d}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(d,"pages/anticounterfeiting/validate"));