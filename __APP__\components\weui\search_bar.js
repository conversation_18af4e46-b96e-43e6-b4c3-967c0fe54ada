Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),n=require("./../../npm/wepy/lib/wepy.js"),i=(t=n)&&t.__esModule?t:{default:t};function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var u=function(t){function n(){var t,e,i;o(this,n);for(var u=arguments.length,a=Array(u),p=0;p<u;p++)a[p]=arguments[p];return e=i=r(this,(t=n.__proto__||Object.getPrototypeOf(n)).call.apply(t,[this].concat(a))),i.props={back:{},inputVal:{twoWay:!0},placeholder:{default:"搜索"}},i.data={isBack:!1,hideText:"取消",inputShowed:!1},i.computed={isBack:function(){return"true"==this.back}},i.methods={showInput:function(){this.inputShowed=!0},hideInput:function(){this.isBack?this.$emit("back"):(this.inputVal="",this.inputShowed=!1,this.$emit("input",null))},clearInput:function(){this.inputVal="",this.$emit("input",null)},inputTyping:function(t){var e=t.detail.value;this.inputVal=e,this.$emit("input",e)}},i.events={},r(i,e)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(n,i.default.component),e(n,[{key:"onLoad",value:function(){this.isBack&&(this.inputShowed=!0,this.hideText="返回")}}]),n}();exports.default=u;