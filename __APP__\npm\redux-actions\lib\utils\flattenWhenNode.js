exports.__esModule=!0,exports.default=void 0;var e=require("./../constants.js"),t=n(require("./ownKeys.js")),r=n(require("./get.js"));function n(e){return e&&e.__esModule?e:{default:e}}exports.default=function(n){return function u(i,o,a,s){var f=void 0===o?{}:o,c=f.namespace,p=void 0===c?e.DEFAULT_NAMESPACE:c,E=f.prefix;return void 0===a&&(a={}),void 0===s&&(s=""),(0,t.default)(i).forEach((function(t){var o=function(e){return s||!E||E&&new RegExp("^"+E+p).test(e)?e:""+E+p+e}(function(t){var r;if(!s)return t;var n=t.toString().split(e.ACTION_TYPE_DELIMITER),u=s.split(e.ACTION_TYPE_DELIMITER);return(r=[]).concat.apply(r,u.map((function(e){return n.map((function(t){return""+e+p+t}))}))).join(e.ACTION_TYPE_DELIMITER)}(t)),f=(0,r.default)(t,i);n(f)?u(f,{namespace:p,prefix:E},a,o):a[o]=f})),a}};