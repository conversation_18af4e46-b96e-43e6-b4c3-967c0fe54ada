exports.__esModule=!0,exports.default=function(n,d,o){void 0===o&&(o={});(0,r.default)((0,t.default)(n)||(0,u.default)(n),"Expected handlers to be a plain object.");var f=(0,a.default)(n,o),c=(0,i.default)(f).map((function(e){return(0,s.default)(e,(0,l.default)(e,f),d)})),j=e.default.apply(void 0,c.concat([d]));return function(e,r){return void 0===e&&(e=d),j(e,r)}};var e=n(require("./../../reduce-reducers/lib/index.js")),r=n(require("./../../invariant/browser.js")),t=n(require("./utils/isPlainObject.js")),u=n(require("./utils/isMap.js")),i=n(require("./utils/ownKeys.js")),a=n(require("./utils/flattenReducerMap.js")),s=n(require("./handleAction.js")),l=n(require("./utils/get.js"));function n(e){return e&&e.__esModule?e:{default:e}}