Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();var t=function(){function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t)}return e(t,null,[{key:"isEmpty",value:function(e){return""==e||null==e||"null"==e}},{key:"isNotEmpty",value:function(e){return!this.isEmpty(e)}},{key:"sum",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,r=0,n=!0,u=!1,a=void 0;try{for(var o,i=e[Symbol.iterator]();!(n=(o=i.next()).done);n=!0){var l=o.value;if(!this.isNumber(l))return NaN;var s=parseFloat(l);if(isNaN(s))return NaN;r+=s}}catch(e){u=!0,a=e}finally{try{!n&&i.return&&i.return()}finally{if(u)throw a}}return r.toFixed(t)}},{key:"isNumber",value:function(e){return/^[-+]?\d+(\.\d+)?$/.test(e)}},{key:"isPositiveNumber",value:function(e){return/^[1-9]\d*$|^\.\d*$|^0\.\d*$|^[1-9]\d*\.\d*$|^0$/.test(e)}},{key:"isArray",value:function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{key:"convertTimestapeToDay",value:function(e){return e.substring(0,e.indexOf(" ")).replace(/-/g,".")}},{key:"dateFormate",value:function(e,t){var r={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var n in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),r)new RegExp("("+n+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?r[n]:("00"+r[n]).substr((""+r[n]).length)));return t}}]),t}();exports.default=t;