<view class="_8762f94">
    <view class="column-center loading-wrap _8762f94" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter _8762f94" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <view class="content _8762f94" wx:if="{{init}}">
        <view class="myWine _8762f94">
            <view class="products _8762f94">
                <view class="pt _8762f94">
                    <view class="mine _8762f94">我的酿酒量</view>
                    <view class="mine2 _8762f94">我的积分</view>
                </view>
                <view class="how-many _8762f94">
                    <view class="amount _8762f94">{{userInfo.wine}}L</view>
                    <view class="amount2 _8762f94">{{userInfo.integration}}分</view>
                </view>
                <view class="inpOut _8762f94">
                    <input bindinput="bangding" class="inputer _8762f94" data-name="{{wine}}" placeholder="请输入兑换量(正整数)" type="number"></input>
                    <view bindtap="exchange" class="takeIt _8762f94">兑换</view>
                </view>
                <view class="rule _8762f94">兑换规则:1L兑换1积分~</view>
            </view>
        </view>
    </view>
</view>
