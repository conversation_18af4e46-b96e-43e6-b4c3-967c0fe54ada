Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e,n=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}(),o=a(require("./../../npm/wepy/lib/wepy.js")),i=(a(require("./../../utils/Tips.js")),a(require("./../../api/garden.js")));function a(t){return t&&t.__esModule?t:{default:t}}function r(t){return function(){var e=t.apply(this,arguments);return new Promise((function(t,n){return function o(i,a){try{var r=e[i](a),s=r.value}catch(t){return void n(t)}if(!r.done)return Promise.resolve(s).then((function(t){o("next",t)}),(function(t){o("throw",t)}));t(s)}("next")}))}}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var c=function(a){function c(){var n,a,l;s(this,c);for(var f=arguments.length,p=Array(f),h=0;h<f;h++)p[h]=arguments[h];return a=l=u(this,(n=c.__proto__||Object.getPrototypeOf(c)).call.apply(n,[this].concat(p))),l.data={status:-1,isMove:!1,width:0,position:{left:0,top:0},picture:"",hole:"",box:{top:0,left:0}},l.props={isShow:{type:Boolean,default:!1,twoWay:!0}},l.methods={close:function(){this.isShow=!1,this.$apply()},startSlide:function(t){this.isMove=!0,e=t.touches[0].pageX},toSlide:function(t){if(t.touches.length>0){var n=t.touches[0].pageX-e;n<=0?n=0:n>220&&(n=220),this.position.left=n,this.width=n}},endSlide:function(){this.methods.toValidate.call(this)},getValidateInfo:function(){var t=this;return r(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.default.getValidateInfo();case 2:1==(n=e.sent).status?(t.picture=n.datas[0],t.hole=n.datas[1],t.position.top=n.datas[2]):t.isShow=!1,t.$apply();case 5:case"end":return e.stop()}}),e,t)})))()},toValidate:function(){var e=this;return r(regeneratorRuntime.mark((function n(){var o;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i.default.toValidate({coordinate:Math.round(t.position.left)});case 2:if(2!=(o=n.sent).status){n.next=8;break}e.status=1,setTimeout((function(){t.isShow=!1,t.$apply()}),1e3),n.next=18;break;case 8:if(3!=o.status){n.next=16;break}return n.next=11,e.methods.getValidateInfo.call(e);case 11:e.position.left=0,e.isMove=!1,e.width=0,n.next=18;break;case 16:e.status=0,setTimeout((function(){t.methods.getValidateInfo.call(t),t.status=-1,t.width=0,t.position.left=0,t.isMove=!1,t.$apply()}),1e3);case 18:e.$apply();case 19:case"end":return n.stop()}}),n,e)})))()}},l.watch={isShow:function(t,e){t?(e||(this.picture="",this.hole=""),this.status=-1,this.position.left=0,this.width=0,this.isMove=!1,this.$apply(),this.methods.getValidateInfo.call(this)):o.default.$instance.globalData.showSlideValidate=!1}},u(l,a)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(c,o.default.component),n(c,[{key:"onLoad",value:function(){t=this,o.default.$instance.watch("showSlideValidate",(function(e){e&&(t.isShow=e,t.$apply())})),wx.getSystemInfo({success:function(e){var n=Math.max(0,(e.screenWidth-280)/2),o=Math.max(0,(e.screenHeight-250)/3);t.box.left=n,t.box.top=o,t.$apply(),t.methods.getValidateInfo.call(t)}})}}]),c}();exports.default=c;