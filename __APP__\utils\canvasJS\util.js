Object.defineProperty(exports,"__esModule",{value:!0}),exports.default={getTextLength:function(r,t){for(var e=0,n=0;n<r.length;n++){var o=r.charCodeAt(n);o>=1&&o<=126||65376<=o&&o<=65439?e++:e+=t?2:1}return e},transferBorder:function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=r.match(/(\w+)px\s(\w+)\s(.*)/),e={};return t&&(e={width:+t[1],style:t[2],color:t[3]}),t?e:null},transferColor:function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t="#";(r=(r=r.replace(/^rgba?\(/,"").replace(/\)$/,"")).split(", ")).length>3&&(r.length=3);var e=!0,n=!1,o=void 0;try{for(var a,l=r[Symbol.iterator]();!(e=(a=l.next()).done);e=!0){var f=a.value;t+=(f=parseInt(f||0))<10?"0"+f:f.toString(16)}}catch(r){n=!0,o=r}finally{try{!e&&l.return&&l.return()}finally{if(n)throw o}}return t},transferPadding:function(){for(var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"0 0 0 0",t=0,e=(r=r.split(" ")).length;t<e;t++)r[t]=+r[t].replace("px","");return r},transferBoxShadow:function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(r&&"none"!==r){var t=void 0,e=void 0;return(e=r.match(/(\w+)\s(\w+)\s(\w+)\s(rgb.*)/))?(e.shift(),r=e,t=e[3]||"#ffffff"):(t=(e=r.split(") "))[0]+")",r=e[1].split("px ")),{offsetX:+r[0]||0,offsetY:+r[1]||0,blur:+r[2]||0,color:t}}}};