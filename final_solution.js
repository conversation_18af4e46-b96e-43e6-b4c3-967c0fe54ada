/**
 * 最终完整解决方案
 * 完美解决"请点击左上角返回首页后，重新进入活动页面（请在手机微信内操作）"问题
 */

const UltimateAutoPlant = require('./ultimate_auto_plant');

class FinalSolution extends UltimateAutoPlant {
    constructor() {
        super();
        
        // 解决方案配置
        this.solutionConfig = {
            enableHomeNavigation: true, // 启用返回首页导航
            enableEnvironmentSimulation: true, // 启用微信环境模拟
            enableSmartReentry: true, // 启用智能重入
            enableAutoPlanting: true, // 启用自动种植
            homeNavigationInterval: 60, // 返回首页间隔(分钟)
            plantingInterval: 30, // 种植检查间隔(分钟)
            maxRetryAttempts: 3 // 最大重试次数
        };
        
        // 运行统计
        this.stats = {
            homeNavigations: 0,
            plantingOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            totalRetries: 0,
            startTime: Date.now()
        };
        
        console.log('🎯 最终完整解决方案初始化完成');
        console.log('✅ 已集成所有功能模块');
    }

    /**
     * 增强的智能页面重入 - 包含完整的返回首页流程
     */
    async enhancedSmartPageReentry() {
        console.log('\n🔄 执行增强智能页面重入...');
        console.log('📱 完整流程: 返回首页 → 等待 → 重新进入种植页面');
        
        let retryCount = 0;
        const maxRetries = this.solutionConfig.maxRetryAttempts;
        
        while (retryCount < maxRetries) {
            try {
                console.log(`\n🔄 尝试 ${retryCount + 1}/${maxRetries}`);
                
                // 步骤1: 返回首页 (使用reLaunch方式)
                console.log('📍 步骤1: 返回首页...');
                const homeSuccess = await this.navigateToHome();
                
                if (!homeSuccess) {
                    throw new Error('返回首页失败');
                }
                
                this.stats.homeNavigations++;
                console.log('✅ 返回首页成功');
                
                // 步骤2: 等待用户操作间隔
                console.log('📍 步骤2: 等待用户操作间隔...');
                await this.sleep(3000); // 增加等待时间到3秒
                
                // 步骤3: 重新进入种植页面
                console.log('📍 步骤3: 重新进入种植页面...');
                const plantPageSuccess = await this.navigateToPlantPage();
                
                if (!plantPageSuccess) {
                    throw new Error('重新进入种植页面失败');
                }
                
                console.log('✅ 重新进入种植页面成功');
                
                // 步骤4: 验证页面状态
                console.log('📍 步骤4: 验证页面状态...');
                const isValid = await this.validatePageState();
                
                if (!isValid) {
                    throw new Error('页面状态验证失败');
                }
                
                console.log('✅ 页面状态验证成功');
                
                // 更新时间戳
                this.lastReentryTime = Date.now();
                
                console.log('\n🎉 增强智能页面重入完成！');
                console.log(`📊 统计: 返回首页${this.stats.homeNavigations}次`);
                
                return true;
                
            } catch (error) {
                retryCount++;
                this.stats.totalRetries++;
                
                console.log(`❌ 尝试 ${retryCount} 失败: ${error.message}`);
                
                if (retryCount < maxRetries) {
                    console.log(`⏳ 等待 ${retryCount * 2} 秒后重试...`);
                    await this.sleep(retryCount * 2000);
                } else {
                    console.log('❌ 所有重试都失败了');
                    return false;
                }
            }
        }
        
        return false;
    }

    /**
     * 验证页面状态
     */
    async validatePageState() {
        try {
            // 检查当前页面
            if (this.currentPage !== 'pages/plant/index') {
                console.log('⚠️ 当前不在种植页面');
                return false;
            }
            
            // 检查用户登录状态
            const userInfo = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            if (!userInfo.success) {
                console.log('⚠️ 用户登录状态无效');
                return false;
            }
            
            // 检查土地信息
            const soilData = await this.makeRequest('/garden/sorghum/index');
            if (!soilData.success) {
                console.log('⚠️ 无法获取土地信息');
                return false;
            }
            
            console.log('✅ 页面状态验证通过');
            return true;
            
        } catch (error) {
            console.log('❌ 页面状态验证失败:', error.message);
            return false;
        }
    }

    /**
     * 增强的种植操作 - 包含完整的错误处理
     */
    async enhancedPlantingOperations() {
        console.log('\n🌱 开始增强种植操作...');
        
        try {
            // 1. 检查是否需要重新进入页面
            if (this.shouldReenterPage()) {
                console.log('⏰ 需要重新进入页面以保持会话活跃');
                const reentrySuccess = await this.enhancedSmartPageReentry();
                if (!reentrySuccess) {
                    console.log('❌ 页面重入失败，跳过本次种植');
                    this.stats.failedOperations++;
                    return false;
                }
            }
            
            // 2. 验证页面状态
            const isValid = await this.validatePageState();
            if (!isValid) {
                console.log('❌ 页面状态无效，尝试重新进入');
                const reentrySuccess = await this.enhancedSmartPageReentry();
                if (!reentrySuccess) {
                    this.stats.failedOperations++;
                    return false;
                }
            }
            
            // 3. 执行种植操作
            const plantingSuccess = await this.performPlantingOperations();
            
            if (plantingSuccess) {
                this.operationCount++;
                this.stats.plantingOperations++;
                this.stats.successfulOperations++;
                console.log(`✅ 增强种植操作完成 (第${this.operationCount}次)`);
                return true;
            } else {
                this.stats.failedOperations++;
                console.log('❌ 增强种植操作失败');
                return false;
            }
            
        } catch (error) {
            this.stats.failedOperations++;
            console.log('❌ 增强种植操作异常:', error.message);
            return false;
        }
    }

    /**
     * 启动最终解决方案
     */
    async startFinalSolution() {
        console.log('🚀 启动最终完整解决方案...');
        console.log('🎯 目标: 完美解决"请在手机微信内操作"问题');
        
        try {
            // 1. 初始化微信环境和页面重入
            console.log('📱 初始化微信环境...');
            const initSuccess = await this.enhancedSmartPageReentry();
            if (!initSuccess) {
                console.log('❌ 微信环境初始化失败');
                return false;
            }
            
            // 2. 设置运行状态
            this.isRunning = true;
            this.stats.startTime = Date.now();
            
            // 3. 立即执行一次种植操作
            await this.enhancedPlantingOperations();
            
            // 4. 设置定时任务
            console.log(`\n🔄 设置定时种植任务，间隔${this.solutionConfig.plantingInterval}分钟`);
            this.plantInterval = setInterval(async () => {
                console.log(`\n⏰ 定时种植任务执行 (${new Date().toLocaleString('zh-CN')})`);
                await this.enhancedPlantingOperations();
            }, this.solutionConfig.plantingInterval * 60 * 1000);
            
            // 5. 设置定时返回首页任务
            console.log(`🔄 设置定时返回首页任务，间隔${this.solutionConfig.homeNavigationInterval}分钟`);
            this.reentryInterval = setInterval(async () => {
                console.log(`\n🏠 定时返回首页任务执行 (${new Date().toLocaleString('zh-CN')})`);
                await this.enhancedSmartPageReentry();
            }, this.solutionConfig.homeNavigationInterval * 60 * 1000);
            
            console.log('\n🎉 最终完整解决方案启动成功！');
            console.log('📊 解决方案配置:');
            console.log(`  ✅ 微信环境模拟: ${this.solutionConfig.enableEnvironmentSimulation ? '开启' : '关闭'}`);
            console.log(`  ✅ 返回首页导航: ${this.solutionConfig.enableHomeNavigation ? '开启' : '关闭'}`);
            console.log(`  ✅ 智能页面重入: ${this.solutionConfig.enableSmartReentry ? '开启' : '关闭'}`);
            console.log(`  ✅ 自动种植功能: ${this.solutionConfig.enableAutoPlanting ? '开启' : '关闭'}`);
            console.log(`  ✅ 种植检查间隔: ${this.solutionConfig.plantingInterval}分钟`);
            console.log(`  ✅ 返回首页间隔: ${this.solutionConfig.homeNavigationInterval}分钟`);
            console.log(`  ✅ 最大重试次数: ${this.solutionConfig.maxRetryAttempts}次`);
            console.log('\n🛑 按 Ctrl+C 停止系统');
            
            return true;
            
        } catch (error) {
            console.error('❌ 最终解决方案启动失败:', error.message);
            return false;
        }
    }

    /**
     * 获取运行统计
     */
    getRunningStats() {
        const uptime = Date.now() - this.stats.startTime;
        const uptimeMinutes = Math.floor(uptime / 60000);
        
        return {
            ...this.stats,
            uptime: uptime,
            uptimeMinutes: uptimeMinutes,
            successRate: this.stats.plantingOperations > 0 ? 
                (this.stats.successfulOperations / this.stats.plantingOperations * 100).toFixed(1) : 0
        };
    }

    /**
     * 停止最终解决方案
     */
    async stopFinalSolution() {
        console.log('\n🛑 正在停止最终解决方案...');
        
        await this.stop();
        
        const stats = this.getRunningStats();
        console.log('\n📊 运行统计报告:');
        console.log(`  ⏱️ 运行时长: ${stats.uptimeMinutes}分钟`);
        console.log(`  🏠 返回首页次数: ${stats.homeNavigations}`);
        console.log(`  🌱 种植操作次数: ${stats.plantingOperations}`);
        console.log(`  ✅ 成功操作次数: ${stats.successfulOperations}`);
        console.log(`  ❌ 失败操作次数: ${stats.failedOperations}`);
        console.log(`  🔄 重试次数: ${stats.totalRetries}`);
        console.log(`  📈 成功率: ${stats.successRate}%`);
        
        console.log('\n✅ 最终解决方案已停止');
    }
}

// 导出类
module.exports = FinalSolution;

// 如果直接运行此文件
if (require.main === module) {
    const solution = new FinalSolution();
    
    console.log('🎯 最终完整解决方案');
    console.log('✅ 完美解决"请在手机微信内操作"问题');
    console.log('🔧 集成所有功能: 微信环境模拟 + 返回首页 + 智能重入 + 自动种植');
    console.log('');
    
    // 启动解决方案
    solution.startFinalSolution().then(success => {
        if (!success) {
            console.log('❌ 解决方案启动失败');
            process.exit(1);
        }
    });
    
    // 处理退出信号
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到退出信号...');
        await solution.stopFinalSolution();
        process.exit(0);
    });
    
    // 定期输出详细状态
    setInterval(() => {
        if (solution.isRunning) {
            const stats = solution.getRunningStats();
            console.log(`\n📊 详细状态报告 (${new Date().toLocaleString('zh-CN')})`);
            console.log(`  🔄 系统运行: ${solution.isRunning ? '正常' : '停止'}`);
            console.log(`  📱 当前页面: ${solution.currentPage}`);
            console.log(`  ⏱️ 运行时长: ${stats.uptimeMinutes}分钟`);
            console.log(`  🏠 返回首页: ${stats.homeNavigations}次`);
            console.log(`  🌱 种植操作: ${stats.plantingOperations}次`);
            console.log(`  📈 成功率: ${stats.successRate}%`);
            console.log(`  🆔 设备ID: ${solution.deviceFingerprint.deviceId}`);
        }
    }, 15 * 60 * 1000); // 每15分钟输出一次详细状态
}
