/**
 * 最终自动化种植脚本
 * 基于完整的算法分析和现有Authorization实现
 */

const https = require('https');
const fs = require('fs');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AutoPlantingFinal {
    constructor() {
        // 基于算法分析的完整认证信息
        this.auth = {
            loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY',
            authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI',
            memberID: 6865357,
            unionID: 'oA4oD1fddsj8tqw_UU2Z52eWTSpg',
            expireTime: 1749649802 // 2025年6月12日过期
        };
        
        // API配置
        this.apis = {
            baseUrl: 'https://wap.exijiu.com/index.php/API',
            memberInfo: '/garden/Gardenmemberinfo/getMemberInfo',
            plantingInfo: '/garden/Gardenmemberinfo/getPlantingInfo',
            planting: '/garden/Gardenmemberinfo/planting',
            harvest: '/garden/Gardenmemberinfo/harvest'
        };
        
        // 种植配置
        this.plantingConfig = {
            maxRetries: 3,
            retryDelay: 2000,
            checkInterval: 60000, // 1分钟检查一次
            autoHarvest: true,
            logFile: 'planting_log.txt'
        };
        
        console.log('🌱 最终自动化种植脚本初始化完成');
        console.log(`🔑 Authorization有效期至: ${new Date(this.auth.expireTime * 1000).toLocaleString('zh-CN')}`);
        console.log(`⏰ 剩余有效时间: ${Math.floor((this.auth.expireTime - Date.now() / 1000) / 86400)}天`);
    }

    /**
     * 获取标准请求头
     */
    getHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Authorization': this.auth.authorization,
            'login_code': this.auth.loginCode,
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(path, method = 'GET', data = null) {
        const url = this.apis.baseUrl + path;
        const headers = this.getHeaders();
        
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        console.log('原始响应:', responseData);
                        const jsonData = JSON.parse(responseData);
                        console.log('解析后数据:', JSON.stringify(jsonData, null, 2));

                        if (jsonData.err === 0) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(jsonData.msg || `请求失败，错误码: ${jsonData.err}`));
                        }
                    } catch (e) {
                        console.log('JSON解析失败，原始响应:', responseData);
                        reject(new Error(`响应解析失败: ${e.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 获取会员信息
     */
    async getMemberInfo() {
        try {
            const result = await this.makeRequest(this.apis.memberInfo);
            return result.data;
        } catch (error) {
            throw new Error(`获取会员信息失败: ${error.message}`);
        }
    }

    /**
     * 获取种植信息
     */
    async getPlantingInfo() {
        try {
            const result = await this.makeRequest(this.apis.plantingInfo);
            return result.data;
        } catch (error) {
            throw new Error(`获取种植信息失败: ${error.message}`);
        }
    }

    /**
     * 执行种植
     */
    async performPlanting() {
        try {
            const result = await this.makeRequest(this.apis.planting, 'POST');
            return result;
        } catch (error) {
            throw new Error(`种植失败: ${error.message}`);
        }
    }

    /**
     * 执行收获
     */
    async performHarvest() {
        try {
            const result = await this.makeRequest(this.apis.harvest, 'POST');
            return result;
        } catch (error) {
            throw new Error(`收获失败: ${error.message}`);
        }
    }

    /**
     * 检查是否可以种植
     */
    async checkCanPlant() {
        try {
            const plantingInfo = await this.getPlantingInfo();
            
            // 检查种植状态
            if (plantingInfo.planting_status === 0) {
                return { canPlant: true, reason: '可以种植' };
            } else if (plantingInfo.planting_status === 1) {
                return { canPlant: false, reason: '正在种植中' };
            } else if (plantingInfo.planting_status === 2) {
                return { canPlant: false, reason: '可以收获', canHarvest: true };
            } else {
                return { canPlant: false, reason: '未知状态' };
            }
        } catch (error) {
            return { canPlant: false, reason: error.message };
        }
    }

    /**
     * 检查是否可以收获
     */
    async checkCanHarvest() {
        try {
            const plantingInfo = await this.getPlantingInfo();
            return plantingInfo.planting_status === 2;
        } catch (error) {
            return false;
        }
    }

    /**
     * 记录日志
     */
    log(message) {
        const timestamp = new Date().toLocaleString('zh-CN');
        const logMessage = `[${timestamp}] ${message}`;
        
        console.log(logMessage);
        
        // 写入日志文件
        try {
            fs.appendFileSync(this.plantingConfig.logFile, logMessage + '\n');
        } catch (error) {
            console.log('写入日志文件失败:', error.message);
        }
    }

    /**
     * 执行一次完整的种植检查和操作
     */
    async performPlantingCycle() {
        this.log('🔍 开始种植周期检查...');
        
        try {
            // 1. 检查Authorization是否过期
            const currentTime = Math.floor(Date.now() / 1000);
            if (currentTime >= this.auth.expireTime) {
                this.log('❌ Authorization已过期，需要更新');
                return { success: false, reason: 'Authorization过期' };
            }
            
            // 2. 获取会员信息验证认证
            try {
                const memberInfo = await this.getMemberInfo();
                this.log(`✅ 会员认证成功: ${memberInfo.nickname || '未知用户'}`);
            } catch (error) {
                this.log(`❌ 会员认证失败: ${error.message}`);
                return { success: false, reason: '认证失败' };
            }
            
            // 3. 检查种植状态
            const plantStatus = await this.checkCanPlant();
            this.log(`📊 种植状态: ${plantStatus.reason}`);
            
            // 4. 如果可以收获，先收获
            if (plantStatus.canHarvest && this.plantingConfig.autoHarvest) {
                this.log('🌾 检测到可以收获，开始收获...');
                try {
                    const harvestResult = await this.performHarvest();
                    this.log(`🎉 收获成功: ${harvestResult.msg || '收获完成'}`);
                } catch (error) {
                    this.log(`❌ 收获失败: ${error.message}`);
                }
            }
            
            // 5. 如果可以种植，执行种植
            if (plantStatus.canPlant) {
                this.log('🌱 开始种植...');
                try {
                    const plantResult = await this.performPlanting();
                    this.log(`🎉 种植成功: ${plantResult.msg || '种植完成'}`);
                    return { success: true, action: 'planted' };
                } catch (error) {
                    this.log(`❌ 种植失败: ${error.message}`);
                    return { success: false, reason: error.message };
                }
            } else {
                this.log(`ℹ️ 当前无法种植: ${plantStatus.reason}`);
                return { success: true, action: 'no_action', reason: plantStatus.reason };
            }
            
        } catch (error) {
            this.log(`💥 种植周期异常: ${error.message}`);
            return { success: false, reason: error.message };
        }
    }

    /**
     * 启动自动化种植
     */
    async startAutoPlanting() {
        this.log('🚀 启动自动化种植系统');
        this.log(`⚙️ 检查间隔: ${this.plantingConfig.checkInterval / 1000}秒`);
        this.log(`🔄 最大重试次数: ${this.plantingConfig.maxRetries}`);
        this.log(`🌾 自动收获: ${this.plantingConfig.autoHarvest ? '开启' : '关闭'}`);
        
        let cycleCount = 0;
        
        const runCycle = async () => {
            cycleCount++;
            this.log(`\n🔄 第 ${cycleCount} 次检查`);
            
            let retries = 0;
            let success = false;
            
            while (retries < this.plantingConfig.maxRetries && !success) {
                if (retries > 0) {
                    this.log(`🔄 第 ${retries + 1} 次重试...`);
                    await this.sleep(this.plantingConfig.retryDelay);
                }
                
                const result = await this.performPlantingCycle();
                
                if (result.success) {
                    success = true;
                    if (result.action === 'planted') {
                        this.log('✅ 种植操作成功完成');
                    } else {
                        this.log(`ℹ️ 检查完成: ${result.reason}`);
                    }
                } else {
                    retries++;
                    this.log(`❌ 操作失败 (${retries}/${this.plantingConfig.maxRetries}): ${result.reason}`);
                    
                    if (result.reason === 'Authorization过期') {
                        this.log('🛑 Authorization过期，停止自动化');
                        return;
                    }
                }
            }
            
            if (!success) {
                this.log('❌ 达到最大重试次数，本次检查失败');
            }
            
            // 计划下次检查
            const nextCheck = new Date(Date.now() + this.plantingConfig.checkInterval);
            this.log(`⏰ 下次检查时间: ${nextCheck.toLocaleString('zh-CN')}`);
        };
        
        // 立即执行第一次检查
        await runCycle();
        
        // 设置定时器
        const timer = setInterval(runCycle, this.plantingConfig.checkInterval);
        
        // 优雅退出处理
        process.on('SIGINT', () => {
            this.log('\n🛑 收到退出信号，正在停止自动化种植...');
            clearInterval(timer);
            this.log('✅ 自动化种植已停止');
            process.exit(0);
        });
        
        this.log('✅ 自动化种植系统已启动，按 Ctrl+C 停止');
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 执行单次种植（用于测试）
     */
    async testPlanting() {
        this.log('🧪 执行单次种植测试...');
        
        const result = await this.performPlantingCycle();
        
        if (result.success) {
            this.log('✅ 测试成功完成');
        } else {
            this.log(`❌ 测试失败: ${result.reason}`);
        }
        
        return result;
    }
}

// 导出类
module.exports = AutoPlantingFinal;

// 如果直接运行此文件
if (require.main === module) {
    const planter = new AutoPlantingFinal();
    
    console.log('🌱 最终自动化种植脚本');
    console.log('🎯 基于完整算法分析的稳定版本');
    console.log('🔑 使用现有Authorization (有效期6天)');
    console.log('');
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    
    if (args.includes('--test')) {
        // 测试模式
        console.log('🧪 运行测试模式...');
        planter.testPlanting().then(result => {
            console.log('\n📊 测试结果:', result);
        }).catch(error => {
            console.error('💥 测试异常:', error);
        });
    } else {
        // 正常自动化模式
        console.log('🚀 启动自动化种植...');
        planter.startAutoPlanting().catch(error => {
            console.error('💥 启动异常:', error);
        });
    }
}
