Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=require("./../../npm/wepy/lib/wepy.js"),r=(e=n)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function n(){var e,t,r;o(this,n);for(var i=arguments.length,p=Array(i),u=0;u<i;u++)p[u]=arguments[u];return t=r=a(this,(e=n.__proto__||Object.getPrototypeOf(n)).call.apply(e,[this].concat(p))),r.data={init:!1,appId:"",path:""},r.methods={goto:function(){console.log(this.appId,this.path),wx.navigateToMiniProgram({appId:this.appId,path:this.path})}},r.config={navigationBarTitleText:"跳转中..."},a(r,t)}var i,p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,r.default.page),t(n,[{key:"onLoad",value:(i=regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.appId=t.appId,this.path=t.path?decodeURIComponent(t.path):"";case 2:case"end":return e.stop()}}),e,this)})),p=function(){var e=i.apply(this,arguments);return new Promise((function(t,n){return function r(o,a){try{var i=e[o](a),p=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(p).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(p)}("next")}))},function(e){return p.apply(this,arguments)})}]),n}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(i,"pages/web/navigateToMiniProgram"));