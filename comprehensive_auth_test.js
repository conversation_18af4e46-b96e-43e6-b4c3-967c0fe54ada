/**
 * 全面的Authorization测试
 * 测试所有可能的域名和API路径组合
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class ComprehensiveAuthTest {
    constructor() {
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码中发现的所有可能的域名
        this.domains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api',
            'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
            'https://wap.exijiu.com/index.php',
            'https://apimallwm.exijiu.com',
            'https://statistics.exijiu.com'
        ];
        
        // 从源码中发现的所有可能的JWT API路径
        this.jwtAPIs = [
            '/Member/getJwt',
            '/Member/getJifenShopJwt',
            '/api/v2/jifenCrm/createJwt',
            '/member/getJwt',
            '/member/jwt',
            '/auth/jwt',
            '/garden/jwt',
            '/jwt/create',
            '/token/jwt',
            '/Member/getJifenShopMemberInfo',
            '/garden/wechat/auth',
            '/garden/wechat/login'
        ];
        
        console.log('🔧 全面Authorization测试初始化完成');
        console.log(`🌐 将测试 ${this.domains.length} 个域名`);
        console.log(`🔑 将测试 ${this.jwtAPIs.length} 个API路径`);
    }

    /**
     * 构建请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'login_code': this.loginCode
        };
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method = 'GET', data = null) {
        const headers = this.buildHeaders();
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode,
                            rawData: responseData
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode,
                            rawData: responseData
                        });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({ 
                    success: false, 
                    data: null, 
                    status: 0,
                    error: error.message
                });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ 
                    success: false, 
                    data: null, 
                    status: 0,
                    error: '请求超时'
                });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 测试单个API组合
     */
    async testAPICombo(domain, apiPath) {
        try {
            const result = await this.makeRequest(domain, apiPath, 'GET');
            
            return {
                domain: domain,
                apiPath: apiPath,
                success: result.success,
                status: result.status,
                data: result.data,
                error: result.error,
                hasJWT: this.checkForJWT(result.data)
            };
            
        } catch (error) {
            return {
                domain: domain,
                apiPath: apiPath,
                success: false,
                status: 0,
                data: null,
                error: error.message,
                hasJWT: false
            };
        }
    }

    /**
     * 检查响应中是否包含JWT相关字段
     */
    checkForJWT(data) {
        if (!data || typeof data !== 'object') return false;
        
        const jwtFields = ['jwt', 'token', 'auth', 'authorized_token', 'access_token', 'authToken'];
        
        for (const field of jwtFields) {
            if (data[field]) return field;
        }
        
        // 递归检查嵌套对象
        for (const key in data) {
            if (typeof data[key] === 'object' && data[key] !== null) {
                const nested = this.checkForJWT(data[key]);
                if (nested) return nested;
            }
        }
        
        return false;
    }

    /**
     * 运行全面测试
     */
    async runComprehensiveTest() {
        console.log('🚀 开始全面Authorization测试...');
        console.log(`🎯 总共将测试 ${this.domains.length * this.jwtAPIs.length} 个组合`);
        
        const results = {
            successful: [],
            withJWT: [],
            failed: [],
            total: 0
        };
        
        let testCount = 0;
        
        for (const domain of this.domains) {
            console.log(`\n🌐 测试域名: ${domain}`);
            
            for (const apiPath of this.jwtAPIs) {
                testCount++;
                console.log(`\n🧪 [${testCount}/${this.domains.length * this.jwtAPIs.length}] 测试: ${apiPath}`);
                
                const result = await this.testAPICombo(domain, apiPath);
                results.total++;
                
                if (result.success) {
                    console.log(`✅ 成功: ${result.status}`);
                    results.successful.push(result);
                    
                    if (result.hasJWT) {
                        console.log(`🎉 发现JWT字段: ${result.hasJWT}`);
                        results.withJWT.push(result);
                    }
                    
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    
                } else {
                    console.log(`❌ 失败: ${result.status} - ${result.error || '未知错误'}`);
                    results.failed.push(result);
                }
                
                // 避免请求过快
                await this.sleep(500);
            }
        }
        
        // 输出测试结果总结
        console.log('\n' + '='.repeat(60));
        console.log('📊 全面测试结果总结');
        console.log('='.repeat(60));
        
        console.log(`\n📈 总体统计:`);
        console.log(`  🔢 总测试数: ${results.total}`);
        console.log(`  ✅ 成功数: ${results.successful.length}`);
        console.log(`  🎯 包含JWT数: ${results.withJWT.length}`);
        console.log(`  ❌ 失败数: ${results.failed.length}`);
        console.log(`  📊 成功率: ${((results.successful.length / results.total) * 100).toFixed(1)}%`);
        
        if (results.withJWT.length > 0) {
            console.log(`\n🎉 发现包含JWT的API:`);
            results.withJWT.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.domain}${result.apiPath}`);
                console.log(`     JWT字段: ${result.hasJWT}`);
                console.log(`     状态: ${result.status}`);
            });
        }
        
        if (results.successful.length > 0) {
            console.log(`\n✅ 所有成功的API:`);
            results.successful.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.domain}${result.apiPath} (${result.status})`);
            });
        }
        
        // 按状态码分组显示失败原因
        const failuresByStatus = {};
        results.failed.forEach(result => {
            const status = result.status || 'network_error';
            if (!failuresByStatus[status]) {
                failuresByStatus[status] = [];
            }
            failuresByStatus[status].push(result);
        });
        
        console.log(`\n❌ 失败原因分析:`);
        Object.keys(failuresByStatus).forEach(status => {
            console.log(`  ${status}: ${failuresByStatus[status].length} 个`);
        });
        
        return results;
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出类
module.exports = ComprehensiveAuthTest;

// 如果直接运行此文件
if (require.main === module) {
    const tester = new ComprehensiveAuthTest();
    
    console.log('🔍 全面Authorization测试');
    console.log('🎯 测试所有可能的域名和API路径组合');
    console.log('🔑 寻找能够生成Authorization的API');
    console.log('');
    
    // 运行全面测试
    tester.runComprehensiveTest().then(results => {
        if (results.withJWT.length > 0) {
            console.log('\n🎊 测试成功！找到了能够生成JWT的API！');
            console.log('🔑 现在你知道了Authorization的生成方法！');
        } else if (results.successful.length > 0) {
            console.log('\n🤔 找到了一些可用的API，但没有JWT字段');
            console.log('💡 可能需要进一步分析这些API的响应');
        } else {
            console.log('\n😔 没有找到可用的JWT API');
            console.log('💡 可能需要：');
            console.log('1. 检查login_code是否有效');
            console.log('2. 尝试其他认证方式');
            console.log('3. 分析更多的API路径');
        }
    }).catch(error => {
        console.error('💥 测试异常:', error);
    });
}
