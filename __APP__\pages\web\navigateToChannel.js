Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=r(require("./../../npm/wepy/lib/wepy.js")),n=r(require("./../../utils/Tips.js"));function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(r){function a(){var e,t,r;o(this,a);for(var p=arguments.length,u=Array(p),s=0;s<p;s++)u[s]=arguments[s];return t=r=i(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(u))),r.data={init:!1,appId:"",path:"",type:"profile"},r.methods={goto:function(){console.log(this.appId,this.path),"profile"==this.type?wx.openChannelsUserProfile({finderUserName:this.appId}):"video"==this.type?wx.openChannelsActivity({finderUserName:this.appId,feedId:this.path}):n.default.confirm("参数错误")}},r.config={navigationBarTitleText:"跳转中..."},i(r,t)}var p,u;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default.page),e(a,[{key:"onLoad",value:(p=regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.appId=t.appId,this.path=t.path?decodeURIComponent(t.path):"",this.type=t.type?t.type:"profile";case 3:case"end":return e.stop()}}),e,this)})),u=function(){var e=p.apply(this,arguments);return new Promise((function(t,n){return function r(o,i){try{var a=e[o](i),p=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(p).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(p)}("next")}))},function(e){return u.apply(this,arguments)})}]),a}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(a,"pages/web/navigateToChannel"));