Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),n=o(require("./../npm/wepy/lib/wepy.js")),u=(o(require("./../utils/ForumPage.js")),o(require("./../utils/ReplayPostPage.js")),o(require("./../utils/ForumHttp.js")));function o(e){return e&&e.__esModule?e:{default:e}}function a(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(u,o){try{var a=t[u](o),i=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var f=(t=e=function(e){function t(){return i(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}var o,f,l;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,u.default),r(t,null,[{key:"brandList",value:(l=a(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.forumBaseUrl+"/forum/brand/list",e.next=3,this.post(t,{});case 3:return r=e.sent,e.abrupt("return",r.data);case 5:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"uploadImg",value:(f=a(regeneratorRuntime.mark((function e(t){var r,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/evaluation/upload",e.next=3,n.default.uploadFile({url:r,filePath:t,name:"file"}).catch((function(e){console.log("uploadImgErr",e)}));case 3:return u=e.sent,e.abrupt("return",JSON.parse(u));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"uploadImgList",value:function(e){var t=this.forumBaseUrl+"/forum/imageType/evaluation/type/"+e;return this.get(t).then((function(e){return e.data}))}},{key:"evaluation",value:function(e){var t=this.forumBaseUrl+"/forum/evaluation";return this.post(t,e).then((function(e){return e}))}},{key:"identify",value:function(e){var t=this.forumBaseUrl+"/forum/evaluation/commit/result";return this.put(t,e).then((function(e){return e}))}},{key:"validateContent",value:(o=a(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/content/validate?content="+t,e.abrupt("return",this.post(r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})}]),t}(),e.forumBaseUrl=n.default.$instance.globalData.forumBaseUrl,t);exports.default=f;