Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=p(require("./../../npm/wepy/lib/wepy.js")),n=p(require("./../../mixins/base.js")),r=p(require("./../../components/common/loading.js")),a=(p(require("./../../utils/Tips.js")),p(require("./../../api/auth.js")),p(require("./../../utils/WxUtils.js"))),i=p(require("./../../api/memberActivity.js")),u=p(require("./../../api/member.js")),o=p(require("./../../api/garden.js")),c=p(require("./../../api/xijiuwx.js")),s=p(require("./../../mixins/pagination.js")),l=p(require("./../../components/common/placeholder.js")),f=p(require("./../../components/weui/loadmore.js"));function p(e){return e&&e.__esModule?e:{default:e}}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var y=function(p){function y(){var e,t,i,u;g(this,y);for(var o=arguments.length,c=Array(o),p=0;p<o;p++)c[p]=arguments[p];return i=u=h(this,(e=y.__proto__||Object.getPrototypeOf(y)).call.apply(e,[this].concat(c))),u.data=(d(t={userRanking:"",activityList:[]},"userRanking",[]),d(t,"hotActivity",""),d(t,"current",1),d(t,"init",!1),d(t,"latitude",""),d(t,"longitude",""),d(t,"userPianqu",""),d(t,"page",{list:[]}),d(t,"pianquActivityPage",{list:[]}),t),u.$repeat={},u.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},Loadmore:{"v-bind:page.sync":"page"}},u.$events={},u.components={Loading:r.default,Placeholder:l.default,Loadmore:f.default},u.mixins=[n.default,s.default],u.methods={current:function(e){if(this.current==e)return!1;this.current=e,this.$apply();var t="/pages/customer/activity_list?current="+e;a.default.backOrRedirect(t)},toDetail:function(e){var t=e.currentTarget.dataset.url;this.$navigate(t)}},u.config={navigationBarBackgroundColor:"#fff",navigationBarTextStyle:"black"},h(u,i)}var m,b;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(y,t.default.page),e(y,[{key:"onLoad",value:(m=regeneratorRuntime.mark((function e(n){var r,s,l,f;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.default.isJifenLogined();case 2:if(n.current&&(this.current=n.current),2!=this.current){e.next=26;break}if((r=t.default.getStorageSync("pianqu"))&&!((new Date-new Date(r.createTime))/1e3>3600)){e.next=23;break}if((s=t.default.getStorageSync("userLocation"))&&!((new Date-new Date(s.createTime))/1e3>3600)){e.next=13;break}return e.next=10,t.default.getSetting().then((function(e){if(!e.authSetting["scope.record"])return t.default.authorize({scope:"scope.userLocation"})})).then((function(e){return t.default.getLocation({type:"gcj02"})})).then((function(e){return s={latitude:e.latitude,longitude:e.longitude,speed:e.speed,accuracy:e.accuracy,createTime:new Date},t.default.setStorage({key:"userLocation",data:s}),e})).catch((function(e){return a.default.backOrRedirect("/pages/auth/setting"),!1}));case 10:if(e.sent){e.next=13;break}return e.abrupt("return");case 13:return this.latitude=s.latitude,this.longitude=s.longitude,e.next=17,c.default.getUerLocation(this.latitude,this.longitude);case 17:return l=e.sent,e.next=20,o.default.getUserPianqu(l);case 20:f=e.sent,r={pianqu:f,createTime:new Date},t.default.setStorage({key:"pianqu",data:r});case 23:this.params=function(){return{pianqu:r.pianqu}},e.next=27;break;case 26:this.params=function(){return{}};case 27:return e.next=29,i.default.list();case 29:return this.page=e.sent,e.next=32,this.next();case 32:this.loaded();case 33:case"end":return e.stop()}}),e,this)})),b=function(){var e=m.apply(this,arguments);return new Promise((function(t,n){return function r(a,i){try{var u=e[a](i),o=u.value}catch(e){return void n(e)}if(!u.done)return Promise.resolve(o).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(o)}("next")}))},function(e){return b.apply(this,arguments)})},{key:"params",value:function(){}}]),y}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(y,"pages/customer/activity_list"));