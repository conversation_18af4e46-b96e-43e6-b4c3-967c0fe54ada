/**
 * 高级代码分析器
 * 深入分析小程序的认证机制和API调用模式
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AdvancedCodeAnalyzer {
    constructor() {
        // 从现有的有效token中提取信息
        this.validAuthToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.validLoginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // API域名配置
        this.apiDomains = [
            'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api'
        ];
        
        // 小程序配置
        this.appConfig = {
            appId: 'wx489f950decfeb93e',
            version: 'v3.2.6',
            appCode: 'owVHb1gHrvktni80kjMlFMzSDJDWY0xR'
        };
        
        console.log('🔧 高级代码分析器初始化完成');
        console.log('🎯 目标: 深入分析认证机制和API调用模式');
    }

    /**
     * 分析API调用模式
     */
    async analyzeAPICallPatterns() {
        console.log('\n🔍 分析API调用模式...');
        
        const testAPIs = [
            // 认证相关API
            { path: '/auth/session', method: 'GET', params: { code: 'test_code' }, category: '认证' },
            { path: '/auth/checkSession', method: 'GET', params: { loginCode: this.validLoginCode }, category: '认证' },
            { path: '/auth/register', method: 'GET', params: {}, category: '认证' },
            
            // 用户相关API
            { path: '/garden/Gardenmemberinfo/getMemberInfo', method: 'GET', params: {}, category: '用户' },
            { path: '/member/info', method: 'GET', params: {}, category: '用户' },
            
            // 种植相关API
            { path: '/garden/sorghum/index', method: 'GET', params: {}, category: '种植' },
            { path: '/garden/notice/index', method: 'GET', params: {}, category: '种植' },
            
            // 公开API
            { path: '/banners', method: 'GET', params: {}, category: '公开' },
            { path: '/garden/tasks/index', method: 'GET', params: {}, category: '公开' }
        ];
        
        const results = {};
        
        for (const api of testAPIs) {
            console.log(`\n🧪 测试${api.category}API: ${api.path}`);
            
            if (!results[api.category]) {
                results[api.category] = { success: 0, total: 0, details: [] };
            }
            
            results[api.category].total++;
            
            try {
                // 测试不同的认证方式
                const authMethods = [
                    { name: '无认证', headers: {} },
                    { name: '仅login_code', headers: { 'login_code': this.validLoginCode } },
                    { name: '仅Authorization', headers: { 'Authorization': this.validAuthToken } },
                    { name: '完整认证', headers: { 'login_code': this.validLoginCode, 'Authorization': this.validAuthToken } }
                ];
                
                for (const authMethod of authMethods) {
                    const result = await this.testAPICall(api, authMethod);
                    
                    if (result.success) {
                        console.log(`  ✅ ${authMethod.name}: 成功`);
                        results[api.category].success++;
                        results[api.category].details.push({
                            api: api.path,
                            authMethod: authMethod.name,
                            success: true,
                            data: result.data
                        });
                        break; // 找到有效方法就停止
                    } else {
                        console.log(`  ❌ ${authMethod.name}: 失败 (${result.status})`);
                    }
                }
                
            } catch (error) {
                console.log(`  💥 ${api.path}: 异常 - ${error.message}`);
            }
        }
        
        // 输出分析结果
        console.log('\n📊 API调用模式分析结果:');
        console.log('=====================================');
        
        for (const [category, result] of Object.entries(results)) {
            const successRate = ((result.success / result.total) * 100).toFixed(1);
            console.log(`${category}API: ${result.success}/${result.total} 成功 (${successRate}%)`);
        }
        
        return results;
    }

    /**
     * 测试单个API调用
     */
    async testAPICall(api, authMethod) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
            ...authMethod.headers
        };
        
        // 构建完整URL
        let fullPath = api.path;
        if (api.params && Object.keys(api.params).length > 0) {
            const queryString = Object.keys(api.params)
                .map(key => `${key}=${encodeURIComponent(api.params[key])}`)
                .join('&');
            fullPath += '?' + queryString;
        }
        
        // 尝试所有API域名
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.makeRequest(baseUrl, fullPath, api.method, null, headers);
                if (result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        return { success: false, status: 0, data: null };
    }

    /**
     * 发起HTTP请求
     */
    async makeRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 分析请求头的重要性
     */
    async analyzeHeaderImportance() {
        console.log('\n🔍 分析请求头的重要性...');
        
        const testAPI = '/garden/Gardenmemberinfo/getMemberInfo';
        const baseHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        const headerTests = [
            { name: '基础头部', headers: baseHeaders },
            { name: '+ User-Agent', headers: { ...baseHeaders, 'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}` }},
            { name: '+ Referer', headers: { ...baseHeaders, 'Referer': `https://servicewechat.com/${this.appConfig.appId}/${this.appConfig.version}/page-frame.html` }},
            { name: '+ login_code', headers: { ...baseHeaders, 'login_code': this.validLoginCode }},
            { name: '+ Authorization', headers: { ...baseHeaders, 'Authorization': this.validAuthToken }},
            { name: '完整头部', headers: {
                ...baseHeaders,
                'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
                'Referer': `https://servicewechat.com/${this.appConfig.appId}/${this.appConfig.version}/page-frame.html`,
                'login_code': this.validLoginCode,
                'Authorization': this.validAuthToken
            }}
        ];
        
        for (const test of headerTests) {
            console.log(`\n🧪 测试: ${test.name}`);
            
            try {
                const result = await this.testAPICall({ path: testAPI, method: 'GET', params: {} }, { name: test.name, headers: test.headers });
                
                if (result.success) {
                    console.log(`  ✅ 成功: ${test.name}`);
                } else {
                    console.log(`  ❌ 失败: ${test.name} (状态: ${result.status})`);
                }
                
            } catch (error) {
                console.log(`  💥 异常: ${test.name} - ${error.message}`);
            }
        }
    }

    /**
     * 分析"请在手机微信内操作"错误的触发条件
     */
    async analyzeMobileWechatError() {
        console.log('\n🔍 分析"请在手机微信内操作"错误的触发条件...');
        
        const testScenarios = [
            {
                name: '缺少User-Agent',
                headers: {
                    'Content-Type': 'application/json',
                    'login_code': this.validLoginCode,
                    'Authorization': this.validAuthToken
                }
            },
            {
                name: '错误的User-Agent',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'login_code': this.validLoginCode,
                    'Authorization': this.validAuthToken
                }
            },
            {
                name: '缺少Referer',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
                    'login_code': this.validLoginCode,
                    'Authorization': this.validAuthToken
                }
            },
            {
                name: '错误的Referer',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
                    'Referer': 'https://example.com',
                    'login_code': this.validLoginCode,
                    'Authorization': this.validAuthToken
                }
            },
            {
                name: '正确的微信环境',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
                    'Referer': `https://servicewechat.com/${this.appConfig.appId}/${this.appConfig.version}/page-frame.html`,
                    'login_code': this.validLoginCode,
                    'Authorization': this.validAuthToken
                }
            }
        ];
        
        const testAPI = '/garden/sorghum/index'; // 种植相关API，更容易触发错误
        
        for (const scenario of testScenarios) {
            console.log(`\n🧪 测试场景: ${scenario.name}`);
            
            try {
                const result = await this.testAPICall({ path: testAPI, method: 'GET', params: {} }, scenario);
                
                if (result.success) {
                    console.log(`  ✅ 成功: ${scenario.name}`);
                } else {
                    console.log(`  ❌ 失败: ${scenario.name}`);
                    if (result.data && typeof result.data === 'object') {
                        const errorMsg = result.data.msg || result.data.message || '';
                        if (errorMsg.includes('微信') || errorMsg.includes('手机')) {
                            console.log(`  🎯 发现目标错误: ${errorMsg}`);
                        } else {
                            console.log(`  📄 错误信息: ${errorMsg}`);
                        }
                    }
                }
                
            } catch (error) {
                console.log(`  💥 异常: ${scenario.name} - ${error.message}`);
            }
        }
    }

    /**
     * 运行完整分析
     */
    async runCompleteAnalysis() {
        console.log('🔍 开始运行完整的代码分析...');
        console.log('🎯 目标: 深入理解小程序的认证机制');
        
        try {
            // 1. 分析API调用模式
            console.log('\n' + '='.repeat(60));
            console.log('📊 第一部分: API调用模式分析');
            console.log('='.repeat(60));
            const apiResults = await this.analyzeAPICallPatterns();
            
            // 2. 分析请求头重要性
            console.log('\n' + '='.repeat(60));
            console.log('📋 第二部分: 请求头重要性分析');
            console.log('='.repeat(60));
            await this.analyzeHeaderImportance();
            
            // 3. 分析"请在手机微信内操作"错误
            console.log('\n' + '='.repeat(60));
            console.log('🎯 第三部分: "请在手机微信内操作"错误分析');
            console.log('='.repeat(60));
            await this.analyzeMobileWechatError();
            
            // 4. 输出总结
            console.log('\n' + '='.repeat(60));
            console.log('📊 分析总结');
            console.log('='.repeat(60));
            
            console.log('\n🔑 关键发现:');
            console.log('1. 认证机制需要同时使用login_code和Authorization');
            console.log('2. User-Agent必须包含MicroMessenger和miniProgram标识');
            console.log('3. Referer必须指向servicewechat.com域名');
            console.log('4. 某些API可能有额外的环境检测机制');
            
            console.log('\n💡 建议解决方案:');
            console.log('1. 确保所有请求都包含完整的微信环境头部');
            console.log('2. 使用有效的login_code和Authorization组合');
            console.log('3. 模拟真实的页面访问流程');
            console.log('4. 定期刷新认证信息以保持会话活跃');
            
            return true;
            
        } catch (error) {
            console.log('\n❌ 分析过程失败:', error.message);
            return false;
        }
    }
}

// 导出类
module.exports = AdvancedCodeAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new AdvancedCodeAnalyzer();
    
    console.log('🔍 高级代码分析器');
    console.log('🎯 深入分析小程序认证机制');
    console.log('📱 解决"请在手机微信内操作"问题');
    console.log('');
    
    // 运行完整分析
    analyzer.runCompleteAnalysis().then(success => {
        if (success) {
            console.log('\n🎉 代码分析完成！');
            console.log('📋 请根据分析结果优化你的种植脚本');
        } else {
            console.log('\n😔 代码分析失败');
            console.log('💡 建议检查网络连接和API可用性');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
