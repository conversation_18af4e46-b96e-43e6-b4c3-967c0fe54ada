Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=o(require("./../../npm/wepy/lib/wepy.js")),t=o(require("./../common/placeholder.js"));function o(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var p=function(o){function p(){var e,o,s;r(this,p);for(var a=arguments.length,i=Array(a),c=0;c<a;c++)i[c]=arguments[c];return o=s=n(this,(e=p.__proto__||Object.getPrototypeOf(p)).call.apply(e,[this].concat(i))),s.props={list:[],is_empty:{default:!1}},s.$repeat={},s.$props={placeholder:{"xmlns:v-bind":"","v-bind:show.sync":"is_empty",message:"暂无积分数据"}},s.$events={},s.components={placeholder:t.default},n(s,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,e.default.component),p}();exports.default=p;