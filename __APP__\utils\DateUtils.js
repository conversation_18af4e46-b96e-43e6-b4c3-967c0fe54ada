Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();var t=function(){function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t)}return e(t,null,[{key:"formatDate",value:function(e){var t=new Date(e),n=864e5,r=(new Date).getTime()-t;if(!(r<0)){var a=r/2592e6,o=r/(7*n),u=r/n,i=r/1296e6,l=r/36e5,f=r/6e4;return a>=1?parseInt(a)+"月前":o>=1?parseInt(o)+"周前":u>=1?parseInt(u)+"天前":i>=1?parseInt(i)+"半月前":l>=1?parseInt(l)+"小时前":f>=1?parseInt(f)+"分钟前":"刚刚"}}},{key:"plantformatDate",value:function(e){if(e){var t=e.replace(/\-/g,"/"),n=new Date(t).getTime()-Date.now();(n=parseInt(n/1e3))<0&&(n=0);var r=n,a=parseInt(r/3600),o=parseInt(r%3600/60);return a<10&&(a=a),o<10&&(o="0"+o),a+":"+o}}},{key:"timeFromNow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Y-m-d H:i:s",t=new Date,n=new Date(e),r=(n-t)/1e3;return r}},{key:"formatSecondToHourMinute",value:function(e){var t=parseInt(e/3600),n=parseInt((e-3600*t)/60);return n<10&&(n="0"+n),t<10&&(t=""+t),t+":"+n}},{key:"countDown",value:function(e,t){var n=(t?t instanceof Date?t:new Date(t):new Date).getTime(),r=new Date(e).getTime();console.log("futureDateTime, currentDateTime",e,t),console.log("end   now",r,n);var a=r-n,o=void 0,u=void 0,i=void 0,l="00:00:00";return a>=0&&(l=(o=(o=Math.floor(a/1e3/60/60))<10?"0"+o:o)+":"+(u=(u=Math.floor(a/1e3/60%60))<10?"0"+u:u)+":"+(i=(i=Math.floor(a/1e3%60))<10?"0"+i:i)),l}},{key:"getYMD",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-",n=new Date(e),r=n.getFullYear(),a=n.getMonth()+1,o=n.getDate();return a>=1&&a<=9&&(a="0"+a),o>=0&&o<=9&&(o="0"+o),r+t+a+t+o}}]),t}();exports.default=t;