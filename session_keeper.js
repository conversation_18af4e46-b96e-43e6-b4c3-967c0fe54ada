/**
 * 微信小程序会话保活器
 * 模拟页面生命周期，无需手动进入种植页面
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class WxSessionKeeper {
    constructor() {
        // Token配置
        this.authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // API配置 - 尝试多个可能的域名
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api',
            'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2'
        ];
        
        this.appId = 'wx489f950decfeb93e';
        this.memberInfo = this.decodeJWT(this.authToken);
        this.memberId = this.memberInfo.memberInfo.id;
        
        // 会话状态
        this.isSessionActive = false;
        this.lastHeartbeat = null;
        this.heartbeatInterval = null;
        this.sessionData = {};
        
        console.log('🔧 会话保活器初始化完成');
        console.log('👤 会员ID:', this.memberId);
    }

    /**
     * 解析JWT Token
     */
    decodeJWT(token) {
        try {
            const parts = token.split('.');
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            return payload;
        } catch (e) {
            return null;
        }
    }

    /**
     * 构建请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'Referer': `https://servicewechat.com/${this.appId}/devtools/page-frame.html`,
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Authorization': this.authToken,
            'login_code': this.loginCode,
            'X-WX-AppId': this.appId,
            'X-WX-Version': 'v3.2.6',
            'X-WX-Platform': 'ios'
        };
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        const headers = this.buildHeaders();
        
        // 尝试所有可能的API域名
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.tryRequest(baseUrl, path, method, data, headers);
                if (result.success) {
                    return result.data;
                }
            } catch (error) {
                console.log(`❌ ${baseUrl} 请求失败:`, error.message);
                continue;
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        // 检查是否是成功的非JSON响应
                        if (res.statusCode === 200 && !responseData.includes('系统发生错误')) {
                            resolve({ success: true, data: responseData, status: res.statusCode });
                        } else {
                            resolve({ success: false, data: responseData, status: res.statusCode });
                        }
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 模拟页面onLoad生命周期
     */
    async simulatePageLoad() {
        console.log('\n🔄 模拟页面加载 (onLoad)...');
        
        try {
            // 1. 检查授权状态
            console.log('📋 检查授权状态...');
            
            // 2. 获取用户信息
            console.log('👤 获取用户信息...');
            await this.getUserInfo();
            
            // 3. 获取土地信息
            console.log('🌱 获取土地信息...');
            await this.getSoilList();
            
            // 4. 每日签到
            console.log('📅 执行每日签到...');
            await this.dailySign();
            
            console.log('✅ 页面加载模拟完成');
            return true;
            
        } catch (error) {
            console.log('❌ 页面加载模拟失败:', error.message);
            return false;
        }
    }

    /**
     * 模拟页面onShow生命周期
     */
    async simulatePageShow() {
        console.log('\n🔄 模拟页面显示 (onShow)...');
        
        try {
            // 1. 刷新用户信息
            await this.getUserInfo();
            
            // 2. 更新好友列表
            await this.getFriendList();
            
            // 3. 获取帮助记录
            await this.getHelpRecord();
            
            console.log('✅ 页面显示模拟完成');
            this.isSessionActive = true;
            this.lastHeartbeat = Date.now();
            
        } catch (error) {
            console.log('❌ 页面显示模拟失败:', error.message);
        }
    }

    /**
     * 获取用户信息
     */
    async getUserInfo() {
        try {
            const result = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            this.sessionData.userInfo = result;
            console.log('👤 用户信息更新成功');
            return result;
        } catch (error) {
            console.log('👤 获取用户信息失败:', error.message);
            return null;
        }
    }

    /**
     * 获取土地列表
     */
    async getSoilList() {
        try {
            const result = await this.makeRequest('/garden/sorghum/index');
            this.sessionData.soilList = result;
            console.log('🌱 土地信息更新成功');
            return result;
        } catch (error) {
            console.log('🌱 获取土地信息失败:', error.message);
            return [];
        }
    }

    /**
     * 每日签到
     */
    async dailySign() {
        try {
            const result = await this.makeRequest('/garden/sign/dailySign', 'POST');
            console.log('📅 签到结果:', result);
            return result;
        } catch (error) {
            console.log('📅 签到失败:', error.message);
            return null;
        }
    }

    /**
     * 获取好友列表
     */
    async getFriendList() {
        try {
            const result = await this.makeRequest('/garden/friend/list?offset=0&limit=10');
            this.sessionData.friendList = result;
            console.log('👥 好友列表更新成功');
            return result;
        } catch (error) {
            console.log('👥 获取好友列表失败:', error.message);
            return [];
        }
    }

    /**
     * 获取帮助记录
     */
    async getHelpRecord() {
        try {
            const result = await this.makeRequest('/garden/help/record');
            this.sessionData.helpRecord = result;
            console.log('🤝 帮助记录更新成功');
            return result;
        } catch (error) {
            console.log('🤝 获取帮助记录失败:', error.message);
            return [];
        }
    }

    /**
     * 启动会话保活
     */
    async startSessionKeeping(intervalMinutes = 15) {
        console.log(`\n🚀 启动会话保活，间隔${intervalMinutes}分钟`);
        
        // 首次模拟页面加载
        const loadSuccess = await this.simulatePageLoad();
        if (!loadSuccess) {
            console.log('❌ 初始页面加载失败，无法启动会话保活');
            return false;
        }
        
        // 模拟页面显示
        await this.simulatePageShow();
        
        // 设置定时心跳
        this.heartbeatInterval = setInterval(async () => {
            console.log('\n💓 执行会话心跳...');
            await this.simulatePageShow();
        }, intervalMinutes * 60 * 1000);
        
        console.log('✅ 会话保活已启动');
        return true;
    }

    /**
     * 停止会话保活
     */
    stopSessionKeeping() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        this.isSessionActive = false;
        console.log('🛑 会话保活已停止');
    }

    /**
     * 获取会话状态
     */
    getSessionStatus() {
        return {
            isActive: this.isSessionActive,
            lastHeartbeat: this.lastHeartbeat ? new Date(this.lastHeartbeat).toLocaleString('zh-CN') : null,
            memberInfo: this.memberInfo,
            sessionData: this.sessionData
        };
    }
}

// 导出类
module.exports = WxSessionKeeper;

// 如果直接运行此文件
if (require.main === module) {
    const keeper = new WxSessionKeeper();
    
    // 启动会话保活
    keeper.startSessionKeeping(15).then(success => {
        if (success) {
            console.log('\n🎉 会话保活启动成功！');
            console.log('💡 现在可以运行自动种植脚本而无需手动进入页面');
            
            // 保持进程运行
            process.on('SIGINT', () => {
                console.log('\n🛑 收到退出信号，正在停止会话保活...');
                keeper.stopSessionKeeping();
                process.exit(0);
            });
        } else {
            console.log('❌ 会话保活启动失败');
            process.exit(1);
        }
    });
}
