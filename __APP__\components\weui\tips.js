Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=require("./../../npm/wepy/lib/wepy.js"),o=(t=e)&&t.__esModule?t:{default:t};function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var i=function(t){function e(){var t,o,i;n(this,e);for(var s=arguments.length,u=Array(s),a=0;a<s;a++)u[a]=arguments[a];return o=i=r(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(u))),i.data={display:!1,message:"",timeoutId:null},i.methods={show:function(t){var e=this;this.display=!0,this.message=t,this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null),this.timeoutId=setTimeout((function(){e.display=!1,e.timeoutId=null,e.$apply()}),1e4)}},r(i,o)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,o.default.component),e}();exports.default=i;