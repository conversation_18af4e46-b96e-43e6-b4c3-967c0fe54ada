<view style="height:100%;background:#fff;">
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <scroll-view bindscrolltolower="bottomRefresh" scrollY="true" style="height:99%;background:#fff;" wx:if="{{init}}">
        <view class="reality">
            <view class="realVido" wx:for="{{vidoList}}" wx:key="index">
                <video controls binderror="videoErrorCallback" id="myVideo" pictureInPictureMode="{{['push','pop']}}" showCenterPlayBtn="{{true}}" src="{{item.url}}"></video>
            </view>
            <view class="realPic" wx:for="{{imgList}}" wx:key="index">
                <image bindtap="previewImage" data-list="{{showImg}}" data-src="{{item.url}}" lazyLoad="lazy-load" src="{{item.url}}"></image>
            </view>
        </view>
    </scroll-view>
</view>
