Object.defineProperty(exports,"__esModule",{value:!0});var _,E,T=function(){function _(_,E){for(var T=0;T<E.length;T++){var A=E[T];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(_,A.key,A)}}return function(E,T,A){return T&&_(E.prototype,T),A&&_(E,A),E}}();var A=require("./WxNotificationCenter.js"),D=(E=_=function(){function _(){!function(_,E){if(!(_ instanceof E))throw new TypeError("Cannot call a class as a function")}(this,_)}return T(_,null,[{key:"listen",value:function(_,E,T){this.remove(_,T),A.addNotification(_,E,T)}},{key:"emit",value:function(_,E){A.postNotificationName(_,E)}},{key:"remove",value:function(_,E){A.removeNotification(_,E)}}]),_}(),_.BARGAIN_DETAIL_UPDATE="BARGAIN_DETAIL_UPDATE",_.BARGAIN_LIST_UPDATE="BARGAIN_LIST_UPDATE",_.GROUP_LIST_UPDATE="GROUP_LIST_UPDATE",_.ORDER_LIST_UPDATE="ORDER_LIST_UPDATE",_.ORDER_TAB_UPDATE="ORDER_TAB_UPDATE",_.GOODS_DETAILS_UPDATE="GOODS_DETAILS_UPDATE",_.GOODS_LIST_UPDATE="GOODS_LIST_UPDATE",_.COUPON_LIST_UPDATE="COUPON_LIST_UPDATE",_.TRADE_COUPON_UPDATE="TRADE_COUPON_UPDATE",_.TRADE_ADDRESS_UPDATE="TRADE_ADDRESS_UPDATE",_.NOTICE_LIST_UPDATE="NOTICE_LIST_UPDATE",_.DELIVERY_LIST_UPDATE="DELIVERY_LIST_UPDATE",_.SHOP_INFO_UPDATE="SHOP_INFO_UPDATE",_.CART_LIST_CLEAR="CART_LIST_CLEAR",_.CART_LIST_RESET="CART_LIST_RESET",_.CART_LIST_ADD="CART_LIST_ADD",_.ADDRESS_DETAIL_UPDATE="ADDRESS_DETAIL_UPDATE",_.ADDRESS_LIST_UPDATE="ADDRESS_LIST_UPDATE",_.REGISTE_MEMBER_UPDATE="REGISTE_MEMBER_UPDATE",_.MEMBER_CARD_UPDATE="MEMBER_CARD_UPDATE",_.GOODS_PANEL_OPEN="GOOD_PANEL_OPEN",_.GOODS_PANEL_PLUS="GOODS_PANEL_PLUS",_.GOODS_PANEL_MINUS="GOODS_PANEL_MINUS",E);exports.default=D;