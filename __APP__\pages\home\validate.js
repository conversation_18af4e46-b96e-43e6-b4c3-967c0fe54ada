Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=c(require("./../../npm/wepy/lib/wepy.js")),r=(c(require("./../../api/auth.js")),c(require("./../../mixins/input.js"))),n=(c(require("./../../api/comment.js")),c(require("./../../mixins/base.js"))),a=c(require("./../../api/product.js")),i=c(require("./../../components/weui/tips.js")),o=c(require("./../../utils/Tips.js")),u=c(require("./../../components/common/loading.js"));function c(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(a,i){try{var o=t[a](i),u=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var p=function(c){function p(){var e,t,c;l(this,p);for(var d=arguments.length,m=Array(d),h=0;h<d;h++)m[h]=arguments[h];return t=c=f(this,(e=p.__proto__||Object.getPrototypeOf(p)).call.apply(e,[this].concat(m))),c.data={init:!1,imageUrl:["http://wap.exijiu.cn/Public/Qrcode/images/product-code-on-bottle-body.jpg","http://wap.exijiu.cn/Public/Qrcode/images/product-code-on-bottle-cap.jpg","http://wap.exijiu.cn/Public/Qrcode/default/liquor/images/egpm.png"],page:{list:[]},commentInit:!1,validatenum:{}},c.methods={confirm:function(e){var t=this,r=e.detail;return s(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object.assign(t.validatenum,r.value),0!=t.data.validatenum.theLast4Digits.length){e.next=5;break}o.default.modal("您好！防伪码不能为空。"),e.next=36;break;case 5:if(!(t.data.validatenum.theLast4Digits.length<4)){e.next=9;break}o.default.modal("您好！防伪码不能少于4位!"),e.next=36;break;case 9:if(!(t.data.validatenum.theLast4Digits.length>=5)){e.next=13;break}o.default.modal("您好！您输入的防伪码超过4位，请您核对!"),e.next=36;break;case 13:if(/^[0-9]*$/.test(t.data.validatenum.theLast4Digits)){e.next=17;break}o.default.modal("您好！您输入的防伪码格式不正确，请您核对!"),e.next=36;break;case 17:return e.prev=17,e.next=20,a.default.validateProductNums(t.validatenum);case 20:if(!e.sent.isMatch){e.next=26;break}return e.next=24,o.default.confirm("真品，请您放心饮用！");case 24:e.next=28;break;case 26:return e.next=28,o.default.modal("假品，点击“确定”进行举报").then((function(e){if(e.confirm){var n="/pages/home/<USER>"+r.value.qrCode;t.$navigate(n)}})).catch((function(e){console.log("取消")}));case 28:e.next=36;break;case 31:return e.prev=31,e.t0=e.catch(17),e.next=35,o.default.error("请重新操作");case 35:return e.abrupt("return");case 36:case"end":return e.stop()}}),e,t,[[17,31]])})))()},previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})}},c.computed={},c.components={Tips:i.default,Loading:u.default},c.mixins=[n.default,r.default],c.config={navigationBarTitleText:"进一步验证"},f(c,t)}var d;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,t.default.page),e(p,[{key:"onLoad",value:(d=s(regeneratorRuntime.mark((function e(t){var r=t.qrCode;return t.productNum,t.theLast4Digits,regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.validatenum.qrCode=r,this.loaded();case 2:case"end":return e.stop()}}),e,this)}))),function(e){return d.apply(this,arguments)})},{key:"onUnload",value:function(){}}]),p}();exports.default=p;