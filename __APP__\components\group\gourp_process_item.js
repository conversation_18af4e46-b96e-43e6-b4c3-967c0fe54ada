Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),r=o(require("./../../mixins/router.js"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(o){function u(){var e,t,o;n(this,u);for(var a=arguments.length,f=Array(a),c=0;c<a;c++)f[c]=arguments[c];return t=o=i(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(f))),o.props={item:{}},o.methods={joined:function(e){this.$root.$navigate("/pages/group/group_detail?groupId="+e.id)}},o.mixins=[r.default],i(o,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.component),e(u,[{key:"onLoad",value:function(){}}]),u}();exports.default=u;