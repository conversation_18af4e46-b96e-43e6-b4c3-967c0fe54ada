Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}(),o=require("./../../npm/wepy/lib/wepy.js"),n=(e=o)&&e.__esModule?e:{default:e};function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function o(){var e,t,n;r(this,o);for(var u=arguments.length,i=Array(u),l=0;l<u;l++)i[l]=arguments[l];return t=n=a(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(i))),n.data={version:null},n.methods={},n.props={buttom:{default:"false"}},a(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:function(){this.version=n.default.$instance.globalData.version}}]),o}();exports.default=u;