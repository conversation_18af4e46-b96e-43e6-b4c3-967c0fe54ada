Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var o=0;o<r.length;o++){var s=r[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(r,o,s){return o&&e(r.prototype,o),s&&e(r,s),r}}(),r=i(require("./base.js")),o=i(require("./../npm/wepy/lib/wepy.js")),s=i(require("./../utils/Page.js")),t=require("./order_const.js");function i(e){return e&&e.__esModule?e:{default:e}}function u(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function n(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var a=function(i){function a(){return u(this,a),n(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(a,r.default),e(a,null,[{key:"rules",value:function(e){var r=this,o=this.baseUrl+"/goods_group/rules/"+e;return this.get(o).then((function(e){return r._processGoodsDetail(e)}))}},{key:"processing",value:function(e){var r=this,o=this.baseUrl+"/goods_group/processing?rule_id="+e+"&sort=asc&by=group_time&limit=3";return this.get(o).then((function(e){return r._processGroupProcessingDetail(e)}))}},{key:"processingList",value:function(e){var r=this,o=this.baseUrl+"/goods_group/processing?rule_id="+e;return new s.default(o,(function(e){r._processGroupProcessingListDetail(e)}))}},{key:"goodsGroup",value:function(e,r){var o=this.baseUrl+"/goods_group";this._processOrderAddress(e,r);var s={ruleId:e.ruleId,order:e,id:e.groupId};return this.post(o,s)}},{key:"list",value:function(e){var r=this,o=this.baseUrl+"/goods_group/list?status="+e;return new s.default(o,(function(e){r._processGroupListItem(e)}))}},{key:"groupDetail",value:function(e){var r=this,o=this.baseUrl+"/goods_group/"+e;return this.get(o).then((function(e){return r._processGroupDetail(e)}))}},{key:"_processGoodsDetail",value:function(e){return this._processGoodsPreview(e),this._processSkuLable(e),this._processGoodsPriceRange(e),this._processGoodsPriceLabel(e),e}},{key:"_processGroupProcessingDetail",value:function(e){var r=this;return null===e?[]:(e.forEach((function(e){r._processGoodsPreview(e.rule),r._processSkuLable(e.rule),r._processGoodsPriceRange(e.rule),r._processGoodsPriceLabel(e.rule),r._processGroupTime(e),r._processGroupHeader(e),r._processGroupParticipated(e)})),e)}},{key:"_processGroupProcessingListDetail",value:function(e){return this._processGoodsPreview(e.rule),this._processSkuLable(e.rule),this._processGoodsPriceRange(e.rule),this._processGoodsPriceLabel(e.rule),this._processGroupParticipated(e),this._processGroupTime(e),this._processGroupHeader(e),e}},{key:"_processGroupDetail",value:function(e){var r=e.rule;return this._processGoodsPreview(r),this._processSkuLable(r),this._processGoodsPriceRange(r),this._processGoodsPriceLabel(r),this._processGroupListLength(e,r),e}},{key:"_processGoodsPreview",value:function(e){var r=e.goods.images;null==r||r.length<1||null==r[0].url?e.goods.imageUrl="/images/goods/broken.png":e.goods.imageUrl=r[0].url+"/medium"}},{key:"_processSkuLable",value:function(e){var r=e.goods.goodsSkuInfo;if(r){for(var o=[],s=1;s<=5;s++){var t=r["prop"+s],i=r["value"+s];if(!t||!i)break;var u={key:t,value:i.split(",")};o.push(u)}e.goods.labels=o}}},{key:"_processGoodsPriceRange",value:function(e){if(e.goods.goodsSkuInfo&&e.goods.goodsSkuInfo.goodsSkuDetails){var r=e.goods.goodsSkuInfo.goodsSkuDetails,o=0,s=Number.MAX_VALUE;for(var t in r){var i=r[t].goodsSkuDetailBase;o=Math.max(i.price,o),s=Math.min(i.price,s)}e.goods.maxPrice=o,e.goods.minPrice=s}}},{key:"_processGoodsPriceLabel",value:function(e){var r=e.goods.sellPrice;e.goods.maxPrice&&e.goods.minPrice&&(r=e.goods.minPrice),e.goods.priceLable=isNaN(e.goods.priceLable)?r:r.toFixed.toFixed(2)}},{key:"_processGroupHeader",value:function(e){e.list&&(e.header=e.list.find((function(e){return!0===e.head})))}},{key:"_processGroupTime",value:function(e){var r=new Date(e.groupTime.replace(/-/g,"/"))-new Date+864e5;if(r>0){var o=Math.floor(r/36e5),s=Math.floor(r/6e4%60),t=Math.floor(r/1e3%60);o=o<10?"0"+o:o,s=s<10?"0"+s:s,t=t<10?"0"+t:t,e.time="还剩"+o+":"+s+":"+t}else e.time="已结束"}},{key:"_processGroupParticipated",value:function(e){var r=o.default.getStorageSync("user");e.list.forEach((function(o){e.isPar=o.customerId===r.id}))}},{key:"_processOrderAddress",value:function(e,r){t.orderUtils.isDeliveryOrder(e.orderType)&&(e.receiveName=r.name+" "+r.sexText,e.receivePhone=r.phone,e.address=r.fullAddress)}},{key:"_processGroupListItem",value:function(e){var r=e.detail.order;r.shopName=this.shopName,this._processOrderStatusDesc(r),this._processOrderPrice(r),this._processOrderAction(r,!0);var o=r.orderGoodsInfos;return this._processOrderGoods(o),this._processOfflinePayment(r),e}},{key:"_processOrderStatusDesc",value:function(e){var r=e.status,o=e.orderType;if(e.statusText=t.orderUtils.statusName(o,r),e.statusDesc=t.orderUtils.statusDesc(e,r),7===e.status&&e.orderCloseNote){var s=e.orderCloseNote;e.statusDesc="订单已关闭，关闭原因："+s.note}}},{key:"_processOrderPrice",value:function(e){e.postFee=this._fixedPrice(e.postFee),e.dealPrice=this._fixedPrice(e.dealPrice),e.finalPrice=this._fixedPrice(e.finalPrice),e.couponPrice=this._fixedPrice(e.couponPrice),e.reduceFee=this._fixedPrice(e.reduceFee),e.bonusPrice=this._fixedPrice(e.bonusPrice)}},{key:"_fixedPrice",value:function(e){return null==e||isNaN(Number(e))?null:e.toFixed(2)}},{key:"_processOrderAction",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=[];e.curRefund&&o.push(t.ACTION.REFUND_DETAIL);var s=e.orderType,i=e.paymentType,u=e.status,n=t.orderUtils.statusActions(s,i,u);if(n){var a=r?n.filter((function(e){return 1!=e.inner})):n;e.actions=o.concat(a)}else e.actions=o}},{key:"_processOrderGoods",value:function(e){var r=this;null==e||e.length<1||(e.forEach((function(e){e.imageUrl+="/small"})),null==e||e.length<1||e.forEach((function(e){var o=e.goodsSku;e.skuText=r._processOrderSku(o)})))}},{key:"_processOrderSku",value:function(e){var r="";return e&&""!=e&&(r=e.replace(/:/g,",")),r}},{key:"_processOfflinePayment",value:function(e){if(e.orderType==t.TYPE.OFFLINE)return e.orderGoodsInfos=[{imageUrl:"http://img.leshare.shop/shop/other/wechat_pay.png",goodsName:"微信支付 "+e.finalPrice+"元",goodsPrice:e.finalPrice,count:1}],e}},{key:"_processGroupListLength",value:function(e,r){if(r.spareCustomer=r.limitCustomer-e.list.length,r.limitCustomer>e.list.length)for(var o=1;o<r.limitCustomer;o++)e.list.push({})}}]),a}();exports.default=a;