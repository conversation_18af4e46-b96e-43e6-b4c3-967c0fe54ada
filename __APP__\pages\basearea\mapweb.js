Object.defineProperty(exports,"__esModule",{value:!0});var e,n=function(){function e(e,n){for(var t=0;t<n.length;t++){var o=n[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(n,t,o){return t&&e(n.prototype,t),o&&e(n,o),n}}(),t=require("./../../npm/wepy/lib/wepy.js"),o=(e=t)&&e.__esModule?e:{default:e};function r(e){return function(){var n=e.apply(this,arguments);return new Promise((function(e,t){return function o(r,a){try{var c=n[r](a),i=c.value}catch(e){return void t(e)}if(!c.done)return Promise.resolve(i).then((function(e){o("next",e)}),(function(e){o("throw",e)}));e(i)}("next")}))}}function a(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function c(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}var i=function(e){function t(){var e,n,o;a(this,t);for(var r=arguments.length,i=Array(r),s=0;s<r;s++)i[s]=arguments[s];return n=o=c(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i))),o.data={url:"",locToken:"",beaconMap:"",showTimeInterval:null},o.methods={},o.config={},o.components={},c(o,n)}var i,s,u,l,p,f,h,m,w;return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}(t,o.default.page),n(t,[{key:"onReady",value:(w=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:wx.showToast({title:"点击自动按钮，自动语言导览",icon:"none",duration:2e3});case 1:case"end":return e.stop()}}),e,this)}))),function(){return w.apply(this,arguments)})},{key:"onLoad",value:(m=r(regeneratorRuntime.mark((function e(n){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.randomString();case 2:this.locToken=e.sent,this.url=decodeURIComponent(n.url)+"&locToken="+this.locToken,this.beaconMap=new Map;case 5:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"onShow",value:(h=r(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=this,setTimeout((function(){n.scanBluetooth()}),2e3),this.showTimeInterval=setInterval((function(){n.uploadBeacon()}),3e3);case 3:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"randomString",value:(f=r(regeneratorRuntime.mark((function e(){var n,t,o,r,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t=(n="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678").length,o="",r=0;r<a;r++)o+=n.charAt(Math.floor(Math.random()*t));return e.abrupt("return",o);case 5:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"scanBluetooth",value:(p=r(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=this,console.log("开始扫描蓝牙"),wx.startBeaconDiscovery({uuids:["FDA50693-A4E2-4FB1-AFCF-C6EB07647825"],success:function(){console.log("开始扫描设备"),wx.onBeaconUpdate((function(e){var t;console.log("onBeaconUpdate"),console.log(e),console.log(e.beacons.length),console.log("蓝牙设备列表"),e&&e.beacons&&e.beacons.length>0&&(n.beaconMap=null,n.beaconMap=new Map,e.beacons.forEach((function(e){t=e.uuid+e.major+e.minor,n.beaconMap[t]=e})),console.log("beaconMap:"),console.log(n.beaconMap))}))},fail:function(){wx.showToast({title:"打开蓝牙失败",icon:"error",duration:1500})}});case 3:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"uploadBeacon",value:(l=r(regeneratorRuntime.mark((function e(){var n,t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t in n=[],this.beaconMap)n.push(this.beaconMap[t]);console.log("beaconList-u",JSON.stringify(n)),this.beaconMap=null,this.beaconMap=new Map,wx.request({url:"https://zhdl-loc.gzxijiu.com/miniprogram/rest/uploadBeacon",data:{token:this.locToken,beaconInfoList:n},method:"POST",success:function(e){console.log("uploadBeacon res"),console.log(e)}}),console.log("beaconList:"),console.log(n);case 8:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"stopScanBluetooth",value:(u=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:wx.stopBeaconDiscovery({success:function(){console.log("停止扫描设备！")}}),wx.offBeaconUpdate((function(){console.log("取消监听 Beacon 设备更新事件")}));case 2:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})},{key:"onHide",value:(s=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.stopScanBluetooth(),clearInterval(this.showTimeInterval),this.showTimeInterval=null;case 3:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"onUnload",value:(i=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.stopScanBluetooth(),clearInterval(this.showTimeInterval),this.showTimeInterval=null;case 3:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})}]),t}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(i,"pages/basearea/mapweb"));