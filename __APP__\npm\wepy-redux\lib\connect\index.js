exports.__esModule=!0,exports.default=function(o,n){return o=(0,e.mapState)(o||{}),n=(0,e.mapActions)(n||{}),function(e){var r=null,i=e.prototype.onLoad,s=e.prototype.onUnload,a=function(){var e=this,n=((0,t.getStore)(),!1);Object.keys(o).forEach((function(t){var r=o[t].call(e);e[t]!==r&&(e[t]=r,n=!0)})),n&&this.$apply()};return function(e){function c(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c);var t=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,e.call(this));return t.computed=Object.assign(t.computed||{},o),t.methods=Object.assign(t.methods||{},n),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(c,e),c.prototype.onLoad=function(){var e=(0,t.getStore)();r=e.subscribe(a.bind(this)),a.call(this),i&&i.apply(this,arguments)},c.prototype.onUnload=function(){r&&r(),r=null,s&&s.apply(this,arguments)},c}(e)}};var t=require("./../store.js"),e=require("./../helpers/index.js");