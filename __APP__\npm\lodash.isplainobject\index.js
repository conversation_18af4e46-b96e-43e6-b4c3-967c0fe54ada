var e=require("./../lodash._basefor/index.js"),o=require("./../lodash.isarguments/index.js"),r=require("./../lodash.keysin/index.js");var t=Object.prototype,n=t.hasOwnProperty,c=t.toString;module.exports=function(t){var i,s;return!(!function(e){return!!e&&"object"==typeof e}(t)||"[object Object]"!=c.call(t)||o(t)||!(n.call(t,"constructor")||"function"!=typeof(i=t.constructor)||i instanceof i))&&(e(t,(function(e,o){s=o}),r),void 0===s||n.call(t,s))};