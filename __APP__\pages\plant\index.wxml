<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outer" wx:if="{{init}}">
        <view class="outter" wx:if="{{$TipsModal$showMsg}}">
            <view class="your_gift">
                <view class="hide_title">提示</view>
                <view class="hide_content">
                    <view bindtap="$TipsModal$closeIt" class="close">X</view>
                    <view class="msg">{{$TipsModal$tipMsg}}</view>
                    <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
                </view>
            </view>
        </view>
        <view class="firstLogin" wx:if="{{isTodayFirstSign}}">
            <view class="your_gifts">
                <view class="hide_title">每日登录奖励</view>
                <view class="hide_content">
                    <view bindtap="gotIt" class="close">X</view>
                    <view class="hide_img"></view>
                    <view class="hide_send">
                        <text class="lf">{{dailySign.tips}}</text>
                    </view>
                    <view bindtap="gotIt" class="gotIt">我收下了</view>
                </view>
            </view>
        </view>
        <view class="cover" wx:if="{{showLoginCover}}"></view>
        <view class="cover" wx:if="{{showRegisterCover}}"></view>
        <view class="cover" wx:if="{{showIproveCover}}"></view>
        <view class="doommview">
            <view class="help" style="animation:first 10s {{ind*3}}s linear forwards;top:100rpx;" wx:for="{{helpRecord}}" wx:for-index="ind" wx:key="id">
                <image class="head_img" src="{{item.helper_head_img}}"></image>
                <view class="names">
                    <view class="name">{{item.helper_nick_name}}</view>
                    <view style="font-size:24rpx" wx:if="{{item.type===0}}">{{item.remark}}</view>
                    <view style="font-size:24rpx" wx:if="{{item.type===1}}">{{item.remark}}</view>
                    <view style="font-size:24rpx" wx:if="{{item.type===2}}">{{item.remark}}</view>
                </view>
            </view>
        </view>
        <view bindtap="clickContent" class="content" style="background-image:{{contentBg}}">
            <view class="cloud1"></view>
            <view class="cloud2"></view>
            <view class="swtch">
                <view catchtap="switch1Change" class="switch-content" data-wpyswitch1change-a="{{false}}" wx:if="{{switch1Checked}}">
                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-music-on.png"></image>
                </view>
                <view catchtap="switch1Change" class="switch-content" data-wpyswitch1change-a="{{true}}" wx:else>
                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-music-off.png"></image>
                </view>
            </view>
            <view bindtap="clickhead">
                <view class="head">
                    <view class="header-bar" style="position:{{$plantHeadComponent$showRegisterCover||$plantHeadComponent$showLoginCover?'':'relative'}}">
                        <view class="avatar">
                            <view class="img-content">
                                <button bindtap="$plantHeadComponent$getPhoneNumber" class="btn" plain="true" wx:if="{{$plantHeadComponent$showRegisterCover}}">
                                    <image class="avatar-image" src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-register.png"></image>
                                </button>
                                <button bindgetuserinfo="$plantHeadComponent$login" class="btn" openType="getUserInfo" plain="true" wx:if="{{$plantHeadComponent$showLoginCover}}">
                                    <image class="avatar-image" src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-login.png"></image>
                                </button>
                                <button bindtap="$plantHeadComponent$gotoWithLogined" class="btn" data-url="/pages/plant/ranking" plain="true" wx:else>
                                    <image class="avatar-image" src="{{$plantHeadComponent$userInfo.head_imgurl}}"></image>
                                </button>
                            </view>
                            <view class="name" wx:if="{{!$plantHeadComponent$showLoginCover&&!$plantHeadComponent$showRegisterCover}}">{{$plantHeadComponent$userInfo.nick_name}}的农场</view>
                            <view class="noCity" wx:if="{{$plantHeadComponent$showIproveCover}}">
                                <view class="cLeft">未获取到城市排名，先去完善信息吧，有奖励喔~</view>
                                <view bindtap="$plantHeadComponent$gotoImprove" class="cRight" data-url="/pages/plant/Improve">去完善</view>
                            </view>
                        </view>
                        <view class="depot-info" wx:if="{{!$plantHeadComponent$showLoginCover&&!$plantHeadComponent$showRegisterCover}}">
                            <view class="depot-info-item">
                                <view class="icon">
                                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-sorghum-package.png"></image>
                                </view>
                                <view class="info-main">高粱{{$plantHeadComponent$userInfo.sorghum==$plantHeadComponent$null?0:$plantHeadComponent$userInfo.sorghum}}斤</view>
                            </view>
                            <view class="depot-info-item">
                                <view class="icon">
                                    <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-wheat-package.png"></image>
                                </view>
                                <view class="info-main">小麦{{$plantHeadComponent$userInfo.wheat==$plantHeadComponent$null?0:$plantHeadComponent$userInfo.wheat}}斤</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="introduction">
                <view bindtap="redirecttoWithLogined" class="item" data-url="/pages/plant/map" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/Introduction" hoverClass="hover1"></view>
            </view>
            <view class="one_click_collection" wx:if="{{showHarvestAll}}">
                <view catchtap="onclickcollection" class="click_collection_view"></view>
            </view>
            <view class="plants">
                <view class="soil-block" wx:for="{{sortSoilList}}" wx:key="id">
                    <view catchtap="seed" class="click-block" data-wpyseed-a="{{item}}" wx:if="{{!item.deving}}"></view>
                    <view catchtap="seed" class="crop-time-popple" data-wpyseed-a="{{item}}" wx:if="{{item.status==10||item.status==11}}">
                        <text>{{item.formatPlantDate}}</text>
                    </view>
                    <view class="bg green" wx:if="{{item.status===-1}}"></view>
                    <view class="bg brwon" wx:else></view>
                    <view catchtap="touchDev" class="dev" data-wpytouchdev-a="{{item}}" wx:if="{{item.deving}}"></view>
                    <view class="dev-tip" wx:if="{{item.deving&&canIextend}}">
                        <view class="news">
                            <view class="cont">您有土地待开垦！</view>
                        </view>
                    </view>
                    <view catchtap="watering" class="operate-watering" data-wpywatering-a="{{item}}" wx:if="{{(item.status===10||item.status===11)&&selectSoil.id===item.id&&!showWateringGif&&!showFertilizeGif}}"></view>
                    <view catchtap="fertilize" class="operate-fertilize" data-wpyfertilize-a="{{item}}" wx:if="{{(item.status===10||item.status===11)&&selectSoil.id===item.id&&!showWateringGif&&!showFertilizeGif}}"></view>
                    <view catchtap="harvest" class="operate-gain" data-wpyharvest-a="{{item}}" wx:if="{{item.status===2&&selectHarvest.id!==item.id}}"></view>
                    <view class="animation-watering" wx:if="{{showWateringGif&&selectSoil.id&&selectSoil.id===item.id}}"></view>
                    <view class="animation-fertilize" wx:if="{{showFertilizeGif&&selectSoil.id&&selectSoil.id===item.id}}"></view>
                    <view class="animation-harvest-sorghum" wx:if="{{selectHarvest.id===item.id&&selectHarvest.type===1}}">
                        <view class="harvest-num">x{{selectHarvest.sorghum}}</view>
                    </view>
                    <view class="animation-harvest-wheat" wx:if="{{selectHarvest.id===item.id&&selectHarvest.type===2}}">
                        <view class="harvest-num">x{{selectHarvest.wheat}}</view>
                    </view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-sorghum-1.png)" wx:if="{{item.status===10&&item.type===1}}"></view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-sorghum-2.png)" wx:if="{{item.status===11&&item.type===1}}"></view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-sorghum-3.png)" wx:if="{{item.status===2&&item.type===1}}"></view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-wheat-1.png)" wx:if="{{item.status===10&&item.type===2}}"></view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-wheat-2.png)" wx:if="{{item.status===11&&item.type===2}}"></view>
                    <view class="crop" style="background-image:url(http://wap.exijiu.cn/Public/MemberClub/images/v2-wheat-3.png)" wx:if="{{item.status===2&&item.type===2}}"></view>
                </view>
            </view>
            <view class="bottom-bar">
                <view bindtap="showModal_firends" class="item" data-wpyshowmodal_firends-a="" hoverClass="hover1"></view>
                <view bindtap="showModal_task" class="item" data-wpyshowmodal_task-a="" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/luck_draw" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/exchangeStore" hoverClass="hover1"></view>
                <view catchtap="showDepotModal" class="item" data-wpyshowdepotmodal-a="{{true}}" hoverClass="hover1"></view>
            </view>
            <view bindtap="hideModal" catchtouchmove="true" class="commodity_screen" wx:if="{{showModalStatus}}"></view>
            <view bindtap="hideModal_harvest" catchtouchmove="true" class="commodity_screen" wx:if="{{showModalStatus_harvest}}"></view>
            <view class="harvestTips" wx:if="{{showModalStatus_harvest}}">
                <view class="contHText">恭喜您获得100斤高粱、100斤小麦</view>
                <view bindtap="gotoWithWine" class="contH" data-url="/pages/plant/goodwine?from=plant">去酿酒</view>
                <view bindtap="hideModal_harvest" class="know"></view>
            </view>
            <view wx:if="{{showModalStatus_mid_autumn}}">
                <view class="pop-alert-box dialog">
                    <view class="alert-content-box">
                        <view class="alert-content">
                            <image class="icon_alert_dialog" mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClub/images/mid-autumn-lottery.jpg"></image>
                            <view class="title">贵州习酒 中秋邀您免费抽奖</view>
                            <view class="title">活动期间（10月1日-10月7日）</view>
                            <view class="title">会员每天都有5次免费抽奖机会</view>
                            <view class="title">之后如需再抽奖，每次抽奖需消耗100积分</view>
                        </view>
                        <view class="btn_box">
                            <view bindtap="gotoWithLogined" class="button type_red" data-url="/pages/plant/luck_draw">参与活动</view>
                        </view>
                    </view>
                    <image catchtap="hideModal_mid_autumn" class="iconfont icon-close" src="https://wap.exijiu.cn/Public/MemberClub/images/closed.png"></image>
                </view>
                <view class="alert_mask"></view>
            </view>
            <view animation="{{animationData}}" class="commodity_attr_box_task" wx:if="{{showModalStatus_task1}}">
                <view class="title">做任务获得浇水施肥次数</view>
                <view class="task_cont">
                    <view class="tasks  type{{item.type}}" wx:for="{{tasks}}" wx:for-index="tindex" wx:key="tindex">
                        <view class="task_name">
                            <view class="tLeft">
                                <view class="img"></view>
                                <view class="abc">
                                    <view class="font1">{{item.name}}</view>
                                    <view class="font2">{{item.reward}}</view>
                                </view>
                            </view>
                            <button bindtap="gotoWithTasks" class="task_btn" data-code="{{item.code}}" data-is_complete="{{item.is_complete}}" data-target="{{item.target}}" data-url="{{item.target}}?is_complete={{item.is_complete}}&id={{userInfo.member_id}}" id="{{tindex}}" openType="{{item.type==3?'share':''}}">
                                <block wx:if="{{item.code=='subscribe'}}">{{item.is_complete==2?'领取':item.is_complete==1?'已领取':'去关注'}}</block>
                                <block wx:else>{{item.is_complete?'已完成':'去完成'}}</block>
                            </button>
                        </view>
                    </view>
                </view>
            </view>
            <view animation="{{animationData}}" class="commodity_attr_box_friends" wx:if="{{showModalStatus_firends1}}">
                <view class="title">我的微信好友</view>
                <scroll-view bindscrolltolower="friend_listShow" class="friends_cont" scrollY="true" style="height:{{friend_notlogin.length?'60%':'80%'}}">
                    <view bindtap="redirecttoWithLogined" class="friends" data-url="/pages/plant/friends?friendId={{item.member_id}}" wx:for="{{friend_list}}" wx:for-index="tindex" wx:key="tindex">
                        <view class="task_name">
                            <view class="tLeft">
                                <text class="ranks">{{tindex+1}}</text>
                                <image class="img" src="{{item.head_imgurl}}"></image>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name||item.real_name}}</view>
                                </view>
                            </view>
                            <view class="task_btn">{{item.wine}}L</view>
                        </view>
                        <view class="handbg" wx:if="{{item.is_can_steal}}"></view>
                    </view>
                    <view class="nomore" wx:if="{{more_friend}}">您没有更多好友啦，到每日任务里通过分享即可邀请并和TA成为游戏好友！</view>
                </scroll-view>
                <view wx:if="{{friend_notlogin.length}}">
                    <view class="title2">您的好友已经迷路了，快去找他回来</view>
                    <view class="friends notlogin" wx:for="{{friend_notlogin}}" wx:key="index">
                        <view class="task_name">
                            <view class="tLeft">
                                <image class="img" src="{{item.head_imgurl}}"></image>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name}}</view>
                                </view>
                            </view>
                            <button class="invite_btn" openType="share">邀请TA</button>
                        </view>
                    </view>
                </view>
            </view>
            <view class="choose-seed-modal" wx:if="{{showChooseSeed}}">
                <view class="modal-select-box">
                    <view catchtap="toSeed" class="item" data-wpytoseed-a="{{1}}" hoverClass="hover1">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-sorghum.png"></image>
                    </view>
                    <view catchtap="toSeed" class="item" data-wpytoseed-a="{{2}}" hoverClass="hover1">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-wheat.png"></image>
                    </view>
                </view>
                <view catchtap="showSeedModal" class="modal-button-enter" data-wpyshowseedmodal-a="{{false}}" hoverClass="hover1"></view>
                <view class="modal-bg"></view>
                <view class="mask"></view>
            </view>
            <view class="depot-modal" wx:if="{{showDepot}}">
                <view class="modal-select-box">
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-sorghum.png"></image>
                        <view class="item-text">高粱:{{userInfo.sorghum}}斤</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-wheat.png"></image>
                        <view class="item-text">小麦:{{userInfo.wheat}}斤</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-yeast.png"></image>
                        <view class="item-text">酒曲:{{userInfo.wine_yeast}}块</view>
                    </view>
                </view>
                <view class="modal-select-box">
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-modal-jar.png"></image>
                        <view class="item-text">酒:{{userInfo.wine}}L</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-kettle.png"></image>
                        <view class="item-text">水:{{userInfo.water}}桶</view>
                    </view>
                    <view class="item">
                        <image src="http://wap.exijiu.cn/Public/MemberClub/images/v2-logo-muck.png"></image>
                        <view class="item-text">肥料:{{userInfo.manure}}袋</view>
                    </view>
                </view>
                <view catchtap="showDepotModal" class="modal-button-enter" data-wpyshowdepotmodal-a="{{false}}" hoverClass="hover1"></view>
                <view class="modal-bg"></view>
                <view class="mask"></view>
            </view>
        </view>
    </view>
    <view class="wx-container">
        <view class="wx-overlay" wx:if="{{$Validate$isShow}}"></view>
        <view class="wx-main" style="top:{{$Validate$box.top}}px;left:{{$Validate$box.left}}px;" wx:if="{{$Validate$isShow}}">
            <view class="wx-head">请滑动滑块进行验证<view bindtap="$Validate$close" class="wx-butn">
                    <view class="wx-close"></view>
                </view>
                <view bindtap="$Validate$getValidateInfo" class="wx-butn">
                    <view class="wx-refresh"></view>
                </view>
            </view>
            <view style="padding:5px;">
                <view class="wx-body" style="background-image:url({{$Validate$picture}});background-size:contain;">
                    <view class="wx-slideback" style="left:{{$Validate$position.left}}px;top:{{$Validate$position.top}}px;background-image:url({{$Validate$hole}});"></view>
                </view>
                <view class="wx-bottom">
                    <view class="wx-result" wx:if="{{$Validate$status>=0}}">
                        <view class="wx-error" wx:if="{{$Validate$status==0}}">
                            <icon type="warn"></icon>验证失败</view>
                        <view class="wx-success" wx:if="{{$Validate$status==1}}">
                            <icon type="success"></icon>验证成功</view>
                    </view>
                    <view class="wx-slide" wx:else>
                        <view class="wx-padding" style="width:{{$Validate$width}}px;"></view>
                        <view bindtouchend="$Validate$endSlide" bindtouchmove="$Validate$toSlide" bindtouchstart="$Validate$startSlide" class="wx-slidebutn" style="left:{{$Validate$position.left}}px;">
                            <view class="wx-icon"></view>
                        </view>向右滑动填充图片</view>
                </view>
            </view>
        </view>
    </view>
</view>
