var t=/^\[object .+?Constructor\]$/;function r(t){return!!t&&"object"==typeof t}var n,o,e,c=Object.prototype,u=Function.prototype.toString,l=c.hasOwnProperty,a=c.toString,i=RegExp("^"+u.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),p=(o="isArray",function(n){return null!=n&&(function(t){return function(t){var r=typeof t;return!!t&&("object"==r||"function"==r)}(t)&&"[object Function]"==a.call(t)}(n)?i.test(u.call(n)):r(n)&&t.test(n))}(e=null==(n=Array)?void 0:n[o])?e:void 0);var f=p||function(t){return r(t)&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}(t.length)&&"[object Array]"==a.call(t)};module.exports=f;