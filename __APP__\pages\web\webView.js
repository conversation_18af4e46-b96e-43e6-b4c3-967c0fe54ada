Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=i(require("./../../npm/wepy/lib/wepy.js")),n=(i(require("./../../api/auth.js")),i(require("./../../mixins/base.js"))),r=(i(require("./../../utils/Tips.js")),i(require("./../../api/member.js"))),o=(i(require("./../../utils/WxUtils.js")),i(require("./../../components/common/loading.js")));i(require("./../../api/riskControl.js"));function i(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(i){function s(){var e,t,r;a(this,s);for(var i=arguments.length,f=Array(i),c=0;c<i;c++)f[c]=arguments[c];return t=r=u(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(f))),r.data={init:!1,url:"",userInfo:{}},r.methods={},r.events={},r.$repeat={},r.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},r.$events={},r.components={Loading:o.default},r.mixins=[n.default],r.config={navigationBarTitleText:""},u(r,t)}var f,c;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,t.default.page),e(s,[{key:"onLoad",value:(f=regeneratorRuntime.mark((function e(n){var o,i,a,u=n.url,s=n.nbc,f=n.nfc,c=n.nbt,l=n.nologin;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=c,(i=f)||(i="#ffffff"),(o=s)&&t.default.setNavigationBarColor({backgroundColor:o,frontColor:i}),a&&t.default.setNavigationBarTitle({title:a}),l){e.next=9;break}return e.next=9,r.default.isJifenLogined();case 9:this.userInfo=t.default.getStorageSync("userInfo"),this.url=decodeURIComponent(u),this.loaded();case 12:case"end":return e.stop()}}),e,this)})),c=function(){var e=f.apply(this,arguments);return new Promise((function(t,n){return function r(o,i){try{var a=e[o](i),u=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(u)}("next")}))},function(e){return c.apply(this,arguments)})}]),s}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(s,"pages/web/webView"));