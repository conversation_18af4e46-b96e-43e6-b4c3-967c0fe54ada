/**
 * 智能API探索器
 * 基于深度分析结果，智能探索真正有效的认证API
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class SmartAPIExplorer {
    constructor() {
        // 已知的认证信息
        this.auth = {
            loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY',
            validAuth: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI'
        };
        
        // 基于深度分析发现的智能探索策略
        this.explorationStrategies = [
            {
                name: '已知有效API验证',
                description: '使用已知有效的API验证认证机制',
                apis: [
                    'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                    'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getPlantingInfo'
                ]
            },
            {
                name: '反向工程探索',
                description: '基于有效API反向推导认证API',
                method: 'reverse_engineering'
            },
            {
                name: '参数变体测试',
                description: '测试不同的参数组合和认证方式',
                method: 'parameter_variants'
            },
            {
                name: '响应头分析',
                description: '分析响应头寻找认证相关信息',
                method: 'header_analysis'
            }
        ];
        
        console.log('🔧 智能API探索器初始化完成');
        console.log('🎯 基于深度分析结果智能探索认证API');
    }

    /**
     * 策略1: 验证已知有效API
     */
    async validateKnownAPIs() {
        console.log('\n🔍 策略1: 验证已知有效API...');
        
        const knownAPIs = [
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getPlantingInfo',
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/planting',
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/harvest'
        ];
        
        const results = [];
        
        for (const api of knownAPIs) {
            console.log(`🧪 测试: ${api}`);
            
            try {
                const result = await this.makeAuthenticatedRequest(api);
                
                if (result.success) {
                    console.log('✅ API有效！');
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    results.push({
                        api: api,
                        success: true,
                        data: result.data
                    });
                } else {
                    console.log('❌ API无效');
                    console.log(`错误: ${result.error}`);
                    results.push({
                        api: api,
                        success: false,
                        error: result.error
                    });
                }
                
            } catch (error) {
                console.log(`💥 异常: ${error.message}`);
                results.push({
                    api: api,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 策略2: 反向工程探索
     */
    async reverseEngineerAPIs() {
        console.log('\n🔍 策略2: 反向工程探索...');
        
        // 基于已知有效API的路径结构，推导可能的认证API
        const basePatterns = [
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/',
            'https://wap.exijiu.com/index.php/API/member/',
            'https://wap.exijiu.com/index.php/API/auth/',
            'https://wap.exijiu.com/index.php/API/user/'
        ];
        
        const authMethods = [
            'getJwt',
            'getToken',
            'getAuth',
            'login',
            'auth',
            'token',
            'jwt',
            'refresh',
            'renew'
        ];
        
        const candidateAPIs = [];
        basePatterns.forEach(base => {
            authMethods.forEach(method => {
                candidateAPIs.push(base + method);
            });
        });
        
        console.log(`📋 生成了 ${candidateAPIs.length} 个候选API`);
        
        const results = [];
        
        for (let i = 0; i < candidateAPIs.length; i++) {
            const api = candidateAPIs[i];
            console.log(`🧪 [${i+1}/${candidateAPIs.length}] 测试: ${api}`);
            
            try {
                const result = await this.makeAuthenticatedRequest(api);
                
                if (result.success) {
                    console.log('✅ 发现有效API！');
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    
                    // 检查是否包含JWT相关信息
                    if (result.data && (result.data.jwt || result.data.token || result.data.auth)) {
                        console.log('🎉 发现JWT相关API！');
                        results.push({
                            api: api,
                            success: true,
                            hasJWT: true,
                            data: result.data
                        });
                    } else {
                        results.push({
                            api: api,
                            success: true,
                            hasJWT: false,
                            data: result.data
                        });
                    }
                } else {
                    // 静默处理失败，避免过多输出
                }
                
            } catch (error) {
                // 静默处理异常
            }
        }
        
        return results;
    }

    /**
     * 策略3: 参数变体测试
     */
    async testParameterVariants() {
        console.log('\n🔍 策略3: 参数变体测试...');
        
        // 基于已知有效的API，测试不同的参数组合
        const baseAPI = 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo';
        
        const parameterVariants = [
            // 添加refresh参数
            { refresh: 'true' },
            { refresh: '1' },
            { force: 'true' },
            { new: 'true' },
            { renew: 'true' },
            
            // 添加token相关参数
            { get_token: 'true' },
            { get_jwt: 'true' },
            { return_token: 'true' },
            
            // 组合参数
            { refresh: 'true', get_token: 'true' },
            { force: 'true', return_jwt: 'true' }
        ];
        
        const results = [];
        
        for (const params of parameterVariants) {
            console.log(`🧪 测试参数: ${JSON.stringify(params)}`);
            
            try {
                const result = await this.makeAuthenticatedRequest(baseAPI, 'GET', params);
                
                if (result.success && result.data) {
                    // 检查响应是否与标准响应不同
                    if (result.data.jwt || result.data.token || result.data.auth) {
                        console.log('🎉 参数变体产生了JWT响应！');
                        console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                        results.push({
                            params: params,
                            success: true,
                            hasJWT: true,
                            data: result.data
                        });
                    }
                }
                
            } catch (error) {
                // 静默处理异常
            }
        }
        
        return results;
    }

    /**
     * 策略4: 响应头分析
     */
    async analyzeResponseHeaders() {
        console.log('\n🔍 策略4: 响应头分析...');
        
        const api = 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo';
        
        try {
            const headers = await this.getResponseHeaders(api);
            
            console.log('📋 响应头分析:');
            Object.entries(headers).forEach(([key, value]) => {
                console.log(`  ${key}: ${value}`);
                
                // 寻找认证相关的头
                if (key.toLowerCase().includes('auth') || 
                    key.toLowerCase().includes('token') || 
                    key.toLowerCase().includes('jwt')) {
                    console.log(`🔑 发现认证相关头: ${key} = ${value}`);
                }
            });
            
            return { success: true, headers: headers };
            
        } catch (error) {
            console.log(`💥 响应头分析失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 执行认证请求
     */
    async makeAuthenticatedRequest(url, method = 'GET', params = null) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Authorization': this.auth.validAuth,
            'login_code': this.auth.loginCode,
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
        
        let requestUrl = url;
        if (params && method === 'GET') {
            const queryString = Object.keys(params)
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
            requestUrl = `${url}?${queryString}`;
        }
        
        try {
            const response = await this.makeRequest(requestUrl, method, method === 'POST' ? params : null, headers);
            
            if (response.err === 0) {
                return {
                    success: true,
                    data: response
                };
            } else {
                return {
                    success: false,
                    error: response.msg || 'API调用失败'
                };
            }
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取响应头
     */
    async getResponseHeaders(url) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname,
                method: 'HEAD',
                headers: {
                    'Authorization': this.auth.validAuth,
                    'login_code': this.auth.loginCode,
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
                },
                timeout: 5000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                resolve(res.headers);
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            req.end();
        });
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 5000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve(jsonData);
                    } catch (e) {
                        reject(new Error(`响应解析失败: ${e.message}`));
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的智能API探索
     */
    async runSmartAPIExploration() {
        console.log('🚀 开始智能API探索...');
        console.log('🎯 基于深度分析结果智能寻找认证API');
        
        const allResults = {
            knownAPIs: [],
            reverseEngineered: [],
            parameterVariants: [],
            headerAnalysis: null
        };
        
        try {
            // 策略1: 验证已知有效API
            console.log('\n' + '='.repeat(60));
            console.log('🔍 策略1: 验证已知有效API');
            console.log('='.repeat(60));
            allResults.knownAPIs = await this.validateKnownAPIs();
            
            // 策略2: 反向工程探索
            console.log('\n' + '='.repeat(60));
            console.log('🔍 策略2: 反向工程探索');
            console.log('='.repeat(60));
            allResults.reverseEngineered = await this.reverseEngineerAPIs();
            
            // 策略3: 参数变体测试
            console.log('\n' + '='.repeat(60));
            console.log('🔍 策略3: 参数变体测试');
            console.log('='.repeat(60));
            allResults.parameterVariants = await this.testParameterVariants();
            
            // 策略4: 响应头分析
            console.log('\n' + '='.repeat(60));
            console.log('🔍 策略4: 响应头分析');
            console.log('='.repeat(60));
            allResults.headerAnalysis = await this.analyzeResponseHeaders();
            
            // 分析所有结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 智能API探索结果分析');
            console.log('='.repeat(60));
            
            const jwtAPIs = [
                ...allResults.reverseEngineered.filter(r => r.hasJWT),
                ...allResults.parameterVariants.filter(r => r.hasJWT)
            ];
            
            const validAPIs = [
                ...allResults.knownAPIs.filter(r => r.success),
                ...allResults.reverseEngineered.filter(r => r.success)
            ];
            
            console.log(`📈 探索统计:`);
            console.log(`  🔑 发现JWT API: ${jwtAPIs.length}`);
            console.log(`  ✅ 有效API: ${validAPIs.length}`);
            console.log(`  📋 已知API验证: ${allResults.knownAPIs.length}`);
            console.log(`  🔧 反向工程发现: ${allResults.reverseEngineered.length}`);
            
            if (jwtAPIs.length > 0) {
                console.log('\n🎉 智能探索成功！发现JWT相关API:');
                jwtAPIs.forEach((api, index) => {
                    console.log(`  ${index + 1}. ${api.api || '参数变体'}`);
                    if (api.data && (api.data.jwt || api.data.token)) {
                        console.log(`     🔑 Token: ${api.data.jwt || api.data.token}`);
                    }
                });
                
                return {
                    success: true,
                    hasJWTAPIs: true,
                    jwtAPIs: jwtAPIs,
                    allResults: allResults
                };
            } else if (validAPIs.length > 0) {
                console.log('\n💡 智能探索部分成功！');
                console.log('✅ 确认了认证机制有效，但未发现JWT生成API');
                
                return {
                    success: true,
                    hasJWTAPIs: false,
                    validAPIs: validAPIs,
                    allResults: allResults
                };
            } else {
                console.log('\n🤔 智能探索未发现新的API');
                console.log('💡 可能需要更深入的逆向分析');
                
                return {
                    success: false,
                    allResults: allResults
                };
            }
            
        } catch (error) {
            console.log('\n❌ 智能API探索失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = SmartAPIExplorer;

// 如果直接运行此文件
if (require.main === module) {
    const explorer = new SmartAPIExplorer();
    
    console.log('🔍 智能API探索器');
    console.log('🎯 基于深度分析结果智能探索认证API');
    console.log('🔑 使用多种策略寻找JWT生成API');
    console.log('');
    
    // 运行智能API探索
    explorer.runSmartAPIExploration().then(result => {
        if (result.success) {
            console.log('\n🎉 智能API探索成功！');
            
            if (result.hasJWTAPIs) {
                console.log('\n🎊 最重要的发现 - JWT API:');
                result.jwtAPIs.forEach(api => {
                    console.log(`🔑 ${api.api || '参数变体'}`);
                });
            }
        } else {
            console.log('\n🤔 智能API探索需要进一步研究');
            console.log('💡 建议进行更深入的逆向工程');
        }
    }).catch(error => {
        console.error('💥 探索异常:', error);
    });
}
