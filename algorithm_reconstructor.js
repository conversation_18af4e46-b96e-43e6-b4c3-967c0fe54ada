/**
 * 算法重构器
 * 基于深度分析发现的认证流程算法进行精确重构
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AlgorithmReconstructor {
    constructor() {
        // 从深度分析中发现的关键算法片段
        this.discoveredAlgorithm = {
            // 核心认证流程
            getJifenShopJwt: {
                endpoint: '/Member/getJwt',
                method: 'GET',
                description: '获取积分商城JWT的核心API'
            },
            
            // 关键的数据转换逻辑
            dataTransformation: {
                input: 'n = API响应',
                process: 'r = {expire_time: n.expire_time, authorized_token: n.jwt}',
                output: 'globalData.auth.Authorization = r.authorized_token',
                description: '将API返回的jwt字段转换为authorized_token，然后赋值给Authorization'
            },
            
            // 过期检查逻辑
            expireCheck: {
                condition: 'expire_time < parseInt((new Date).getTime()/1e3+3600)',
                description: '检查过期时间是否小于当前时间+1小时'
            },
            
            // 存储逻辑
            storage: [
                'setStorageSync("authData", r)',
                'setStorageSync("Authorization", r.authorized_token)'
            ],
            
            // 全局数据赋值
            globalAssignment: 'globalData.auth.Authorization = r.authorized_token'
        };
        
        // 已知的认证信息
        this.auth = {
            loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY',
            baseUrl: 'https://wap.exijiu.com/index.php/API'
        };
        
        console.log('🔧 算法重构器初始化完成');
        console.log('🎯 基于深度分析发现的认证流程算法进行精确重构');
    }

    /**
     * 分析发现的算法逻辑
     */
    analyzeDiscoveredAlgorithm() {
        console.log('\n🔍 分析发现的算法逻辑...');
        
        console.log('📋 核心算法流程:');
        console.log('1. API调用: GET /Member/getJwt');
        console.log('2. 数据转换: {expire_time: n.expire_time, authorized_token: n.jwt}');
        console.log('3. 全局赋值: globalData.auth.Authorization = r.authorized_token');
        console.log('4. 存储数据: setStorageSync("authData", r)');
        console.log('5. 存储Token: setStorageSync("Authorization", r.authorized_token)');
        
        console.log('\n🔑 关键发现:');
        console.log('- Authorization的值直接来自API响应的jwt字段');
        console.log('- 没有复杂的加密或签名过程');
        console.log('- 只是简单的字段重命名和赋值');
        console.log('- 过期检查基于expire_time字段');
        
        console.log('\n💡 算法本质:');
        console.log('Authorization = getJwt().jwt');
        console.log('这是一个简单的API调用和字段映射过程');
        
        return {
            complexity: 'simple',
            type: 'api_field_mapping',
            encryption: 'none',
            keySteps: [
                'api_call',
                'field_mapping', 
                'global_assignment',
                'storage'
            ]
        };
    }

    /**
     * 重构getJwt API调用
     */
    async reconstructGetJwtAPI() {
        console.log('\n🔧 重构getJwt API调用...');
        
        const endpoint = this.auth.baseUrl + this.discoveredAlgorithm.getJifenShopJwt.endpoint;
        console.log(`📍 API端点: ${endpoint}`);
        
        // 构建请求头
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'login_code': this.auth.loginCode,
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
        
        console.log('📋 请求配置:');
        console.log(`- 方法: ${this.discoveredAlgorithm.getJifenShopJwt.method}`);
        console.log(`- 端点: ${this.discoveredAlgorithm.getJifenShopJwt.endpoint}`);
        console.log('- 认证: login_code');
        
        try {
            console.log('\n🚀 执行API调用...');
            const result = await this.makeRequest(endpoint, 'GET', null, headers);
            
            console.log('📊 API响应:');
            console.log(JSON.stringify(result, null, 2));
            
            if (result.err === 0 && result.jwt) {
                console.log('\n🎉 API调用成功！');
                console.log(`🔑 获得JWT: ${result.jwt}`);
                console.log(`⏰ 过期时间: ${result.expire_time}`);
                
                // 执行算法中的数据转换
                const transformedData = this.executeDataTransformation(result);
                
                return {
                    success: true,
                    apiResponse: result,
                    transformedData: transformedData,
                    newAuthorization: transformedData.authorized_token
                };
            } else {
                console.log('\n❌ API调用失败');
                console.log(`错误信息: ${result.msg || '未知错误'}`);
                
                return {
                    success: false,
                    error: result.msg || '未知错误',
                    apiResponse: result
                };
            }
            
        } catch (error) {
            console.log('\n💥 API调用异常:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 执行数据转换逻辑
     */
    executeDataTransformation(apiResponse) {
        console.log('\n🔄 执行数据转换逻辑...');
        
        // 按照发现的算法执行转换
        // r = {expire_time: n.expire_time, authorized_token: n.jwt}
        const transformedData = {
            expire_time: apiResponse.expire_time,
            authorized_token: apiResponse.jwt
        };
        
        console.log('📋 转换过程:');
        console.log(`输入: ${JSON.stringify(apiResponse)}`);
        console.log(`输出: ${JSON.stringify(transformedData)}`);
        
        console.log('\n🎯 关键赋值:');
        console.log(`globalData.auth.Authorization = "${transformedData.authorized_token}"`);
        
        return transformedData;
    }

    /**
     * 验证重构的算法
     */
    async validateReconstructedAlgorithm(newAuthorization) {
        console.log('\n🧪 验证重构的算法...');
        
        try {
            // 使用新的Authorization测试API调用
            const testAPI = 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo';
            
            const result = await this.makeRequest(testAPI, 'GET', null, {
                'Content-Type': 'application/json',
                'Authorization': newAuthorization,
                'login_code': this.auth.loginCode,
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
            });
            
            if (result.err === 0) {
                console.log('✅ 新Authorization验证成功！');
                console.log('🎊 算法重构完全正确！');
                return true;
            } else {
                console.log('❌ 新Authorization验证失败');
                console.log(`错误信息: ${result.msg}`);
                return false;
            }
            
        } catch (error) {
            console.log('💥 验证异常:', error.message);
            return false;
        }
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    console.log(`📊 响应状态码: ${res.statusCode}`);
                    console.log(`📄 原始响应: ${responseData.substring(0, 500)}...`);

                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve(jsonData);
                    } catch (e) {
                        console.log('❌ JSON解析失败，可能是HTML响应');
                        resolve({
                            err: 1,
                            msg: 'API返回HTML而非JSON',
                            rawResponse: responseData,
                            statusCode: res.statusCode
                        });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的算法重构
     */
    async runCompleteAlgorithmReconstruction() {
        console.log('🚀 开始完整的算法重构...');
        console.log('🎯 基于深度分析发现的认证流程算法');
        
        try {
            // 1. 分析发现的算法
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 分析发现的算法逻辑');
            console.log('='.repeat(60));
            const algorithmAnalysis = this.analyzeDiscoveredAlgorithm();
            
            // 2. 重构getJwt API调用
            console.log('\n' + '='.repeat(60));
            console.log('🔧 第二阶段: 重构getJwt API调用');
            console.log('='.repeat(60));
            const reconstructionResult = await this.reconstructGetJwtAPI();
            
            // 3. 验证重构的算法
            if (reconstructionResult.success) {
                console.log('\n' + '='.repeat(60));
                console.log('🧪 第三阶段: 验证重构的算法');
                console.log('='.repeat(60));
                const isValid = await this.validateReconstructedAlgorithm(reconstructionResult.newAuthorization);
                
                if (isValid) {
                    console.log('\n' + '='.repeat(60));
                    console.log('🎊 算法重构完全成功！');
                    console.log('='.repeat(60));
                    
                    console.log('\n🎉 重构成功总结:');
                    console.log(`🔑 新Authorization: ${reconstructionResult.newAuthorization}`);
                    console.log(`⏰ 过期时间: ${new Date(reconstructionResult.transformedData.expire_time * 1000).toLocaleString('zh-CN')}`);
                    console.log(`🎯 算法类型: ${algorithmAnalysis.type}`);
                    console.log(`🔐 加密方式: ${algorithmAnalysis.encryption}`);
                    
                    console.log('\n💡 算法核心:');
                    console.log('1. 调用 GET /Member/getJwt');
                    console.log('2. 提取响应中的 jwt 字段');
                    console.log('3. 重命名为 authorized_token');
                    console.log('4. 赋值给 globalData.auth.Authorization');
                    
                    return {
                        success: true,
                        newAuthorization: reconstructionResult.newAuthorization,
                        expireTime: reconstructionResult.transformedData.expire_time,
                        algorithmType: algorithmAnalysis.type,
                        reconstructionResult: reconstructionResult
                    };
                } else {
                    console.log('\n❌ 算法重构失败 - 验证不通过');
                    return {
                        success: false,
                        reason: 'validation_failed',
                        reconstructionResult: reconstructionResult
                    };
                }
            } else {
                console.log('\n❌ 算法重构失败 - API调用失败');
                return {
                    success: false,
                    reason: 'api_call_failed',
                    error: reconstructionResult.error
                };
            }
            
        } catch (error) {
            console.log('\n💥 算法重构异常:', error.message);
            return {
                success: false,
                reason: 'exception',
                error: error.message
            };
        }
    }
}

// 导出类
module.exports = AlgorithmReconstructor;

// 如果直接运行此文件
if (require.main === module) {
    const reconstructor = new AlgorithmReconstructor();
    
    console.log('🔧 算法重构器');
    console.log('🎯 基于深度分析发现的认证流程算法进行精确重构');
    console.log('🔑 目标：重构出完整的Authorization生成算法');
    console.log('');
    
    // 运行完整的算法重构
    reconstructor.runCompleteAlgorithmReconstruction().then(result => {
        if (result.success) {
            console.log('\n🎊 算法重构完全成功！');
            console.log(`🔑 新Authorization: ${result.newAuthorization}`);
            console.log('🎯 现在可以实现真正的自动化了！');
        } else {
            console.log('\n🤔 算法重构未完全成功');
            console.log(`原因: ${result.reason}`);
            console.log('💡 需要进一步分析和调试');
        }
    }).catch(error => {
        console.error('💥 重构异常:', error);
    });
}
