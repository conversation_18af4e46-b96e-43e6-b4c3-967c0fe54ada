"function"==typeof Symbol&&Symbol.iterator;var o=[];module.exports={addNotification:function(e,t,i){e&&t?(i||console.log("addNotification Warning: no observer will can't remove notice"),console.log("addNotification:"+e),function(e){o.push(e)}({name:e,selector:t,observer:i})):console.log("addNotification error: no selector or name")},removeNotification:function(e,t){for(var i=0;i<o.length;i++){var n=o[i];if(n.name===e&&n.observer===t)return void o.splice(i,1)}},postNotificationName:function(e,t){if(console.log("postNotificationName:"+e),0!=o.length)for(var i=0;i<o.length;i++){var n=o[i];n.name===e&&n.selector(t)}else console.log("postNotificationName error: u hadn't add any notice.")},addOnceNotification:function(e,t,i){if(o.length>0)for(var n=0;n<o.length;n++){var r=o[n];if(r.name===e&&r.observer===i)return}this.addNotification(e,t,i)}};