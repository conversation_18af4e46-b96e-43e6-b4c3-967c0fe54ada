Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=require("./../../npm/wepy/lib/wepy.js"),n=(e=t)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function t(){var e,n,i;o(this,t);for(var s=arguments.length,a=Array(s),u=0;u<s;u++)a[u]=arguments[u];return n=i=r(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(a))),i.data={list:[{signTime:30,signed:1},{signTime:30},{signTime:30},{signTime:30},{signTime:30},{signTime:30},{signTime:30}],aways:0},i.methods={},r(i,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,n.default.component),t}();exports.default=i;