Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=require("./../../npm/wepy/lib/wepy.js"),n=(t=e)&&t.__esModule?t:{default:t};function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var r=function(t){function e(){var t,n,r;o(this,e);for(var a=arguments.length,s=Array(a),c=0;c<a;c++)s[c]=arguments[c];return n=r=i(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(s))),r.props={tap:{}},r.data={display:!1,message:"",waitSecond:{},timeoutId:null},r.computed={isWaiting:function(){return this.waitSecond>0},btnText:function(){return this.isWaiting>0?"请等待"+this.waitSecond+"秒":"获取验证码"}},r.methods={cd:function(t){var e=this;this.isWaiting||(this.waitSecond=t,this.interval=setInterval((function(){e.waitSecond--,0===e.waitSecond&&clearInterval(e.interval),e.$apply()}),1e3))},onCodeTap:function(){this.isWaiting||this.$emit("tap")}},i(r,n)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,n.default.component),e}();exports.default=r;