Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=i(require("./../npm/wepy/lib/wepy.js")),r=i(require("./../utils/Lang.js")),n=i(require("./../utils/Validate.js"));function i(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(i){function a(){var e,t,r;u(this,a);for(var n=arguments.length,i=Array(n),l=0;l<n;l++)i[l]=arguments[l];return t=r=o(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(i))),r.data={input:{}},r.methods={input:function(e){var t=e.currentTarget.id;this.input[t]=e.detail.value},radio:function(e){var t=e.currentTarget.id;this.input[t]=e.detail.value}},o(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default.mixin),e(a,[{key:"onUnload",value:function(){this.input={}}},{key:"isEmpty",value:function(e){return r.default.isEmpty(e)}},{key:"isNotEmpty",value:function(e){return!r.default.isEmpty(e)}},{key:"tips",value:function(e){this.$invoke("Tips","show",e)}},{key:"check",value:function(e){var t=!0,n=!1,i=void 0;try{for(var u,o=e[Symbol.iterator]();!(t=(u=o.next()).done);t=!0){var a=u.value,l=a.value;if("noDuplicate"!=a.method&&r.default.isArray(l)){var f=!0,c=!1,s=void 0;try{for(var p,y=l[Symbol.iterator]();!(f=(p=y.next()).done);f=!0){var v=p.value;if(!this.execCheck(a,v))return!1}}catch(e){c=!0,s=e}finally{try{!f&&y.return&&y.return()}finally{if(c)throw s}}}else{if(!this.execCheck(a,l))return!1}}}catch(e){n=!0,i=e}finally{try{!t&&o.return&&o.return()}finally{if(n)throw i}}return!0}},{key:"execCheck",value:function(e,t){return!!n.default[e.method].bind(n.default)(t,e.param)||(this.tips(e.message),!1)}},{key:"onInput",value:function(e){var t=e.currentTarget.id;this.input[t]=e.detail.value}}]),a}();exports.default=a;