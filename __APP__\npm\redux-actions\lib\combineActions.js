exports.__esModule=!0,exports.default=function(){for(var e=arguments.length,r=new Array(e),u=0;u<e;u++)r[u]=arguments[u];(0,t.default)(l(r),"Expected action types to be strings, symbols, or action creators");var s=r.map(i.default).join(n.ACTION_TYPE_DELIMITER);return{toString:function(){return s}}};var t=o(require("./../../invariant/browser.js")),e=o(require("./utils/isFunction.js")),r=o(require("./utils/isSymbol.js")),u=o(require("./utils/isEmpty.js")),i=o(require("./utils/toString.js")),s=o(require("./utils/isString.js")),n=require("./constants.js");function o(t){return t&&t.__esModule?t:{default:t}}function a(t){return(0,s.default)(t)||(0,e.default)(t)||(0,r.default)(t)}function l(t){return!(0,u.default)(t)&&t.every(a)}