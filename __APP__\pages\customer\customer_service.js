Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=n(require("./../../npm/wepy/lib/wepy.js"));n(require("./../../utils/WxUtils.js"));function n(e){return e&&e.__esModule?e:{default:e}}function r(e){if(null==e)throw new TypeError("Cannot destructure undefined")}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(o,i){try{var u=t[o](i),a=u.value}catch(e){return void n(e)}if(!u.done)return Promise.resolve(a).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(a)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(n){function a(){var e,t,n;i(this,a);for(var r=arguments.length,o=Array(r),c=0;c<r;c++)o[c]=arguments[c];return t=n=u(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(o))),n.data={init:!1,stepsecend:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(4).jpg"],imgalist:["http://wap.exijiu.cn/Public/Qrcode/default/img/ewm.jpg"]},n.mixins=[],n.methods={goto:function(e){var t=e.currentTarget.dataset.url;this.$navigate(t)},callPhone:function(){wx.makePhoneCall({phoneNumber:"4006671988"})}},n.components={},n.computed={},n.mixins=[],n.config={navigationBarTitleText:"咨询客服"},u(n,t)}var c,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default.page),e(a,[{key:"onLoad",value:(s=o(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r(t);case 1:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"onShow",value:(c=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})}]),a}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(a,"pages/customer/customer_service"));