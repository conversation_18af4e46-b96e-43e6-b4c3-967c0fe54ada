exports.__esModule=!0;var t=Object.assign||function(t){for(var r=1;r<arguments.length;r++){var e=arguments[r];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t};exports.default=function(){for(var r=arguments.length,e=Array(r),a=0;a<r;a++)e[a]=arguments[a];return function(r){return function(a,o,u){var c,i=r(a,o,u),s=i.dispatch,p={getState:i.getState,dispatch:function(t){return s(t)}};return c=e.map((function(t){return t(p)})),s=n.default.apply(void 0,c)(i.dispatch),t({},i,{dispatch:s})}}};var r,e=require("./compose.js"),n=(r=e)&&r.__esModule?r:{default:r};