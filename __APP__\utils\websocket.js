var e=!1,o=!1,t=[],c="",s=0,n=null,a=null,l={connectSocket:function(c){wx.showLoading({title:"",mask:!0}),e=!1,o=!1,t=[],wx.connectSocket({url:"开发者服务器接口地",success:function(e){c&&c.success&&c.success(e)},fail:function(e){c&&c.fail&&c.fail(e)}})},sendSocketMessage:function(o){e?wx.sendSocketMessage({data:o.msg,success:function(e){o&&o.success&&o.success(e)},fail:function(e){o&&o.fail&&o.fail(e)}}):t.push(o.msg)},closeSocket:function(e){a&&(clearTimeout(a),a=null),o=!0;this.stopHeartBeat(),wx.closeSocket({success:function(o){console.log("WebSocket 已关闭！"),e&&e.success&&e.success(o)},fail:function(o){e&&e.fail&&e.fail(o)}})},onSocketMessageCallback:function(e){},startHeartBeat:function(){console.log("socket开始心跳");c="heart",this.heartBeat()},stopHeartBeat:function(){console.log("socket结束心跳");c="",n&&(clearTimeout(n),n=null),a&&(clearTimeout(a),a=null)},heartBeat:function(){var e=this;c&&e.sendSocketMessage({msg:JSON.stringify({msg_type:"heart"}),success:function(o){console.log("socket心跳成功"),c&&(n=setTimeout((function(){e.heartBeat()}),7e3))},fail:function(o){console.log("socket心跳失败"),s>2&&e.connectSocket(),c&&(n=setTimeout((function(){e.heartBeat()}),7e3)),s++}})}};wx.onSocketOpen((function(c){if(console.log("WebSocket连接已打开！"),wx.hideLoading(),o)l.closeSocket();else{e=!0;for(var s=0;s<t.length;s++)l.sendSocketMessage(t[s]);t=[],l.startHeartBeat()}})),wx.onSocketError((function(e){console.log("WebSocket连接打开失败，请检查！",e)})),wx.onSocketMessage((function(e){console.log("收到服务器内容："+e.data),l.onSocketMessageCallback(e.data)})),wx.onSocketClose((function(e){console.log("WebSocket 已关闭！"),o||(clearTimeout(a),a=setTimeout((function(){l.connectSocket()}),3e3))})),module.exports=l;