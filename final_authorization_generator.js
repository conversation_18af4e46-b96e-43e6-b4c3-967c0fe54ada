/**
 * 最终Authorization生成器
 * 基于源码完全破解的Authorization生成算法
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class FinalAuthorizationGenerator {
    constructor() {
        // 基于源码发现的配置
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码中发现的API配置
        this.baseUrl = 'https://wap.exijiu.com/index.php/API';
        this.jifenShopApiUrl = 'https://apimallwm.exijiu.com/api';
        
        // 从源码中发现的关键API路径
        this.apis = {
            getJwt: '/Member/getJwt',  // getJifenShopJwt方法使用的API
            createJwt: '/api/v2/jifenCrm/createJwt'  // jifenCrmCreateJwt方法使用的API
        };
        
        // 模拟存储
        this.storage = {
            authData: null,
            Authorization: null
        };
        
        console.log('🔧 最终Authorization生成器初始化完成');
        console.log('🎯 基于源码完全破解的生成算法');
    }

    /**
     * 构建请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'login_code': this.loginCode
        };
    }

    /**
     * 模拟getStorageSync
     */
    async getStorageSync(key) {
        return this.storage[key] || null;
    }

    /**
     * 模拟setStorageSync
     */
    async setStorageSync(key, value) {
        this.storage[key] = value;
        console.log(`💾 存储 ${key}:`, typeof value === 'object' ? JSON.stringify(value) : value);
    }

    /**
     * getJifenShopJwt方法 (基于源码)
     */
    async getJifenShopJwt() {
        console.log('\n🔍 调用getJifenShopJwt...');
        console.log('📍 API路径:', this.apis.getJwt);
        console.log('🌐 完整URL:', this.baseUrl + this.apis.getJwt);
        
        try {
            const result = await this.makeRequest(this.baseUrl, this.apis.getJwt, 'GET');
            
            if (result.success) {
                console.log('✅ getJifenShopJwt调用成功!');
                console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                return result.data;
            } else {
                console.log('❌ getJifenShopJwt调用失败');
                console.log('📄 错误信息:', result.data?.msg || '未知错误');
                console.log('📄 状态码:', result.status);
                return null;
            }
            
        } catch (error) {
            console.log('❌ getJifenShopJwt请求异常:', error.message);
            return null;
        }
    }

    /**
     * jifenCrmCreateJwt方法 (基于源码)
     */
    async jifenCrmCreateJwt() {
        console.log('\n🔍 调用jifenCrmCreateJwt...');
        console.log('📍 API路径:', this.apis.createJwt);
        console.log('🌐 完整URL:', this.jifenShopApiUrl + this.apis.createJwt);
        
        try {
            const result = await this.makeRequest(this.jifenShopApiUrl, this.apis.createJwt, 'GET');
            
            if (result.success) {
                console.log('✅ jifenCrmCreateJwt调用成功!');
                console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                return result.data;
            } else {
                console.log('❌ jifenCrmCreateJwt调用失败');
                console.log('📄 错误信息:', result.data?.msg || '未知错误');
                console.log('📄 状态码:', result.status);
                return null;
            }
            
        } catch (error) {
            console.log('❌ jifenCrmCreateJwt请求异常:', error.message);
            return null;
        }
    }

    /**
     * getAuthData方法 (基于源码完全复制的逻辑)
     */
    async getAuthData() {
        console.log('\n🔄 执行getAuthData方法...');
        console.log('📋 基于源码完全复制的逻辑');
        
        try {
            // 步骤1: 获取存储中的authData
            console.log('📋 步骤1: 获取存储中的authData...');
            let authData = await this.getStorageSync("authData");
            
            // 步骤2: 检查是否过期 (源码逻辑: expire_time < parseInt((new Date).getTime()/1e3+3600))
            const currentTime = parseInt((new Date()).getTime() / 1000 + 3600); // 当前时间 + 1小时
            
            if (!authData || authData.expire_time < currentTime) {
                console.log('📋 步骤2: authData不存在或已过期，需要获取新的JWT');
                console.log(`  当前时间+1小时: ${currentTime}`);
                console.log(`  存储的过期时间: ${authData?.expire_time || '无'}`);
                
                // 步骤3: 调用getJifenShopJwt (源码: t.getJifenShopJwt())
                console.log('📋 步骤3: 调用getJifenShopJwt...');
                const jwtResponse = await this.getJifenShopJwt();
                
                if (!jwtResponse) {
                    throw new Error('getJifenShopJwt调用失败');
                }
                
                // 步骤4: 构建新的authData (源码: r={expire_time:n.expire_time,authorized_token:n.jwt})
                console.log('📋 步骤4: 构建新的authData...');
                authData = {
                    expire_time: jwtResponse.expire_time,
                    authorized_token: jwtResponse.jwt  // 关键：jwt字段变成authorized_token
                };
                
                console.log('📊 构建的authData:', JSON.stringify(authData, null, 2));
                
                // 步骤5: 设置全局Authorization (源码: s.default.$instance.globalData.auth.Authorization=r.authorized_token)
                console.log('📋 步骤5: 设置全局Authorization...');
                const authorization = authData.authorized_token;
                
                // 步骤6: 保存到存储 (源码: s.default.setStorageSync("authData",r))
                console.log('📋 步骤6: 保存到存储...');
                await this.setStorageSync("authData", authData);
                
                // 步骤7: 保存Authorization (源码: s.default.setStorageSync("Authorization",r.authorized_token))
                console.log('📋 步骤7: 保存Authorization...');
                await this.setStorageSync("Authorization", authData.authorized_token);
                
                console.log('🎉 getAuthData执行成功!');
                console.log('🔑 生成的Authorization:', authorization);
                
            } else {
                console.log('📋 步骤2: authData仍然有效，直接使用');
                console.log(`  当前时间+1小时: ${currentTime}`);
                console.log(`  存储的过期时间: ${authData.expire_time}`);
            }
            
            return authData;
            
        } catch (error) {
            console.log('❌ getAuthData执行失败:', error.message);
            return null;
        }
    }

    /**
     * 验证生成的Authorization
     */
    async validateAuthorization(authorization) {
        console.log('\n🔍 验证生成的Authorization...');
        
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': authorization,
                'login_code': this.loginCode,
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
            };
            
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API',
                '/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                headers
            );
            
            if (result.success) {
                console.log('✅ Authorization验证成功!');
                console.log('👤 用户信息:', result.data.nick_name || '未知用户');
                return true;
            } else {
                console.log('❌ Authorization验证失败');
                console.log('📄 错误信息:', result.data?.msg || '未知错误');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Authorization验证异常:', error.message);
            return false;
        }
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method = 'GET', data = null, customHeaders = null) {
        const headers = customHeaders || this.buildHeaders();
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 生成新的Authorization (完整流程)
     */
    async generateAuthorization() {
        console.log('🚀 开始生成新的Authorization...');
        console.log('🎯 使用源码完全破解的生成算法');
        
        try {
            // 执行getAuthData逻辑
            const authData = await this.getAuthData();
            
            if (!authData || !authData.authorized_token) {
                console.log('❌ Authorization生成失败');
                return null;
            }
            
            const authorization = authData.authorized_token;
            
            // 验证生成的Authorization
            const isValid = await this.validateAuthorization(authorization);
            
            if (isValid) {
                console.log('\n🎉 Authorization生成并验证成功!');
                console.log('🔑 新的Authorization:', authorization);
                console.log('\n💡 使用方法:');
                console.log('1. 将此Authorization设置为请求头');
                console.log('2. 配合login_code一起使用');
                console.log('3. 可以访问所有需要认证的API');
                console.log('4. 当过期时，重新调用此方法生成新的');
                
                return authorization;
            } else {
                console.log('❌ 生成的Authorization验证失败');
                return null;
            }
            
        } catch (error) {
            console.log('❌ Authorization生成异常:', error.message);
            return null;
        }
    }

    /**
     * 尝试多种方法生成Authorization
     */
    async tryMultipleMethods() {
        console.log('🔍 尝试多种方法生成Authorization...');
        
        const methods = [
            {
                name: 'getAuthData方法',
                func: () => this.getAuthData()
            },
            {
                name: 'jifenCrmCreateJwt方法',
                func: () => this.jifenCrmCreateJwt()
            },
            {
                name: '直接getJifenShopJwt方法',
                func: () => this.getJifenShopJwt()
            }
        ];
        
        for (const method of methods) {
            console.log(`\n🧪 尝试方法: ${method.name}`);
            
            try {
                const result = await method.func();
                
                if (result) {
                    let authorization = null;
                    
                    if (result.authorized_token) {
                        authorization = result.authorized_token;
                    } else if (result.jwt) {
                        authorization = result.jwt;
                    } else if (result.auth) {
                        authorization = result.auth;
                    }
                    
                    if (authorization) {
                        console.log(`✅ ${method.name} 成功获取token`);
                        
                        const isValid = await this.validateAuthorization(authorization);
                        if (isValid) {
                            console.log(`🎉 ${method.name} 生成的Authorization验证成功!`);
                            return authorization;
                        }
                    }
                }
                
                console.log(`❌ ${method.name} 未能生成有效的Authorization`);
                
            } catch (error) {
                console.log(`❌ ${method.name} 执行失败: ${error.message}`);
            }
        }
        
        return null;
    }
}

// 导出类
module.exports = FinalAuthorizationGenerator;

// 如果直接运行此文件
if (require.main === module) {
    const generator = new FinalAuthorizationGenerator();
    
    console.log('🔑 最终Authorization生成器');
    console.log('🎯 基于源码完全破解的生成算法');
    console.log('📋 完整复制getAuthData方法逻辑');
    console.log('');
    
    // 生成Authorization
    generator.generateAuthorization().then(authorization => {
        if (authorization) {
            console.log('\n🎊 Authorization生成成功!');
            console.log('🔑 你现在掌握了完整的Authorization生成方法!');
        } else {
            console.log('\n🤔 主要方法失败，尝试其他方法...');
            
            // 尝试多种方法
            generator.tryMultipleMethods().then(altAuth => {
                if (altAuth) {
                    console.log('\n🎉 通过备用方法成功生成Authorization!');
                } else {
                    console.log('\n😔 所有方法都失败了');
                    console.log('💡 可能的原因:');
                    console.log('1. login_code可能已过期');
                    console.log('2. API可能需要额外的认证参数');
                    console.log('3. 请求头可能需要调整');
                }
            });
        }
    }).catch(error => {
        console.error('💥 生成异常:', error);
    });
}
