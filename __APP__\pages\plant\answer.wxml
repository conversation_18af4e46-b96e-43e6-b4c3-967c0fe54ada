<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <view class="one-question-for-day" wx:if="{{init}}">
        <view class="question">
            <view class="question-type">
                <view class="icon-out">
                    <view class="type-icon"></view>
                </view>
                <view class="type">{{type==1?'单选题':type==2?'多选题':'其他'}}</view>
                <view class="{{rightWrong?'correct':'error'}}">{{answerTitle}}</view>
                <view class="amount">
                    <text style="font-size:48rpx;font-weight:bold;">{{Nowindex+1}}</text>
                    <text style="color:#999">/{{amount}}</text>
                </view>
            </view>
            <view class="dividing-line"></view>
            <view class="subject">{{sub_title}}</view>
            <view class="answerList" wx:if="{{type==1}}">
                <view bindtap="changeWhite" class="answers {{index==clickId?chose:''}}" id="{{index}}" wx:for="{{subject}}" wx:key="index">{{item}}</view>
            </view>
            <view class="answerList" wx:if="{{type==2}}">
                <view bindtap="changeWhite2" class="answers {{item.checked==true?chose:''}}" data-bindex="{{bindex}}" wx:for="{{questionArr}}" wx:for-index="bindex" wx:key="bindex">{{item.cont}}</view>
            </view>
            <view class="answerList" wx:if="{{type==3}}"></view>
            <textarea class="answerList" placeholder="请输入答案" style="padding-left:20rpx;padding-top:20rpx;width:600rpx;height:326rpx;background:rgba(242,246,250,1);border-radius:20rpx;" wx:if="{{type==4}}"></textarea>
            <textarea class="answerList" placeholder="请输入答案" style="padding-left:20rpx;padding-top:20rpx;width:600rpx;height:326rpx;background:rgba(242,246,250,1);border-radius:20rpx;" wx:if="{{type==5}}"></textarea>
        </view>
        <view class="subArea">
            <view bindtap="submit" class="submit" wx:if="{{!submited}}">提交</view>
            <view bindtap="getQueations" class="submit" wx:if="{{submited}}">下一题</view>
        </view>
        <view class="rightAnswer">
            <view class="title">
                <view class="type-icon"></view>答案解析</view>
            <view class="dividing-line"></view>
            <view class="contents">{{answer_analysis}}</view>
        </view>
    </view>
</view>
