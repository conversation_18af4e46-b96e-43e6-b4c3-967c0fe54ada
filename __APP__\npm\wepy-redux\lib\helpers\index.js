exports.__esModule=!0,exports.mapActions=exports.mapState=void 0;var t=require("./../store.js");exports.mapState=function(e){var n={};return r(e).forEach((function(r){var e=r.key,a=r.val;n[e]=function(){var r=(0,t.getStore)().getState();return"function"==typeof a?a.call(this,r):r[a]}})),n},exports.mapActions=function(e){var n={};return r(e).forEach((function(r){var e=r.key,a=r.val;n[e]=function(){for(var r=(0,t.getStore)(),e=void 0,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e="string"==typeof a?{type:a,payload:o.length>1?o:o[0]}:"function"==typeof a?a.apply(r,o):a,r.dispatch.call(r,e)}})),n};function r(t){return Array.isArray(t)?t.map((function(t){return{key:t,val:t}})):Object.keys(t).map((function(r){return{key:r,val:t[r]}}))}