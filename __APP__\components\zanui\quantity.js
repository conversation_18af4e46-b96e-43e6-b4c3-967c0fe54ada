Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}return function(e,n,a){return n&&t(e.prototype,n),a&&t(e,a),e}}(),n=require("./../../npm/wepy/lib/wepy.js"),a=(t=n)&&t.__esModule?t:{default:t};function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var u=function(t){function n(){var t,e,a;r(this,n);for(var u=arguments.length,i=Array(u),l=0;l<u;l++)i[l]=arguments[l];return e=a=o(this,(t=n.__proto__||Object.getPrototypeOf(n)).call.apply(t,[this].concat(i))),a.props={quantity:{}},a.data={},a.methods={_handleZanQuantityMinus:function(t){this.handle(t,-1)},_handleZanQuantityPlus:function(t){this.handle(t,1)},_handleZanQuantityBlur:function(t){var e=this,n=t.currentTarget.dataset,a=n.componentId,r=+n.max,o=+n.min,u=t.detail.value;return u?((u=+u)>r?u=r:u<o&&(u=o),this.callback(a,u),""+u):(setTimeout((function(){e.callback(a,o)}),16),this.callback(a,u),""+u)}},o(a,e)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(n,a.default.component),e(n,[{key:"handle",value:function(t,e){var n=t.currentTarget.dataset,a=n.componentId,r=n.disabled,o=+n.quantity;if(r)return null;this.callback(a,o+e)}},{key:"callback",value:function(t,e){var n={componentId:t,quantity:e=+e};this.$emit("change",n)}},{key:"onLoad",value:function(){}}]),n}();exports.default=u;