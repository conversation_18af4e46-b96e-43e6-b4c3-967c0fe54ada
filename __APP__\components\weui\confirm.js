Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=require("./../../npm/wepy/lib/wepy.js"),i=(t=e)&&t.__esModule?t:{default:t};function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var r=function(t){function e(){var t,i,r;o(this,e);for(var s=arguments.length,l=Array(s),a=0;a<s;a++)l[a]=arguments[a];return i=r=n(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(l))),r.data={display:!1,title:"提示",message:"",okVal:"确定",cancelVal:"取消",timeoutId:null,isConfirm:null},r.methods={confirm:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return this.message=t,this.display=!0,e&&(this.title=e),o&&(this.okVal=o),n&&(this.cancelVal=n),this.$apply(),new Promise((function(t,e){var o=setInterval((function(){if(null!==i.isConfirm){var e={confirm:i.isConfirm};t(e),clearInterval(o)}}),100);i.$apply()}))},doCancel:function(){this.display=!1,this.timeoutId=null,this.isConfirm=!1,this.$apply()},doConfirm:function(){this.display=!1,this.timeoutId=null,this.isConfirm=!0,this.$apply()},error:function(t){var e=this;this.display=!0,this.message=t,this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null),this.timeoutId=setTimeout((function(){e.display=!1,e.timeoutId=null,e.$apply()}),1500)}},n(r,i)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,i.default.component),e}();exports.default=r;