<view>
    <view class="sw-container">
        <view class="weui-mask weui-mask--visible" wx:if="{{$WuiConfirm$display}}">
            <image class="weui-dialog-img" src="http://wap.exijiu.cn/Public//Miniprogram/images/green-bell.png"></image>
            <view class="weui-dialog weui-dialog--visible">
                <view class="weui-dialog__hd">
                    <view class="weui-dialog__title">{{$WuiConfirm$title}}</view>
                </view>
                <view class="weui-dialog__bd">{{$WuiConfirm$message}}</view>
                <view class="weui-dialog__ft">
                    <view bindtap="$WuiConfirm$doCancel" class="weui-dialog__btn default">{{$WuiConfirm$cancelVal}}</view>
                    <view bindtap="$WuiConfirm$doConfirm" class="weui-dialog__btn primary">{{$WuiConfirm$okVal}}</view>
                </view>
            </view>
        </view>
        <view class="header">
            <swiper autoplay interval="3000">
                <swiper-item bindtap="gotoWithLogined" data-element_name="顶部背景-{{item.name}}" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{topBanners}}" wx:key="id">
                    <image class="image" mode="widthFix" src="{{item.pic_url}}"></image>
                </swiper-item>
            </swiper>
        </view>
        <view class="user-info">
            <view class="bg">
                <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/2.png"></image>
            </view>
            <view class="member" wx:if="{{!isLogin}}">
                <view class="login-loading-msg">加载中...</view>
            </view>
            <view class="wrap" wx:elif="{{userInfo}}">
                <view class="avatar">
                    <image class="image" mode="aspectFill" src="{{userInfo.head_imgurl||'http://wap.exijiu.com/Public/MemberClub/forum/none-headimg.jpg'}}"></image>
                    <button bindtap="gotoWithLogined" class="btn" data-element_name="个人中心" data-url="/pages/customer/goToUmall?pagePath=%2Fpages%2Findex%2Fmy_v2"></button>
                </view>
                <view class="info">
                    <view class="box" style="line-height:1em;">
                        <view class="user">
                            <view class="username">{{userInfo.nick_name}}<button bindtap="gotoWithLogined" class="btn" data-element_name="会员昵称" data-url="/pages/customer/goToUmall?pagePath=%2Fpages%2Findex%2Fmy_v2"></button>
                            </view>
                            <view bindtap="goto" class="level" data-element_name="会员等级" data-url="{{'/pages/customer/store_commission?name='+userInfo.level_name}}">
                                <view class="icon">
                                    <image class="image" mode="aspectFit" src="{{currentLevelImage}}"></image>
                                </view>
                                <view class="text">{{userInfo.current_level.name}}</view>
                            </view>
                        </view>
                        <view class="score" style="margin-top:14rpx;">
                            <view bindtap="goto" class="box" data-element_name="经验值条" data-url="/pages/customer/goToUmall?pagePath=%2Fpages%2Frich%2Flevel">
                                <view class="tit">经验值</view>
                                <view class="num">{{exp}} / {{maxmin_exp}}</view>
                            </view>
                        </view>
                        <view style="height:2rpx;"></view>
                        <view class="score" style="margin-top:8rpx;">
                            <view bindtap="gotoWithLogined" class="box" data-element_name="积分值条" data-url="/pages/customer/goToJph">
                                <view class="tit">积分值</view>
                                <view class="num">{{userInfo.integration}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="member" wx:elif="{{!userInfo}}">
                <view class="login-loading-msg" wx:if="{{loginLoadingMsg}}">{{loginLoadingMsg}}</view>
                <block wx:else>
                    <button bindtap="saveLastAuth" class="btn" data-element_name="点击登录|saveLastAuth" wx:if="{{hasDataMsCenterUser&&!isLastAuth}}">
                        <view class="text">点击登录</view>
                    </button>
                    <button bindgetphonenumber="getPhoneNumber" class="btn" openType="getPhoneNumber" plain="true" wx:elif="{{agreeRule}}">
                        <view bindtap="checkAgreeRule" class="text" data-element_name="点击登录|getPhoneNumber">点击登录</view>
                    </button>
                    <button bindtap="checkAgreeRule" class="btn" data-element_name="点击登录|checkAgreeRule" wx:else>
                        <view class="text">点击登录</view>
                    </button>
                    <view class="agree-box">
                        <checkbox-group bindchange="changeAgreeRule">
                            <checkbox checked="{{agreeRule}}" class="agree">我已阅读并同意</checkbox>
                        </checkbox-group>
                        <view bindtap="goRules" class="agree-a" data-element_name="用户服务协议" data-wpygorules-a="user">《用户服务协议》</view>和<view bindtap="goRules" class="agree-a" data-element_name="隐私保护指引" data-wpygorules-a="privacy">《隐私保护指引》</view>
                    </view>
                </block>
            </view>
        </view>
        <view class="menu-box">
            <view class="wrap">
                <view bindtap="gotoWithLogined" class="item" data-element_name="会员论坛-菜单" data-url="/pagesforum/pages/community">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/3.png"></image>
                    </view>
                    <view class="tit">会员论坛</view>
                </view>
                <view bindtap="goToYouZanShop" class="item" data-element_name="习酒官方商城-菜单" wx:if="{{userInfo}}">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/junpinhui/shop.png"></image>
                    </view>
                    <view class="tit">习酒官方商城</view>
                </view>
                <view bindtap="gotoWithLogined" class="item" data-element_name="鰼部酒谷-菜单" data-id="1" data-url="/pages/plant/map" style="display:none;" wx:if="{{userInfo}}">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/5.png"></image>
                    </view>
                    <view class="tit">鰼部酒谷</view>
                </view>
                <view bindtap="goto" class="item" data-element_name="习酒文旅-菜单" data-url="/pages/basearea/index">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/5.png"></image>
                    </view>
                    <view class="tit">习酒文旅</view>
                </view>
                <view bindtap="goToOfflineStore" class="item" data-element_name="习酒门店-菜单" wx:if="{{userInfo}}">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/6.png"></image>
                    </view>
                    <view class="tit">习酒门店</view>
                </view>
                <view bindtap="goto" class="item" data-element_name="会员等级-菜单" data-url="/pages/customer/store_commission" wx:if="{{userInfo}}">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/7.png"></image>
                    </view>
                    <view class="tit">会员等级</view>
                </view>
                <view bindtap="gotoWithLogined" class="item" data-element_name="会员活动-菜单" data-url="/pages/customer/activity_list?current=1" wx:if="{{userInfo}}">
                    <view class="icon">
                        <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/8.png"></image>
                    </view>
                    <view class="tit">会员活动</view>
                </view>
            </view>
        </view>
        <view class="link-box">
            <view bindtap="gotoWithLogined" class="item" data-element_name="用户签到-菜单" data-url="/pages/customer/goToUmall?pagePath=%2Fpages%2FsignIn%2FsignIn%3Ffrom%3Dminiprogram_index" wx:if="{{userInfo}}">
                <div class="icon">
                    <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/9.png"></image>
                </div>
            </view>
            <view bindtap="gotoWithLogined" class="item" data-element_name="积分商城-菜单" data-url="/pages/customer/goToJph" wx:if="{{userInfo}}">
                <div class="icon">
                    <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/10.png"></image>
                </div>
            </view>
            <view bindtap="scan" class="item" data-element_name="扫码入口-菜单">
                <div class="icon">
                    <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/11.png"></image>
                </div>
            </view>
        </view>
        <view class="banner">
            <view class="swiper mt10">
                <swiper autoplay bindchange="nonlinearSliding" class="bottom-banner-swiper" indicatorActiveColor="#EEE" indicatorDots="true" interval="{{bottomBannerInterval[ bottomBannerInterval['index'] ]}}">
                    <swiper-item bindtap="gotoWithLogined" class="swiper-box" data-element_name="底部banner图-{{item.name}}" data-event="{{item.event_id}}" data-id="2" data-url="{{item.out_site_link}}" wx:for="{{bottomBanners}}" wx:key="indexs">
                        <image mode="widthFix" src="{{item.pic_url}}"></image>
                    </swiper-item>
                </swiper>
            </view>
        </view>
        <view bindtap="goto" class="kefu" data-element_name="联系客服按钮" data-url="/pages/customer/customer_service">
            <image class="img-plus-style" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/12.png"></image>
        </view>
        <view class="footer">
            <view class="icon">
                <image class="image" mode="aspectFit" src="http://wap.exijiu.com/Public/MemberClubV2/images/new/footer.png?v=111"></image>
            </view>
        </view>
        <view animation="{{animationData}}" class="coupon_show_animation">
            <view class="coupon_show">
                <view class="view_title">防伪系统备案证明</view>
                <view style="height:auto;">
                    <image src="http://wap.exijiu.com/Public/Qrcode/images/xiaochengxuimages/icons/certificate.jpg" style="display:block;width:100%;"></image>
                </view>
                <view bindtap="hide" class="textcancle">关闭</view>
            </view>
        </view>
        <view hidden="{{birthdayBoxIsHidden}}">
            <view>
                <view class="pop-alert-box dialog">
                    <view class="alert-content-box">
                        <view class="alert-content">
                            <image bindtap="gotoWithLogined" class="icon_alert_dialogs" data-element_name="生日弹出框-领取图" data-url="/pages/customer/birthday_present" mode="widthFix" src="http://wap.exijiu.com/Public/MemberClub/images/birthdays.png" style="width:520rpx;border-radius:10rpx;"></image>
                            <view class="btn_box">
                                <view bindtap="gotoWithLogined" class="button type_red" data-element_name="生日弹出框-领取按钮" data-url="/pages/customer/birthday_present">领取生日礼物</view>
                            </view>
                        </view>
                    </view>
                    <image catchtap="closeActivityWindow" class="iconfont icon-close" data-element_name="生日弹出框-关闭按钮" src="https://wap.exijiu.com/Public/MemberClub/images/closed.png"></image>
                </view>
            </view>
            <view catchtap="closeActivityWindow" class="alert_mask" data-element_name="生日弹出框-蒙层关闭"></view>
        </view>
        <view wx:if="{{birthdayUpgradeShow}}">
            <view class="pop-alert-box dialog">
                <view class="alert-content-box" style="height:1168rpx;width:750rpx;position:relative;">
                    <view class="img" style="height:100%;width:100%;">
                        <image bindtap="onBirthdayUpgradeSure" mode="aspectFit" src="https://wap.exijiu.com/Public/MemberClub/images/prize-upgrade.png" style="height:100%;width:100%;"></image>
                    </view>
                    <view bindtap="onBirthdayUpgradeClose" class="close" style="width:60rpx;height:60rpx;position:absolute;top:170rpx;right:40rpx;">
                        <image src="https://wap.exijiu.com/Public/MemberClub/images/closed.png" style="height:100%;width:100%;"></image>
                    </view>
                </view>
            </view>
            <view catchtap="" class="alert_mask"></view>
        </view>
        <view hidden="{{alertBoxBannersIsHidden}}">
            <view style="width:100%;height:100%;">
                <view class="pop-alert-box dialog">
                    <view class="alert-content-box" style="height:70%;width:100%;">
                        <swiper autoplay interval="3000" style="height:100%;width:100%;">
                            <swiper-item bindtap="gotoWithLogined" data-element_name="弹出框-{{item.name}}" data-event="{{item.event_id}}" data-url="{{item.out_site_link}}" wx:for="{{alertBoxBanners}}" wx:key="id">
                                <image mode="aspectFit" src="{{item.pic_url}}" style="height:100%;width:100%;"></image>
                            </swiper-item>
                        </swiper>
                    </view>
                    <image catchtap="closeAlertBoxBanners" class="iconfont icon-close" data-element_name="弹出框-关闭按钮" src="https://wap.exijiu.com/Public/MemberClub/images/closed.png"></image>
                </view>
            </view>
            <view catchtap="closeAlertBoxBanners" class="alert_mask" data-element_name="弹出框-蒙层关闭"></view>
        </view>
        <view class="b1" hidden="{{flag}}" wx:if="{{page.list.length>0}}">
            <view class="t_w">
                <view class="t_image">
                    <image class="t_image1" src="http://wap.exijiu.com/Public//Miniprogram/images/msg-bell.png"></image>
                </view>
                <view class="b2">
                    <block wx:if="{{index==0}}" wx:for="{{page.list}}">
                        <view class="v1" wx:if="{{item.isread==0&&item.consumerUserId!=null&&item.topicId!=item.pcommentId}}">
                            <image mode="aspectFill" src="{{item.url}}" style="width:50rpx;height:50rpx;padding-right:10rpx;"></image>{{item.createName}}回复了你的评论:</view>
                        <view class="v1" wx:if="{{item.isread==0&&item.consumerUserId!=null&&item.topicId!=item.pcommentId&&item.createUserId==userInfo.id}}">
                            <image mode="aspectFill" src="{{item.url}}" style="width:50rpx;height:50rpx;padding-right:10rpx;"></image>{{item.createName}}回复了<image mode="aspectFill" src="{{item.consumerUrl}}" style="width:50rpx;height:50rpx;padding-right:10rpx;"></image>{{item.consumerName}}的评论:</view>
                        <view class="v1" wx:if="{{item.isread==0&&item.consumerUserId!=null&&item.topicId==item.pcommentId}}">
                            <image mode="aspectFill" src="{{item.url}}" style="width:50rpx;height:50rpx;padding-right:10rpx;"></image>{{item.createName}}评论了你的帖子:</view>
                        <view class="v11" wx:if="{{item.isread==0&&item.consumerUserId!=null}}">
                            <span>{{item.content}}<em bindtap="showReplay" style="color:#3cc51f;margin-left:10rpx;font-size:28rpx;">立即回复</em>
                            </span>
                        </view>
                        <view style="width:100%;height:100rpx;display:flex;flex-direction:row;align-items:center;position:relative;" wx:if="{{showReplay}}">
                            <textarea bindinput="bindinput" focus="{{focus}}" name="content" placeholder="回复：{{item.createName}}" placeholderStyle="color:#999;" style="width:65%;height:80rpx;border:1px solid #9ce693;margin-left:5%;position:absolute;"></textarea>
                            <view style="width:30%;display:flex;align-items:center;justify-content:center;position:absolute;right:0rpx;">
                                <view bindtap="replay" data-wpyreplay-a="{{item.pcommentId}}" data-wpyreplay-b="{{item.topicId}}" data-wpyreplay-c="{{item.createName}}" data-wpyreplay-d="{{item.commentId}}" style="width:80%;height:60rpx;background:#3cc51f;display:flex;align-items:center;justify-content:center;color:#fff;border-radius:10rpx;">发送</view>
                            </view>
                        </view>
                    </block>
                    <view bindtap="b" class="btn2">
                        <span bindtap="closeMsg" class="p1">暂时不看</span>
                        <span bindtap="goMymessage" class="p2" data-wpygomymessage-a="{{topicid}}" data-wpygomymessage-b="{{commentId}}">前往查看</span>
                    </view>
                </view>
            </view>
        </view>
        <view wx:if="{{showPrivacy}}">
            <view class="pop-alert-box dialog">
                <view class="alert-content-box">
                    <view class="privacy-popbox">
                        <view class="ttl">温馨提示</view>
                        <view class="cont">在您使用【习酒会员俱乐部】服务之前，请仔细阅读<text bindtap="handleOpenPrivacyContract" style="color:green;">《习酒会员俱乐部隐私保护指引》</text>，如您同意请点击“同意”开始使用。</view>
                        <view class="btn-box">
                            <button catchtap="handleDisAgreePrivacyAuthorization" class="btn dis">不同意</button>
                            <button bindagreeprivacyauthorization="handleAgreePrivacyAuthorization" class="btn" openType="agreePrivacyAuthorization">同意</button>
                        </view>
                    </view>
                </view>
            </view>
            <view class="alert_mask"></view>
        </view>
    </view>
</view>
