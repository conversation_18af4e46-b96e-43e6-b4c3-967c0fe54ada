Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),n=(o(require("./../../api/auth.js")),o(require("./../../api/goods.js")),o(require("./../../mixins/base.js"))),r=o(require("./../../components/common/loading.js"));o(require("./../../components/goods/detail_list.js"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(o){function u(){var e,t,o;i(this,u);for(var f=arguments.length,s=Array(f),c=0;c<f;c++)s[c]=arguments[c];return t=o=a(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(s))),o.data={init:!1,page:{list:[]},commentInit:!1,isShow:!0},o.methods={previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})}},o.computed={},o.$repeat={},o.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},o.$events={},o.components={Loading:r.default},o.mixins=[n.default],o.config={navigationBarTitleText:"防伪查询流程"},a(o,t)}var f,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.page),e(u,[{key:"onLoad",value:(f=regeneratorRuntime.mark((function e(n){return n.goodsId,regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.default.setNavigationBarColor({backgroundColor:"#299192",frontColor:"#ffffff"}),this.loaded();case 2:case"end":return e.stop()}}),e,this)})),s=function(){var e=f.apply(this,arguments);return new Promise((function(t,n){return function r(o,i){try{var a=e[o](i),u=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(u)}("next")}))},function(e){return s.apply(this,arguments)})},{key:"onUnload",value:function(){t.default.setNavigationBarColor({backgroundColor:"#299192",frontColor:"#ffffff"})}}]),u}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(u,"pages/anticounterfeiting/process"));