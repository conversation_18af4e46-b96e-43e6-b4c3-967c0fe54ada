Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,o){for(var t=0;t<o.length;t++){var r=o[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(o,t,r){return t&&e(o.prototype,t),r&&e(o,r),o}}(),o=r(require("./base.js")),t=r(require("./../utils/Page.js"));function r(e){return e&&e.__esModule?e:{default:e}}function n(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}function s(e,o){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!o||"object"!=typeof o&&"function"!=typeof o?e:o}var i=function(r){function i(){return n(this,i),s(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function, not "+typeof o);e.prototype=Object.create(o&&o.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(e,o):e.__proto__=o)}(i,o.default),e(i,null,[{key:"page",value:function(){var e=this.baseUrl+"/visit_goods_log";return new t.default(e,this._processFavGoods.bind(this))}},{key:"remove",value:function(e){var o=this.baseUrl+"/visit_goods_log?goodsId="+e;return this.delete(o)}},{key:"_processFavGoods",value:function(e){return{goodsId:e.goodsId,goodsName:e.goods.name,goodsPrice:e.goods.sellPrice.toFixed(2),imageUrl:this._processGoodsPreview(e.goods.images)}}},{key:"_processGoodsPreview",value:function(e){return null==e||e.length<1||null==e[0].url?"/images/goods/broken.png":e[0].url+"/small"}}]),i}();exports.default=i;