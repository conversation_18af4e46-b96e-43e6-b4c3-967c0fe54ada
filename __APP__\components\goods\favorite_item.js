Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=o(require("./../../npm/wepy/lib/wepy.js")),e=o(require("./../../utils/Tips.js"));function o(t){return t&&t.__esModule?t:{default:t}}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var i=function(o){function i(){var t,o,a;n(this,i);for(var s=arguments.length,u=Array(s),c=0;c<s;c++)u[c]=arguments[c];return o=a=r(this,(t=i.__proto__||Object.getPrototypeOf(i)).call.apply(t,[this].concat(u))),a.props={goods:{}},a.methods={detail:function(t){this.$root.$navigate("/pages/goods/detail?goodsId="+t)},tap:function(t){var o=this;e.default.actionWithFunc(["查看商品","删除记录"],(function(){o.$root.$navigate("/pages/goods/detail?goodsId="+t)}),(function(){o.$emit("remove",t)}))}},r(a,o)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,t.default.component),i}();exports.default=i;