/**
 * 微信小程序协议测试脚本
 * 使用实际的Token进行API调用测试
 */

const https = require('https');
const http = require('http');

class WxProtocolTester {
    constructor() {
        // 你提供的实际Token
        this.authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // API基础配置
        this.baseUrl = 'http://library.hankin.ufutx.cn/api';
        this.appId = 'wx8a8b2ad2b9b9e1d5';
        
        // 解析Token信息
        this.memberInfo = this.decodeJWT(this.authToken);
        this.loginInfo = this.decodeJWT(this.loginCode);
        
        console.log('Token信息解析完成:');
        console.log('会员ID:', this.memberInfo.payload.memberInfo.id);
        console.log('UnionID:', this.loginInfo.payload.unionid);
        console.log('Authorization过期时间:', new Date(this.memberInfo.payload.expireTime * 1000).toLocaleString('zh-CN'));
    }

    /**
     * 解码JWT Token
     */
    decodeJWT(token) {
        try {
            const parts = token.split('.');
            const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            return { header, payload };
        } catch (e) {
            console.error('JWT解码失败:', e.message);
            return null;
        }
    }

    /**
     * 构建微信小程序请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'Referer': `https://servicewechat.com/${this.appId}/devtools/page-frame.html`,
            'X-Requested-With': 'XMLHttpRequest',
            'Authorization': this.authToken,
            'login_code': this.loginCode,
            'X-WX-AppId': this.appId,
            'X-WX-Version': 'v3.2.6'
        };
    }

    /**
     * 发起HTTP请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.baseUrl);
            const isHttps = url.protocol === 'https:';
            const client = isHttps ? https : http;
            
            const options = {
                hostname: url.hostname,
                port: url.port || (isHttps ? 443 : 80),
                path: url.pathname + url.search,
                method: method,
                headers: this.buildHeaders()
            };

            console.log(`\n=== 发起${method}请求 ===`);
            console.log('URL:', url.toString());
            console.log('Headers:', JSON.stringify(options.headers, null, 2));

            const req = client.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    console.log('响应状态码:', res.statusCode);
                    console.log('响应头:', JSON.stringify(res.headers, null, 2));
                    
                    try {
                        const jsonData = JSON.parse(responseData);
                        console.log('响应数据:', JSON.stringify(jsonData, null, 2));
                        resolve({
                            statusCode: res.statusCode,
                            headers: res.headers,
                            data: jsonData
                        });
                    } catch (e) {
                        console.log('原始响应:', responseData);
                        resolve({
                            statusCode: res.statusCode,
                            headers: res.headers,
                            data: responseData
                        });
                    }
                });
            });

            req.on('error', (error) => {
                console.error('请求错误:', error);
                reject(error);
            });

            // 发送POST数据
            if (data && method === 'POST') {
                const postData = JSON.stringify(data);
                console.log('POST数据:', postData);
                req.write(postData);
            }

            req.end();
        });
    }

    /**
     * 测试获取用户信息
     */
    async testGetUserInfo() {
        console.log('\n🔍 测试获取用户信息...');
        try {
            const response = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            return response;
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    /**
     * 测试获取土地信息
     */
    async testGetSoilInfo() {
        console.log('\n🌱 测试获取土地信息...');
        try {
            const response = await this.makeRequest('/garden/sorghum/index');
            return response;
        } catch (error) {
            console.error('获取土地信息失败:', error);
            return null;
        }
    }

    /**
     * 测试每日签到
     */
    async testDailySign() {
        console.log('\n📅 测试每日签到...');
        try {
            const response = await this.makeRequest('/garden/sign/dailySign', 'POST');
            return response;
        } catch (error) {
            console.error('每日签到失败:', error);
            return null;
        }
    }

    /**
     * 测试浇水功能
     */
    async testWatering(soilId) {
        console.log(`\n💧 测试浇水功能 (土地ID: ${soilId})...`);
        try {
            const response = await this.makeRequest('/garden/sorghum/watering', 'POST', { id: soilId });
            return response;
        } catch (error) {
            console.error('浇水失败:', error);
            return null;
        }
    }

    /**
     * 运行完整测试
     */
    async runFullTest() {
        console.log('🚀 开始微信小程序协议测试...\n');
        
        // 测试1: 获取用户信息
        const userInfo = await this.testGetUserInfo();
        
        // 测试2: 获取土地信息
        const soilInfo = await this.testGetSoilInfo();
        
        // 测试3: 每日签到
        const signResult = await this.testDailySign();
        
        // 测试4: 如果有土地信息，尝试浇水
        if (soilInfo && soilInfo.data && Array.isArray(soilInfo.data) && soilInfo.data.length > 0) {
            const firstSoil = soilInfo.data[0];
            if (firstSoil.id) {
                await this.testWatering(firstSoil.id);
            }
        }
        
        console.log('\n✅ 测试完成！');
        
        // 返回测试结果摘要
        return {
            userInfo: userInfo?.data,
            soilInfo: soilInfo?.data,
            signResult: signResult?.data,
            authToken: this.authToken,
            loginCode: this.loginCode,
            memberInfo: this.memberInfo.payload
        };
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const tester = new WxProtocolTester();
    tester.runFullTest().then(results => {
        console.log('\n📊 测试结果摘要:');
        console.log(JSON.stringify(results, null, 2));
    }).catch(error => {
        console.error('测试失败:', error);
    });
}

module.exports = WxProtocolTester;
