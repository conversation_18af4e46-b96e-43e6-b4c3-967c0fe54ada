/**
 * 集成会话保活的自动种植脚本
 * 无需手动进入种植页面，自动维持会话状态
 */

const WxSessionKeeper = require('./session_keeper.js');

class AutoPlantWithSession extends WxSessionKeeper {
    constructor() {
        super();
        
        // 种植任务状态
        this.isPlantingActive = false;
        this.plantingInterval = null;
        this.lastPlantingTime = null;
        
        console.log('🌱 集成自动种植系统初始化完成');
    }

    /**
     * 浇水
     */
    async watering(soilId) {
        try {
            await this.makeRequest('/garden/sorghum/watering', 'POST', { id: soilId });
            console.log(`💧 土地${soilId} 浇水成功`);
            return true;
        } catch (error) {
            console.log(`💧 土地${soilId} 浇水失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 施肥
     */
    async fertilize(soilId) {
        try {
            await this.makeRequest('/garden/sorghum/manuring', 'POST', { id: soilId });
            console.log(`🌿 土地${soilId} 施肥成功`);
            return true;
        } catch (error) {
            console.log(`🌿 土地${soilId} 施肥失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 收获
     */
    async harvest(soilId) {
        try {
            const result = await this.makeRequest('/garden/sorghum/harvest', 'POST', { id: soilId });
            console.log(`🎉 土地${soilId} 收获成功`);
            return result;
        } catch (error) {
            console.log(`🎉 土地${soilId} 收获失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 种植
     */
    async plant(soilId, cropType = 1) {
        try {
            await this.makeRequest('/garden/sorghum/seed', 'POST', { id: soilId, type: cropType });
            console.log(`🌱 土地${soilId} 种植成功`);
            return true;
        } catch (error) {
            console.log(`🌱 土地${soilId} 种植失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 处理单块土地
     */
    async processSoil(soil) {
        const { id, status } = soil;
        
        switch (status) {
            case 0: // 空地，可以种植
                await this.plant(id);
                break;
                
            case 2: // 成熟，可以收获
                await this.harvest(id);
                await this.sleep(500);
                await this.plant(id); // 收获后重新种植
                break;
                
            case 10: // 需要浇水
            case 11: // 需要施肥
                await this.watering(id);
                await this.sleep(500);
                await this.fertilize(id);
                break;
                
            default:
                console.log(`🌱 土地${id} 状态${status} 无需操作`);
        }
    }

    /**
     * 执行种植任务
     */
    async performPlantingTasks() {
        console.log('\n🚀 开始执行种植任务...');
        
        try {
            // 1. 确保会话活跃
            if (!this.isSessionActive) {
                console.log('⚠️ 会话未激活，先激活会话...');
                await this.simulatePageShow();
            }

            // 2. 获取最新土地信息
            const soilData = await this.getSoilList();
            if (!soilData || !soilData.data || !Array.isArray(soilData.data)) {
                console.log('❌ 没有土地信息，跳过种植任务');
                console.log('土地数据:', soilData);
                return;
            }

            const soilList = soilData.data;
            console.log(`🌱 找到 ${soilList.length} 块土地`);

            // 3. 每日签到
            await this.dailySign();

            // 4. 处理每块土地
            for (const soil of soilList) {
                await this.processSoil(soil);
                await this.sleep(1000); // 避免请求过快
            }

            this.lastPlantingTime = Date.now();
            console.log('✅ 种植任务执行完成\n');

        } catch (error) {
            console.error('❌ 种植任务执行失败:', error.message);
        }
    }

    /**
     * 启动完整的自动化系统
     */
    async startFullAutomation(sessionIntervalMinutes = 15, plantingIntervalMinutes = 30) {
        console.log('\n🚀 启动完整自动化系统...');
        console.log(`📱 会话保活间隔: ${sessionIntervalMinutes}分钟`);
        console.log(`🌱 种植任务间隔: ${plantingIntervalMinutes}分钟`);
        
        // 1. 启动会话保活
        const sessionSuccess = await this.startSessionKeeping(sessionIntervalMinutes);
        if (!sessionSuccess) {
            console.log('❌ 会话保活启动失败，无法启动自动化系统');
            return false;
        }
        
        // 2. 立即执行一次种植任务
        await this.performPlantingTasks();
        
        // 3. 启动定时种植任务
        this.isPlantingActive = true;
        this.plantingInterval = setInterval(async () => {
            await this.performPlantingTasks();
        }, plantingIntervalMinutes * 60 * 1000);
        
        console.log('✅ 完整自动化系统启动成功！');
        console.log('💡 系统将自动维持会话并执行种植任务');
        console.log('🔄 无需手动进入种植页面');
        
        return true;
    }

    /**
     * 停止完整的自动化系统
     */
    stopFullAutomation() {
        // 停止种植任务
        if (this.plantingInterval) {
            clearInterval(this.plantingInterval);
            this.plantingInterval = null;
        }
        this.isPlantingActive = false;
        
        // 停止会话保活
        this.stopSessionKeeping();
        
        console.log('🛑 完整自动化系统已停止');
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        const sessionStatus = this.getSessionStatus();
        
        return {
            ...sessionStatus,
            planting: {
                isActive: this.isPlantingActive,
                lastPlantingTime: this.lastPlantingTime ? new Date(this.lastPlantingTime).toLocaleString('zh-CN') : null
            }
        };
    }

    /**
     * 延时函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出类
module.exports = AutoPlantWithSession;

// 如果直接运行此文件
if (require.main === module) {
    const autoPlant = new AutoPlantWithSession();
    
    console.log('🌱 微信小程序自动种植系统');
    console.log('🔧 集成会话保活功能，无需手动进入页面');
    console.log('');
    
    // 启动完整自动化系统
    autoPlant.startFullAutomation(15, 30).then(success => {
        if (success) {
            console.log('\n🎉 自动化系统启动成功！');
            console.log('');
            console.log('📊 系统功能:');
            console.log('  ✅ 自动维持会话状态');
            console.log('  ✅ 自动每日签到');
            console.log('  ✅ 自动浇水施肥');
            console.log('  ✅ 自动收获种植');
            console.log('  ✅ 无需手动进入页面');
            console.log('');
            console.log('⚠️  注意: 保持此程序运行以维持自动化');
            console.log('🛑 按 Ctrl+C 停止系统');
            
            // 定期显示状态
            setInterval(() => {
                const status = autoPlant.getSystemStatus();
                console.log('\n📊 系统状态:');
                console.log(`  会话状态: ${status.isActive ? '✅ 活跃' : '❌ 未激活'}`);
                console.log(`  种植状态: ${status.planting.isActive ? '✅ 运行中' : '❌ 已停止'}`);
                console.log(`  最后心跳: ${status.lastHeartbeat || '无'}`);
                console.log(`  最后种植: ${status.planting.lastPlantingTime || '无'}`);
            }, 5 * 60 * 1000); // 每5分钟显示一次状态
            
            // 处理退出信号
            process.on('SIGINT', () => {
                console.log('\n🛑 收到退出信号，正在停止自动化系统...');
                autoPlant.stopFullAutomation();
                console.log('✅ 系统已安全停止');
                process.exit(0);
            });
            
        } else {
            console.log('❌ 自动化系统启动失败');
            process.exit(1);
        }
    });
}
