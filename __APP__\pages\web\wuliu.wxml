<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view class="container" wx:if="{{init}}">
    <view class="detail-box" style="background:#56a2cf">
        <view bindtap="boxCode" class="detail-title row-center">
            <text class="primary lg" style="height:100rpx;font-size:14px;line-height:100rpx;color:#fff;">箱码：{{detail.boxCode}}</text>
        </view>
    </view>
    <view class="goods-info-box column" wx:if="{{productInfo.codeType!='boxCode'}}">
        <view class="row mt10">
            <text class="goodsinfo">防伪码：{{detail.productCode}}</text>
        </view>
    </view>
    <view class="goods-info-box column" wx:else>
        <view class="row mt10">
            <text class="goodsinfo">防伪码：空</text>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb" wx:for="{{detail.logisticsTracking}}" wx:for-index="key" wx:key="id">
        <view bindtap="xfaHuodanhao" class="tr bg-w" id="{{key}}">
            <view class="title">发货单号</view>
            <view class="value" style="color:#21b327">{{item.billNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">产品名称</view>
            <view class="value">{{item.productName}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">下单日期</view>
            <view class="value">{{item.orderDate}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">发货时间</view>
            <view class="value">{{item.scanTime}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">源头</view>
            <view class="value">{{item.consigner}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">发往</view>
            <view class="value">{{item.receiver}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">明细</view>
            <view class="value">{{item.receiverAddress}}</view>
        </view>
    </view>
</view>
