var t;Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=o(require("./../../npm/wepy/lib/wepy.js")),s=require("./../../npm/wepy-redux/lib/index.js"),i=o(require("./../../store/utils.js")),r=(o(require("./../../utils/WxUtils.js")),o(require("./../../utils/Tips.js")));function o(t){return t&&t.__esModule?t:{default:t}}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var a=(0,s.connect)({shop:i.default.get("shop"),status:i.default.get("status"),version:i.default.get("version")})(t=function(t){function s(){var t,i,o;n(this,s);for(var a=arguments.length,l=Array(a),p=0;p<a;p++)l[p]=arguments[p];return i=o=u(this,(t=s.__proto__||Object.getPrototypeOf(s)).call.apply(t,[this].concat(l))),o.props={mode:{}},o.methods={goto:function(){this.$root.$switch("/pages/customer/index_template")}},o.computed={title:function(){if(null!=this.shop&&null!=this.shop.type&&null!=this.version){var t=this.shop.type;if(null==this.status)return"休息中";if(0==this.status.open)return this.init||r.default.modal(this.status.closeTips),this.status.closeTips;if("1"==this.mode){var s=this.version.isOrder?t.name:t.basicName;return e.default.setNavigationBarTitle({title:s}),s}return"2"==this.mode?(e.default.setNavigationBarTitle({title:"堂食点餐"}),"点堂食/外带"):void 0}},badgeText:function(){if(null!=this.shop&&null!=this.shop.type&&null!=this.version){var t=this.shop.type,e=this.version.isOrder?t.badgeText:t.basicBadgeText;return 2==this.mode?"堂食":e}}},u(o,i)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(s,e.default.component),s}())||t;exports.default=a;