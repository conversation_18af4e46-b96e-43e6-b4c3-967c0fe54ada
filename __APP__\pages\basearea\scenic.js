Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),n=require("./../../npm/wepy/lib/wepy.js"),r=(e=n)&&e.__esModule?e:{default:e};function a(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(a,o){try{var i=t[a](o),u=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(u)}("next")}))}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=null,s=function(e){function n(){var e,t,r;o(this,n);for(var s=arguments.length,c=Array(s),l=0;l<s;l++)c[l]=arguments[l];return t=r=i(this,(e=n.__proto__||Object.getPrototypeOf(n)).call.apply(e,[this].concat(c))),r.data={isShowMore:!1,indicatorDots:!1,autoplay:!0,interval:2e3,audio:{isPlaying:!1,currentTime:0,duration:0,isWarting:!1,currentTimeExp:"00:00",durationExp:"00:00"},zoneId:31398,info:{title:"",desc:"",positionName:"",bannerList:[]}},r.methods={showMoreChange:function(){var e=this;return a(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.isShowMore=!e.isShowMore,e.$apply();case 2:case"end":return t.stop()}}),t,e)})))()},payClick:function(){this.data.audio.isPlaying?u.pause():u.play(),this.setData({"audio.isPlaying":!this.data.audio.isPlaying})},sliderChange:function(e){var t=e.detail.value,n=u.duration*t/100;u.seek(n),this.data.audio.isPlaying&&(console.log("sliderChange"),setTimeout((function(){u.play()}),500))},sliderChanging:function(){console.log("sliderChanging"),u.pause()}},r.config={},r.components={},i(r,t)}var s,c,l,p,f,d;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,r.default.page),t(n,[{key:"onLoad",value:(d=a(regeneratorRuntime.mark((function e(t){var n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this,u=wx.createInnerAudioContext(),e.next=4,this.getDetail();case 4:u.onPlay((function(){console.info("开始播放")})),u.onTimeUpdate((function(){console.info(u.currentTime,u.duration),n.setData({"audio.currentTime":u.currentTime,"audio.duration":u.duration,"audio.percent":u.currentTime/u.duration*100,"audio.currentTimeExp":r.timeFormat(u.currentTime),"audio.durationExp":r.timeFormat(u.duration)})})),u.onWaiting((function(){console.log("waiting"),u.pause()})),u.onCanplay((function(){console.log("canplay"),u.duration,setTimeout((function(){n.setData({"audio.durationExp":r.timeFormat(u.duration)})}),2e3),setTimeout((function(){r.firstPlay()}),3e3)})),u.onStop((function(){console.info("停止播放")})),u.onEnded((function(){console.info("结束播放"),u.stop(),n.setData({"audio.isPlaying":!1})})),u.onError((function(e){console.log(e.errMsg),console.log(e.errCode)})),this.$apply();case 12:case"end":return e.stop()}}),e,this)}))),function(e){return d.apply(this,arguments)})},{key:"onShow",value:(f=a(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"onHide",value:(p=a(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:u.stop(),u.pause();case 2:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"onUnload",value:(l=a(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:u.stop(),u.pause();case 2:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"timeFormat",value:function(e){var t=parseInt(e/3600);e%=3600;var n=parseInt(e/60),r=parseInt(e%60);return t>0?this.timeFormatZero(t+":"+n+":"+r):this.timeFormatZero(n+":"+r)}},{key:"timeFormatZero",value:function(e){for(var t="",n=e.split(":"),r=0;r<n.length-1;r++)t+=1==n[r].length?"0"+n[r]:n[r],t+=":";return t+=1==n[r].length?"0"+n[r]:n[r]}},{key:"firstPlay",value:(c=a(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=this).data.audio.currentTime<=0&&!t.data.audio.isPlaying&&(u.play(),t.setData({"audio.isPlaying":!0})),this.$apply();case 3:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"getDetail",value:(s=a(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this,wx.request({url:"https://zhdl-xcx.gzxijiu.com/coolfar/app/appRequest",data:{action:"getPOIStoryList",client:{},data:{zoneId:this.zoneId}},method:"POST",success:function(e){t.setData({"info.title":e.data.data[0].title,"info.desc":e.data.data[0].storyContent,"info.positionName":e.data.data[0].title,"info.bannerList":e.data.data[0].storyImageList}),u.src=e.data.data[0].storyPath}}),this.$apply();case 3:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})}]),n}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(s,"pages/basearea/scenic"));