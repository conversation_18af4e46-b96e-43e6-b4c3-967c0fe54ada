Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../../npm/wepy/lib/wepy.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function r(){var e,t,n;o(this,r);for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];return t=n=u(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(a))),n.data={SubScrubeStatus:!1},u(n,t)}var i,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default.page),t(r,[{key:"onLoad",value:(i=regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)})),a=function(){var e=i.apply(this,arguments);return new Promise((function(t,r){return function n(o,u){try{var i=e[o](u),a=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(a)}("next")}))},function(e){return a.apply(this,arguments)})},{key:"subScrube",value:function(e){wx.requestSubscribeMessage({tmplIds:["aAYvteh8Y7_mH2WKHSqtT9DbDgQJ8RI_Z_8h6QeGds8","94McE9GuX618CX2VLVLe8_Ts0-ckWmpPGS9Tliiv9SE","GIKte3k3HaeNHcFGFY3Undm_xHARGN_m8EFvdpJX0Wo"],success:function(e){console.log(e),wx.navigateBack({})},fail:function(e){this.SubScrubeStatus=!1,20004!=e.errCode?wx.showToast({title:"订阅失败:"+e.errCode,icon:"error",duration:2e3}):wx.showModal({title:"通知权限未开启",content:"您关闭了习酒小程序的通知功能，请点击右上角“···”，前往“设置→订阅消息→通知管理”并打开此功能。",showCancel:!1,confirmText:"好的",success:function(e){e.confirm&&console.log("用户点击了确定")}})}})}}]),r}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(i,"pages/activity/subscribemsg"));