Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,o,r){return o&&t(e.prototype,o),r&&t(e,r),e}}(),o=require("./../../npm/wepy/lib/wepy.js"),r=(t=o)&&t.__esModule?t:{default:t};function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var c=require("./../../utils/qrcode.js"),a=function(t){function o(){var t,e,r;i(this,o);for(var a=arguments.length,l=Array(a),h=0;h<a;h++)l[h]=arguments[h];return e=r=n(this,(t=o.__proto__||Object.getPrototypeOf(o)).call.apply(t,[this].concat(l))),r.props={width:{type:Number,default:150,twoWay:!0},height:{type:Number,default:150,twoWay:!0},colorDark:{type:String,default:"#000",twoWay:!0},colorLight:{type:String,default:"#fff",twoWay:!0},text:{type:String,default:"",twoWay:!0}},r.watch={text:function(t){this.text=t,new c("canvas",{text:this.text,width:this.width,height:this.width,colorDark:this.colorDark,colorLight:this.colorLight,correctLevel:c.CorrectLevel.H})},width:function(t){this.width=t,new c("canvas",{text:this.text,width:this.width,height:this.width,colorDark:this.colorDark,colorLight:this.colorLight,correctLevel:c.CorrectLevel.H})}},n(r,e)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(o,r.default.component),e(o,[{key:"checkqrcode",value:function(){}},{key:"onLoad",value:function(){new c("canvas",{text:this.text,width:this.width,height:this.height,colorDark:this.colorDark,colorLight:this.colorLight,correctLevel:c.CorrectLevel.H})}}]),o}();exports.default=a;