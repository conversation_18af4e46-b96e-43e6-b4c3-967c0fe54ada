Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../../npm/wepy/lib/wepy.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function r(){var e,t,u;o(this,r);for(var a=arguments.length,c=Array(a),f=0;f<a;f++)c[f]=arguments[f];return t=u=i(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(c))),u.props={detail:{}},u.methods={previewDetailImages:function(e){var t=this.detail.goodsDetails.filter((function(e){return 2==e.type})).map((function(e){return e.content}));n.default.previewImage({urls:t,current:e})}},i(u,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default.component),t(r,[{key:"onLoad",value:function(){}}]),r}();exports.default=u;