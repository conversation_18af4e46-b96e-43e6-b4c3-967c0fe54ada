<view>
    <view class="container">
        <view class="bannerBox" wx:if="{{info.bannerList}}">
            <swiper autoplay="{{autoplay}}" class="swiperBox" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
                <swiper-item wx:for="{{info.bannerList}}" wx:key="index">
                    <view class="swiper-item">
                        <image mode="aspectFill" src="{{item}}"></image>
                    </view>
                </swiper-item>
            </swiper>
        </view>
        <view class="position">
            <image mode="aspectFill" src="/images/basearea/book.png"></image>
            <text>当前位置：{{info.positionName}}</text>
        </view>
        <view class="content {{isShowMore?'showMore':''}}">
            <view class="title">{{info.title}}</view>
            <view class="desc">{{info.desc}}</view>
            <view bindtap="showMoreChange" class="more" wx:if="{{info.desc}}">
                <text>{{isShowMore?'收起':'更多简介'}}</text>
                <image mode="aspectFill" src="{{isShowMore?'/images/basearea/more-up.png':'/images/basearea/more-down.png'}}"></image>
            </view>
        </view>
        <view class="audio-box">
            <view bindtap="payClick" class="audio-left">
                <image id="audioPlayer" mode="aspectFill" src="/images/basearea/stop.png" wx:if="{{audio.isPlaying}}"></image>
                <image id="audioPlayer" mode="aspectFill" src="/images/basearea/play.png" wx:else></image>
            </view>
            <view class="audio-length-current" id="audioCurTime">{{audio.currentTimeExp}}</view>
            <view class="audio-right">
                <slider activeColor="#ffffff" backgroundColor="#6a6969" bindchange="sliderChange" bindchanging="sliderChanging" blockSize="16" value="{{audio.percent}}"></slider>
            </view>
            <view class="audio-length-total">{{audio.durationExp}}</view>
        </view>
    </view>
</view>
