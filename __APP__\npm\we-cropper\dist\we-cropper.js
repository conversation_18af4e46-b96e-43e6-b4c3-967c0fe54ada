!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.WeCropper=e()}(this,(function(){"use strict";var t=void 0,e=["touchstarted","touchmoved","touchended"],n={strokeStyle:"setStrokeStyle",fillStyle:"setFillStyle",lineWidth:"setLineWidth"};function o(t){for(var n=[],o=arguments.length-1;o-- >0;)n[o]=arguments[o+1];e.forEach((function(e,o){void 0!==n[o]&&(t[e]=n[o])}))}function r(){return t||(t=wx.getSystemInfoSync()),t}function i(t,e,o){"2d"===t.type?t.ctx[e]=o:t.ctx[n[e]](o)}var a={},c={id:{default:"cropper",get:function(){return a.id},set:function(t){"string"!=typeof t&&console.error("id："+t+" is invalid"),a.id=t}},width:{default:750,get:function(){return a.width},set:function(t){"number"!=typeof t&&console.error("width："+t+" is invalid"),a.width=t}},height:{default:750,get:function(){return a.height},set:function(t){"number"!=typeof t&&console.error("height："+t+" is invalid"),a.height=t}},pixelRatio:{default:r().pixelRatio,get:function(){return a.pixelRatio},set:function(t){"number"!=typeof t&&console.error("pixelRatio："+t+" is invalid"),a.pixelRatio=t}},scale:{default:2.5,get:function(){return a.scale},set:function(t){"number"!=typeof t&&console.error("scale："+t+" is invalid"),a.scale=t}},zoom:{default:5,get:function(){return a.zoom},set:function(t){"number"!=typeof t?console.error("zoom："+t+" is invalid"):(t<0||t>10)&&console.error("zoom should be ranged in 0 ~ 10"),a.zoom=t}},src:{default:"",get:function(){return a.src},set:function(t){"string"!=typeof t&&console.error("src："+t+" is invalid"),a.src=t}},cut:{default:{},get:function(){return a.cut},set:function(t){"object"!=typeof t&&console.error("cut："+t+" is invalid"),a.cut=t}},boundStyle:{default:{},get:function(){return a.boundStyle},set:function(t){"object"!=typeof t&&console.error("boundStyle："+t+" is invalid"),a.boundStyle=t}},onReady:{default:null,get:function(){return a.ready},set:function(t){a.ready=t}},onBeforeImageLoad:{default:null,get:function(){return a.beforeImageLoad},set:function(t){a.beforeImageLoad=t}},onImageLoad:{default:null,get:function(){return a.imageLoad},set:function(t){a.imageLoad=t}},onBeforeDraw:{default:null,get:function(){return a.beforeDraw},set:function(t){a.beforeDraw=t}}},u=r().windowWidth;var s=function(t){return"function"==typeof t},h=["ready","beforeImageLoad","beforeDraw","imageLoad"];function d(t){return function(e){for(var n=[],o=arguments.length-1;o-- >0;)n[o]=arguments[o+1];return void 0===e&&(e={}),new Promise((function(o,r){e.success=function(t){o(t)},e.fail=function(t){r(t)},t.apply(void 0,[e].concat(n))}))}}function f(t,e){return void 0===e&&(e=!1),new Promise((function(n){t.draw&&t.draw(e,n)}))}var l=d(wx.getImageInfo),g=d(wx.canvasToTempFilePath),p="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var v=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t,e){
/*! http://mths.be/base64 v0.1.0 by @mathias | MIT license */
!function(n){var o=e,r=t&&t.exports==o&&t,i="object"==typeof p&&p;i.global!==i&&i.window!==i||(n=i);var a=function(t){this.message=t};(a.prototype=new Error).name="InvalidCharacterError";var c=function(t){throw new a(t)},u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=/[\t\n\f\r ]/g,h={encode:function(t){t=String(t),/[^\0-\xFF]/.test(t)&&c("The string to be encoded contains characters outside of the Latin1 range.");for(var e,n,o,r,i=t.length%3,a="",s=-1,h=t.length-i;++s<h;)e=t.charCodeAt(s)<<16,n=t.charCodeAt(++s)<<8,o=t.charCodeAt(++s),a+=u.charAt((r=e+n+o)>>18&63)+u.charAt(r>>12&63)+u.charAt(r>>6&63)+u.charAt(63&r);return 2==i?(e=t.charCodeAt(s)<<8,n=t.charCodeAt(++s),a+=u.charAt((r=e+n)>>10)+u.charAt(r>>4&63)+u.charAt(r<<2&63)+"="):1==i&&(r=t.charCodeAt(s),a+=u.charAt(r>>2)+u.charAt(r<<4&63)+"=="),a},decode:function(t){var e=(t=String(t).replace(s,"")).length;e%4==0&&(e=(t=t.replace(/==?$/,"")).length),(e%4==1||/[^+a-zA-Z0-9/]/.test(t))&&c("Invalid character: the string to be decoded is not correctly encoded.");for(var n,o,r=0,i="",a=-1;++a<e;)o=u.indexOf(t.charAt(a)),n=r%4?64*n+o:o,r++%4&&(i+=String.fromCharCode(255&n>>(-2*r&6)));return i},version:"0.1.0"};if(o&&!o.nodeType)if(r)r.exports=h;else for(var d in h)h.hasOwnProperty(d)&&(o[d]=h[d]);else n.base64=h}(p)}));function y(t){var e="";if("string"==typeof t)e=t;else for(var n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return v.encode(e)}function x(t,e,n,o,r,i,a){void 0===a&&(a=function(){}),void 0===i&&(i="png"),i=function(t){return"image/"+(t=t.toLowerCase().replace(/jpg/i,"jpeg")).match(/png|jpeg|bmp|gif/)[0]}(i),/bmp/.test(i)?function(t,e,n,o,r,i){wx.canvasGetImageData({canvasId:t,x:e,y:n,width:o,height:r,success:function(t){i(t,null)},fail:function(t){i(null,t)}})}(t,e,n,o,r,(function(t,e){var n=function(t){var e=t.width,n=t.height,o=e*n*3,r=o+54,i=[66,77,255&r,r>>8&255,r>>16&255,r>>24&255,0,0,0,0,54,0,0,0],a=[40,0,0,0,255&e,e>>8&255,e>>16&255,e>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,1,0,24,0,0,0,0,0,255&o,o>>8&255,o>>16&255,o>>24&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=(4-3*e%4)%4,u=t.data,s="",h=e<<2,d=n,f=String.fromCharCode;do{for(var l=h*(d-1),g="",p=0;p<e;p++){var v=p<<2;g+=f(u[l+v+2])+f(u[l+v+1])+f(u[l+v])}for(var x=0;x<c;x++)g+=String.fromCharCode(0);s+=g}while(--d);return y(i.concat(a))+y(s)}(t);s(a)&&a(function(t,e){return"data:"+e+";base64,"+t}(n,"image/"+i),e)})):console.error("暂不支持生成'"+i+"'类型的base64图片")}var m=function(t,e){return void 0===t&&(t={}),void 0===e&&(e=function(){}),x(t.canvasId,t.x,t.y,t.width,t.height,"bmp",e)};var w={touchStart:function(t){var e=t.touches,n=e[0],r=e[1];this.src&&(o(this,!0,null,null),this.__oneTouchStart(n),t.touches.length>=2&&this.__twoTouchStart(n,r))},touchMove:function(t){var e=t.touches,n=e[0],r=e[1];this.src&&(o(this,null,!0),1===t.touches.length&&this.__oneTouchMove(n),t.touches.length>=2&&this.__twoTouchMove(n,r))},touchEnd:function(t){this.src&&(o(this,!1,!1,!0),this.__xtouchEnd())}};var b=function(t){var e,n,o={};return e=this,n=c,Object.defineProperties(e,n),Object.keys(c).forEach((function(t){o[t]=c[t].default})),Object.assign(this,o,t),this.prepare(),this.attachPage(),this.createCtx(),this.observer(),this.cutt(),this.methods(),this.init(),this.update(),this};return b.prototype.init=function(){var t=this.src;return this.version="1.4.0","function"==typeof this.onReady&&this.onReady(this.ctx,this),t?this.pushOrign(t):this.updateCanvas(),o(this,!1,!1,!1),this.oldScale=1,this.newScale=1,this},Object.assign(b.prototype,w),b.prototype.prepare=function(){var t=this;t.attachPage=function(){var e=getCurrentPages(),n=e[e.length-1];Object.defineProperty(n,"wecropper",{get:function(){return console.warn("Instance will not be automatically bound to the page after v1.4.0\n\nPlease use a custom instance name instead\n\nExample: \nthis.mycropper = new WeCropper(options)\n\n// ...\nthis.mycropper.getCropperImage()"),t},configurable:!0})},t.createCtx=function(){var e=t.id,n=t.targetId;e?(t.ctx=t.ctx||wx.createCanvasContext(e),t.targetCtx=t.targetCtx||wx.createCanvasContext(n),"function"!=typeof t.ctx.setStrokeStyle&&(t.type="2d")):console.error("constructor: create canvas context failed, 'id' must be valuable")},t.deviceRadio=u/750},b.prototype.observer=function(){var t=this;t.on=function(e,n){var o;return h.indexOf(e)>-1?s(n)&&("ready"===e?n(t):t["on"+(o=e,o.charAt(0).toUpperCase()+o.slice(1))]=n):console.error("event: "+e+" is invalid"),t}},b.prototype.methods=function(){var t=this,e=t.width,n=t.height,o=t.id,r=t.targetId,i=t.pixelRatio,a=t.cut,c=a.x;void 0===c&&(c=0);var u=a.y;void 0===u&&(u=0);var h=a.width;void 0===h&&(h=e);var d=a.height;void 0===d&&(d=n),t.updateCanvas=function(e){return t.croperTarget&&t.ctx.drawImage(t.croperTarget,t.imgLeft,t.imgTop,t.scaleWidth,t.scaleHeight),s(t.onBeforeDraw)&&t.onBeforeDraw(t.ctx,t),t.setBoundStyle(t.boundStyle),"2d"!==t.type&&t.ctx.draw(!1,e),e&&e(),t},t.pushOrigin=t.pushOrign=function(e){return t.src=e,s(t.onBeforeImageLoad)&&t.onBeforeImageLoad(t.ctx,t),function(t,e){return new Promise((function(n,o){if("2d"===t.type){var r=t.canvas.createImage();r.onload=function(){n(r)},r.onerror=function(t){o(t)},r.src=e}else n(e)}))}(t,e).then((function(n){return t.croperTarget=n,l({src:e}).then((function(e){var n=e.width/e.height;return n<h/d?(t.rectX=c,t.baseWidth=h,t.baseHeight=h/n,t.rectY=u-Math.abs((d-t.baseHeight)/2)):(t.rectY=u,t.baseWidth=d*n,t.baseHeight=d,t.rectX=c-Math.abs((h-t.baseWidth)/2)),t.imgLeft=t.rectX,t.imgTop=t.rectY,t.scaleWidth=t.baseWidth,t.scaleHeight=t.baseHeight,t.update(),new Promise((function(e){t.updateCanvas(e)}))})).then((function(){s(t.onImageLoad)&&t.onImageLoad(t.ctx,t)}))}))},t.removeImage=function(){return t.src="",t.croperTarget="","2d"===t.type?t.ctx.clearRect(0,0,t.canvas.width,t.canvas.height):f(t.ctx)},t.getCropperBase64=function(t){void 0===t&&(t=function(){}),m({canvasId:o,x:c,y:u,width:h,height:d},t)},t.getCropperImage=function(e,n){var a=Object.assign({fileType:"jpg"},e),l=s(e)?e:s(n)?n:null,p={canvasId:o,x:c,y:u,width:h,height:d};"2d"===t.type&&(p.canvas=t.canvas);var v=function(){return Promise.resolve()};return a.original&&(v=function(){return t.targetCtx.drawImage(t.croperTarget,t.imgLeft*i,t.imgTop*i,t.scaleWidth*i,t.scaleHeight*i),p={canvasId:r,x:c*i,y:u*i,width:h*i,height:d*i},f(t.targetCtx)}),v().then((function(){Object.assign(p,a);var t=p.componentContext?[p,p.componentContext]:[p];return g.apply(null,t)})).then((function(e){var n=e.tempFilePath;return l?l.call(t,n,null):n})).catch((function(e){if(!l)throw e;l.call(t,null,e)}))}},b.prototype.cutt=function(){var t=this,e=t.width,n=t.height,o=t.cut,r=o.x;void 0===r&&(r=0);var a=o.y;void 0===a&&(a=0);var c=o.width;void 0===c&&(c=e);var u=o.height;void 0===u&&(u=n),t.outsideBound=function(e,n){t.imgLeft=e>=r?r:t.scaleWidth+e-r<=c?r+c-t.scaleWidth:e,t.imgTop=n>=a?a:t.scaleHeight+n-a<=u?a+u-t.scaleHeight:n},t.setBoundStyle=function(o){void 0===o&&(o={});var s=o.color;void 0===s&&(s="#04b00f");var h=o.mask;void 0===h&&(h="rgba(0, 0, 0, 0.3)");var d=o.lineWidth;void 0===d&&(d=1);var f=d/2,l=[{start:{x:r-f,y:a+10-f},step1:{x:r-f,y:a-f},step2:{x:r+10-f,y:a-f}},{start:{x:r-f,y:a+u-10+f},step1:{x:r-f,y:a+u+f},step2:{x:r+10-f,y:a+u+f}},{start:{x:r+c-10+f,y:a-f},step1:{x:r+c+f,y:a-f},step2:{x:r+c+f,y:a+10-f}},{start:{x:r+c+f,y:a+u-10+f},step1:{x:r+c+f,y:a+u+f},step2:{x:r+c-10+f,y:a+u+f}}];t.ctx.beginPath(),i(t,"fillStyle",h),t.ctx.fillRect(0,0,r,n),t.ctx.fillRect(r,0,c,a),t.ctx.fillRect(r,a+u,c,n-a-u),t.ctx.fillRect(r+c,0,e-r-c,n),t.ctx.fill(),l.forEach((function(e){t.ctx.beginPath(),i(t,"strokeStyle",s),i(t,"lineWidth",d),t.ctx.moveTo(e.start.x,e.start.y),t.ctx.lineTo(e.step1.x,e.step1.y),t.ctx.lineTo(e.step2.x,e.step2.y),t.ctx.stroke()}))}},b.prototype.update=function(){var t=this;t.src&&(t.__oneTouchStart=function(e){t.touchX0=Math.round(e.x),t.touchY0=Math.round(e.y)},t.__oneTouchMove=function(e){var n,o;if(t.touchended)return t.updateCanvas();n=Math.round(e.x-t.touchX0),o=Math.round(e.y-t.touchY0);var r=Math.round(t.rectX+n),i=Math.round(t.rectY+o);t.outsideBound(r,i),t.updateCanvas()},t.__twoTouchStart=function(e,n){var o,r,i;t.touchX1=Math.round(t.rectX+t.scaleWidth/2),t.touchY1=Math.round(t.rectY+t.scaleHeight/2),o=Math.round(n.x-e.x),r=Math.round(n.y-e.y),i=Math.round(Math.sqrt(o*o+r*r)),t.oldDistance=i},t.__twoTouchMove=function(e,n){var o=t.oldScale,r=t.oldDistance,i=t.scale,a=t.zoom;t.newScale=function(t,e,n,o,r){var i,a;return i=Math.round(r.x-o.x),a=Math.round(r.y-o.y),t+.001*n*(Math.round(Math.sqrt(i*i+a*a))-e)}(o,r,a,e,n),t.newScale<=1&&(t.newScale=1),t.newScale>=i&&(t.newScale=i),t.scaleWidth=Math.round(t.newScale*t.baseWidth),t.scaleHeight=Math.round(t.newScale*t.baseHeight);var c=Math.round(t.touchX1-t.scaleWidth/2),u=Math.round(t.touchY1-t.scaleHeight/2);t.outsideBound(c,u),t.updateCanvas()},t.__xtouchEnd=function(){t.oldScale=t.newScale,t.rectX=t.imgLeft,t.rectY=t.imgTop})},b}));