Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),n=require("./../../npm/wepy/lib/wepy.js"),o=(e=n)&&e.__esModule?e:{default:e};function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function n(){var e,t,o;r(this,n);for(var i=arguments.length,u=Array(i),c=0;c<i;c++)u[c]=arguments[c];return t=o=a(this,(e=n.__proto__||Object.getPrototypeOf(n)).call.apply(e,[this].concat(u))),o.props={tab:{twoWay:!0}},o.data={},o.methods={switchTab:function(e){this.$emit("change",e)}},o.events={},a(o,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,o.default.component),t(n,[{key:"onLoad",value:function(){}}]),n}();exports.default=i;