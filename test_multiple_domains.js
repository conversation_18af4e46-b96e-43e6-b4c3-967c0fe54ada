/**
 * 测试多个可能的API域名
 */

const https = require('https');
const http = require('http');

// 忽略SSL证书验证错误
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Token配置
const tokens = {
    authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI',
    loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY'
};

// 生产环境API配置
const possibleAPIs = [
    {
        name: '主要生产环境',
        baseUrl: 'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '论坛API',
        baseUrl: 'https://apiforum.exijiu.com/api',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '微信红包API',
        baseUrl: 'https://wap.exijiu.com/index.php/API',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '积分商城API',
        baseUrl: 'https://apimallwm.exijiu.com/api',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '用户位置API',
        baseUrl: 'https://wap.exijiu.com/index.php/api',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '基地API',
        baseUrl: 'https://zhdl-dl.gzxijiu.com/api',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '统计API',
        baseUrl: 'https://statistics.exijiu.com/api',
        appId: 'wx489f950decfeb93e'
    },
    {
        name: '活动页面API',
        baseUrl: 'https://wap.exijiu.com/Public/active/api',
        appId: 'wx489f950decfeb93e'
    }
];

console.log('🚀 开始测试多个API域名...');

// 构建请求头
function buildHeaders(appId) {
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
        'Authorization': tokens.authorization,
        'login_code': tokens.loginCode,
        'X-WX-AppId': appId
    };
}

// 发起请求
function makeRequest(baseUrl, path, headers) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, baseUrl);
        const isHttps = url.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const options = {
            hostname: url.hostname,
            port: url.port || (isHttps ? 443 : 80),
            path: url.pathname + url.search,
            method: 'GET',
            headers: headers,
            timeout: 8000,
            rejectUnauthorized: false
        };

        console.log(`\n📡 请求: ${url.toString()}`);

        const req = client.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log('响应数据:', JSON.stringify(jsonData, null, 2));
                    resolve({ 
                        status: res.statusCode, 
                        data: jsonData,
                        success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0)
                    });
                } catch (e) {
                    console.log('原始响应:', data.substring(0, 200));
                    resolve({ 
                        status: res.statusCode, 
                        data: data,
                        success: false
                    });
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error.message);
            reject(error);
        });

        req.on('timeout', () => {
            console.log('⏰ 请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

// 测试单个API配置
async function testAPIConfig(apiConfig) {
    console.log(`\n🧪 测试 ${apiConfig.name}`);
    console.log(`Base URL: ${apiConfig.baseUrl}`);
    console.log(`App ID: ${apiConfig.appId}`);
    
    const headers = buildHeaders(apiConfig.appId);
    
    // 测试不同的端点
    const testEndpoints = [
        '/garden/Gardenmemberinfo/getMemberInfo',
        '/garden/sorghum/index',
        '/garden/sign/dailySign',
        '/member/info',
        '/user/profile',
        '/api/member/info',
        '/',
        '/index'
    ];
    
    let successCount = 0;
    
    for (const endpoint of testEndpoints) {
        try {
            const result = await makeRequest(apiConfig.baseUrl, endpoint, headers);
            
            if (result.success) {
                console.log('✅ 成功！找到有效端点');
                successCount++;
                return { config: apiConfig, endpoint: endpoint, result: result };
            } else if (result.status === 200) {
                console.log('⚠️ 端点可访问但返回错误');
            } else {
                console.log('❌ 端点不可访问');
            }
        } catch (error) {
            console.log('❌ 错误:', error.message);
        }
        
        // 短暂等待避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`${apiConfig.name} 测试完成，成功: ${successCount}/${testEndpoints.length}`);
    return null;
}

// 主测试函数
async function main() {
    console.log('=== 开始测试所有可能的API配置 ===\n');
    
    const results = [];
    
    for (const apiConfig of possibleAPIs) {
        try {
            const result = await testAPIConfig(apiConfig);
            if (result) {
                results.push(result);
                console.log(`\n🎉 找到有效配置: ${result.config.name}`);
                console.log(`端点: ${result.endpoint}`);
                console.log(`完整URL: ${result.config.baseUrl}${result.endpoint}`);
            }
        } catch (error) {
            console.log(`${apiConfig.name} 测试失败:`, error.message);
        }
        
        // 等待1秒再测试下一个
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n=== 测试结果汇总 ===');
    if (results.length > 0) {
        console.log(`✅ 找到 ${results.length} 个有效配置:`);
        results.forEach((result, index) => {
            console.log(`${index + 1}. ${result.config.name}: ${result.config.baseUrl}${result.endpoint}`);
        });
    } else {
        console.log('❌ 没有找到有效的API配置');
        console.log('\n💡 建议:');
        console.log('1. 检查Token是否正确');
        console.log('2. 确认API域名是否有变化');
        console.log('3. 检查网络连接');
        console.log('4. 尝试使用抓包工具获取真实API地址');
    }
    
    console.log('\n✅ 测试完成！');
}

main().catch(console.error);
