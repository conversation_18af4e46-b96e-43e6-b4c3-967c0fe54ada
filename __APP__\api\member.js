Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var r=[],n=!0,a=!1,i=void 0;try{for(var u,o=e[Symbol.iterator]();!(n=(u=o.next()).done)&&(r.push(u.value),!t||r.length!==t);n=!0);}catch(e){a=!0,i=e}finally{try{!n&&o.return&&o.return()}finally{if(a)throw i}}return r}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=c(require("./base.js")),i=c(require("./../utils/Page.js")),u=c(require("./../utils/Pages.js")),o=c(require("./../utils/Tips.js")),s=c(require("./../npm/wepy/lib/wepy.js"));function c(e){return e&&e.__esModule?e:{default:e}}function p(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(a,i){try{var u=t[a](i),o=u.value}catch(e){return void r(e)}if(!u.done)return Promise.resolve(o).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(o)}("next")}))}}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=(t=e=function(e){function t(){return l(this,t),f(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}var c,h,m,g,v,b,d,y,k,w,R,x,U,j,S,M,_,A,C,I,D,T,J,P,B,O,F,G;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,a.default),n(t,null,[{key:"info",value:function(){var e=this;return Promise.all([this._member(),this._card()]).then((function(t){var n=r(t,2),a=n[0],i=n[1];return{member:a,card:i,discount:e.processDiscount(i,a)}}))}},{key:"regist",value:(G=p(regeneratorRuntime.mark((function e(t,r){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.baseUrl+"/members?sms_code="+r,e.abrupt("return",this.post(n,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e,t){return G.apply(this,arguments)})},{key:"code",value:(F=p(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/members/sms_code?phone="+t,e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return F.apply(this,arguments)})},{key:"bonus",value:(O=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/members/bonus_detail?by=create_time&sort=desc",e.abrupt("return",new i.default(t,this.processBonusTransformation.bind(this)));case 2:case"end":return e.stop()}}),e,this)}))),function(){return O.apply(this,arguments)})},{key:"_card",value:function(){var e=this.baseUrl+"/memberCards";return this.get(e)}},{key:"_member",value:function(){var e=this.baseUrl+"/members";return this.get(e)}},{key:"processDiscount",value:function(e,t){if(null==t||null==e)return null;if(1!=e.supplyDiscount)return null;var r=t.discountRule,n=t.customDiscount;if(null==r)return null;var a=100;if(n>0&&n<=100?a=n:null!=r&&r.discount<100&&(a=r.discount),null==a||a>=100||a<=0)return null;var i=r.discountCategoryLists;if(null==i||i.length<1)return null;var u=r.discountCategoryLists.map((function(e){return e.categoryId}));return{level:r.levelName,categories:u,rate:a}}},{key:"processBonusTransformation",value:function(e){var t={};return e.addBonus>0?t.costMoney="消费金额：￥"+e.costMoney.toFixed(2):t.costMoney="抵扣金额：￥"+e.costMoney.toFixed(2),t.addBonus=e.addBonus,t.createTime=e.createTime,t.orderId=e.orderId,t.typeDesc=e.typeDesc,t}},{key:"getRoleType",value:(B=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/Member/getRoleType",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return B.apply(this,arguments)})},{key:"hasDataMsCenterUser",value:function(){var e=this.baseUrl+"/Member/hasDataMsCenterUser";return this.get(e)}},{key:"getMemberInfo",value:function(){var e=this.baseUrl+"/Member/getJifenShopMemberInfo";return this.get(e)}},{key:"createDataMsCenterUser",value:(P=p(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/Member/createDataMsCenterUser",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return P.apply(this,arguments)})},{key:"tongdun",value:(J=p(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",!1);case 3:case"end":return e.stop()}}),e,this)}))),function(){return J.apply(this,arguments)})},{key:"getYonyouMemberInfo",value:function(){var e=this.baseUrl+"/Member/getYonyouMemberInfo";return this.get(e)}},{key:"pointsRecord",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.baseUrl+"/Member/pointsRecord";return this.get(t,e)}},{key:"pointsList",value:(T=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/member/pointsList",e.abrupt("return",new u.default(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return T.apply(this,arguments)})},{key:"saveYonyouMemberInfo",value:(D=p(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/Member/saveYonyouMemberInfo",n={userInfo:t},e.abrupt("return",this.post(r,n));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return D.apply(this,arguments)})},{key:"loginToMobileUmall",value:(I=p(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/Member/loginToMobileUmall",n={page:t},e.abrupt("return",this.post(r,n));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return I.apply(this,arguments)})},{key:"getMobileUmallUrl",value:(C=p(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/Member/getMobileUmallUrl",n={page:t},e.abrupt("return",this.post(r,n));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return C.apply(this,arguments)})},{key:"getJifenShopJwt",value:(A=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/Member/getJwt",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return A.apply(this,arguments)})},{key:"getJifenShopMemberInfo",value:function(){var e=this.baseUrl+"/Member/getJifenShopMemberInfo";return this.get(e)}},{key:"saveLastAuth",value:function(){var e=this.baseUrl+"/Member/saveLastAuth";return this.get(e)}},{key:"sendSnlyZeroCoupon",value:(_=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/member/member/sendSnlyZeroCoupon",e.abrupt("return",this.post(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return _.apply(this,arguments)})},{key:"updateNicknameAndAvatar",value:(M=p(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/member/member/updateNicknameAndAvatar",e.abrupt("return",this.post(r,t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return M.apply(this,arguments)})},{key:"getAuthData",value:(S=p(regeneratorRuntime.mark((function e(){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.default.getStorageSync("authData");case 2:if((r=e.sent)&&!(r.expire_time<parseInt((new Date).getTime()/1e3+3600))){e.next=13;break}return e.next=6,t.getJifenShopJwt();case 6:return n=e.sent,r={expire_time:n.expire_time,authorized_token:n.jwt},s.default.$instance.globalData.auth.Authorization=r.authorized_token,e.next=11,s.default.setStorageSync("authData",r);case 11:return e.next=13,s.default.setStorageSync("Authorization",r.authorized_token);case 13:return e.abrupt("return",r);case 14:case"end":return e.stop()}}),e,this)}))),function(){return S.apply(this,arguments)})},{key:"isJifenLogined",value:(j=p(regeneratorRuntime.mark((function e(){var r,n,a,i,u,c,p;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.default.getStorageSync("login_code");case 2:if(r=e.sent,n=void 0,!r){e.next=13;break}return e.prev=5,e.next=8,t.getAuthData();case 8:n=e.sent,e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:if(r&&n){e.next=25;break}return a=getCurrentPages(),i=a[a.length-1],u=i.route,c=i.options,p=[],Object.keys(c).forEach((function(e){c[e]&&p.push(e+"="+c[e])})),p=""+p.join("&"),e.next=23,s.default.setStorageSync("logined_target_url","/"+u+"?"+p);case 23:return o.default.confirm("请先登录").then((function(e){wx.navigateTo({url:"/pages/customer/index"})})),e.abrupt("return",!1);case 25:return e.abrupt("return",n);case 26:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return j.apply(this,arguments)})},{key:"modifyPhone",value:(U=p(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/member/member/modifyPhone",e.abrupt("return",this.post(r,t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return U.apply(this,arguments)})},{key:"saveCompetitionInfo",value:(x=p(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/springfestival/addCommitData",n={competitionInfo:t},e.abrupt("return",this.post(r,n));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return x.apply(this,arguments)})},{key:"buryingPoint",value:(R=p(regeneratorRuntime.mark((function e(t,r,n,a,i,u,o){var s,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=this.pointUrl+"/statistics",c={event_id:t,membaer_id:r,os:n,brower:a,phone_model:i,networktype:u,resolution:o||""},e.abrupt("return",this.post(s,c).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e,t,r,n,a,i,u){return R.apply(this,arguments)})},{key:"getBirthdaypresentInfo",value:function(){var e=this.jifenShopApiUrl+"/member/birthday/is_show";return this.get(e)}},{key:"receiveBirthdaypresent",value:function(){var e=this.jifenShopApiUrl+"/member/birthday/receive";return this.get(e)}},{key:"birthdayUpgradeTrace",value:function(e){var t=this.jifenShopApiUrl+"/member/birthday/birthdayUpgradeTrace",r={event_name:e};return this.post(t,r)}},{key:"officialAccount",value:(w=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/member/member/isSubscribe",e.abrupt("return",this.post(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return w.apply(this,arguments)})},{key:"unbindwechat",value:(k=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/member/member/unbindwechat",e.abrupt("return",this.post(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return k.apply(this,arguments)})},{key:"cancellation",value:(y=p(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/member/member/cancellation",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"miniprogramErrorRecord",value:function(e){var t=this.baseUrl+"/miniprogramerrorlog/record";return this.post(t,e)}},{key:"jifenCrmCreateJwt",value:(d=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/api/v2/jifenCrm/createJwt",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"jifenCrmGetBindInfo",value:(b=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(this.jifenShopApiUrl+"/api/v2/jifenCrm/getBindInfo"),t=this.jifenShopApiUrl+"/api/v2/jifenCrm/getBindInfo",e.abrupt("return",this.get(t));case 3:case"end":return e.stop()}}),e,this)}))),function(){return b.apply(this,arguments)})},{key:"jifenCrmSureBind",value:(v=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/api/v2/jifenCrm/sureBind",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return v.apply(this,arguments)})},{key:"checkBindAndGotoJifenShop",value:(g=p(regeneratorRuntime.mark((function e(){var r,n,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.jifenCrmGetUpgradeStatus();case 3:if(1==(r=e.sent).status){e.next=7;break}return o.default.toasts(r.msg),e.abrupt("return");case 7:return e.next=9,t.jifenCrmGetBindInfo();case 9:n=e.sent,console.log("checkAuthAndGotoJifenShop",n),1==n.auth_status?t.gotoJifenShop(a):wx.navigateTo({url:"/pages/customer/goToJph?to="+a}),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(0),console.log(e.t0),o.default.toasts(e.t0.message);case 18:case"end":return e.stop()}}),e,this,[[0,14]])}))),function(){return g.apply(this,arguments)})},{key:"gotoJifenShop",value:(m=p(regeneratorRuntime.mark((function e(){var t,r,n,a,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="wx8d41cdc44c8aeaab",e.prev=1,e.next=4,this.jifenCrmCreateJwt();case 4:r=e.sent,console.log("gotoJifenShop",t,r),n="pages/pointsmall/pointsmall?mdsource=hyjlb&crmtoken="+r.auth,"pointsDetail"==i?n="pages/pointsmall/pointsmall?mdsource=hyjlb&crmtoken="+r.auth:"xjProduct"==i&&(n="pages/breadcrumb/b1/b1?mdsource=hyjlb&link=https%3A%2F%2Fjf.exijiu.com%2Fpages%2Fshop.html%23categoryGuide%3FcatId%3D241&crmtoken="+r.auth),console.log(n),a={appId:t,path:n,success:function(e){console.log(e)}},console.log(a),wx.navigateToMiniProgram(a),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(1),console.log(e.t0),o.default.toasts(e.t0.message);case 18:case"end":return e.stop()}}),e,this,[[1,14]])}))),function(){return m.apply(this,arguments)})},{key:"jifenCrmGetUpgradeStatus",value:(h=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/api/v2/jifenCrm/getUpgradeStatus",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"getRecentInputAddress",value:(c=p(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/jiaocanggivewine/getRecentInputAddress",e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})}]),t}(),e.pointUrl=s.default.$instance.globalData.pointUrl,t);exports.default=h;