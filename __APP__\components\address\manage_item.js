Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=o(require("./../../npm/wepy/lib/wepy.js")),t=o(require("./../../api/address.js")),r=o(require("./../../utils/Event.js")),n=o(require("./../../utils/Tips.js"));function o(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(o,u){try{var a=t[o](u),i=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(o){function s(){var e,o,c;a(this,s);for(var f=arguments.length,l=Array(f),d=0;d<f;d++)l[d]=arguments[d];return o=c=i(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(l))),c.props={address:{},disabled:{default:null}},c.methods={select:function(e){""!=e?this.$emit("select",e):n.default.alert("非配送区域")},edit:function(e){var t={mode:"edit",id:e};this.$root.$navigate("/pages/address/edit",t)},setDefault:function(e){var o=this;return u(regeneratorRuntime.mark((function u(){return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return n.default.loading(),o.next=3,t.default.setDefault(e);case 3:return o.next=5,n.default.success("操作成功");case 5:r.default.emit(r.default.ADDRESS_LIST_UPDATE);case 6:case"end":return o.stop()}}),u,o)})))()},delete:function(e){var o=this;return u(regeneratorRuntime.mark((function u(){return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,n.default.confirm("确定删除该地址？");case 2:return n.default.loading(),o.next=5,t.default.remove(e);case 5:return o.next=7,n.default.success("操作成功");case 7:r.default.emit(r.default.ADDRESS_LIST_UPDATE);case 8:case"end":return o.stop()}}),u,o)})))()}},i(c,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,e.default.component),s}();exports.default=s;