/**
 * 分析200状态码响应
 * 专门分析那些返回200但被标记为失败的API
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class Analyze200Responses {
    constructor() {
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从测试结果中发现的返回200的API
        this.apis200 = [
            {
                domain: 'https://statistics.exijiu.com',
                path: '/api/v2/jifenCrm/createJwt'
            },
            {
                domain: 'https://statistics.exijiu.com',
                path: '/garden/wechat/auth'
            },
            {
                domain: 'https://statistics.exijiu.com',
                path: '/garden/wechat/login'
            }
        ];
        
        console.log('🔧 200响应分析器初始化完成');
        console.log(`🎯 将详细分析 ${this.apis200.length} 个返回200的API`);
    }

    /**
     * 构建请求头
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'login_code': this.loginCode
        };
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method = 'GET', data = null) {
        const headers = this.buildHeaders();
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200,
                            data: jsonData,
                            status: res.statusCode,
                            rawData: responseData,
                            headers: res.headers
                        });
                    } catch (e) {
                        resolve({ 
                            success: res.statusCode === 200,
                            data: responseData, 
                            status: res.statusCode,
                            rawData: responseData,
                            headers: res.headers,
                            parseError: e.message
                        });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({ 
                    success: false, 
                    data: null, 
                    status: 0,
                    error: error.message
                });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ 
                    success: false, 
                    data: null, 
                    status: 0,
                    error: '请求超时'
                });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 深度分析响应内容
     */
    analyzeResponse(response) {
        const analysis = {
            hasJWT: false,
            jwtFields: [],
            errorInfo: null,
            dataStructure: null,
            possibleAuth: false
        };
        
        // 检查是否有JWT相关字段
        const jwtFieldNames = ['jwt', 'token', 'auth', 'authorized_token', 'access_token', 'authToken', 'Authorization'];
        
        if (response.data && typeof response.data === 'object') {
            this.findJWTFields(response.data, jwtFieldNames, analysis.jwtFields, '');
            
            // 检查错误信息
            if (response.data.err !== undefined || response.data.code !== undefined) {
                analysis.errorInfo = {
                    err: response.data.err,
                    code: response.data.code,
                    msg: response.data.msg || response.data.message,
                    success: response.data.err === 0 || response.data.code === 0
                };
            }
            
            // 分析数据结构
            analysis.dataStructure = this.analyzeDataStructure(response.data);
        }
        
        analysis.hasJWT = analysis.jwtFields.length > 0;
        analysis.possibleAuth = analysis.hasJWT || (analysis.errorInfo && analysis.errorInfo.success);
        
        return analysis;
    }

    /**
     * 递归查找JWT字段
     */
    findJWTFields(obj, fieldNames, results, path) {
        if (!obj || typeof obj !== 'object') return;
        
        for (const [key, value] of Object.entries(obj)) {
            const currentPath = path ? `${path}.${key}` : key;
            
            // 检查是否是JWT字段
            if (fieldNames.includes(key.toLowerCase()) || fieldNames.includes(key)) {
                results.push({
                    field: key,
                    path: currentPath,
                    value: value,
                    isJWTLike: this.isJWTLike(value)
                });
            }
            
            // 递归检查嵌套对象
            if (typeof value === 'object' && value !== null) {
                this.findJWTFields(value, fieldNames, results, currentPath);
            }
        }
    }

    /**
     * 检查值是否像JWT
     */
    isJWTLike(value) {
        if (typeof value !== 'string') return false;
        
        // JWT通常是三部分用.分隔的base64字符串
        const parts = value.split('.');
        return parts.length === 3 && parts.every(part => part.length > 0);
    }

    /**
     * 分析数据结构
     */
    analyzeDataStructure(data) {
        const structure = {
            type: typeof data,
            keys: [],
            hasNestedObjects: false,
            totalFields: 0
        };
        
        if (typeof data === 'object' && data !== null) {
            structure.keys = Object.keys(data);
            structure.totalFields = structure.keys.length;
            
            for (const key of structure.keys) {
                if (typeof data[key] === 'object' && data[key] !== null) {
                    structure.hasNestedObjects = true;
                    break;
                }
            }
        }
        
        return structure;
    }

    /**
     * 运行详细分析
     */
    async runDetailedAnalysis() {
        console.log('🔍 开始详细分析200响应...');
        
        const results = [];
        
        for (let i = 0; i < this.apis200.length; i++) {
            const api = this.apis200[i];
            console.log(`\n📊 [${i + 1}/${this.apis200.length}] 分析: ${api.domain}${api.path}`);
            
            try {
                // 测试GET请求
                console.log('🔍 测试GET请求...');
                const getResult = await this.makeRequest(api.domain, api.path, 'GET');
                const getAnalysis = this.analyzeResponse(getResult);
                
                // 测试POST请求
                console.log('🔍 测试POST请求...');
                const postResult = await this.makeRequest(api.domain, api.path, 'POST', {});
                const postAnalysis = this.analyzeResponse(postResult);
                
                const apiResult = {
                    api: api,
                    get: {
                        result: getResult,
                        analysis: getAnalysis
                    },
                    post: {
                        result: postResult,
                        analysis: postAnalysis
                    }
                };
                
                results.push(apiResult);
                
                // 输出详细信息
                console.log(`\n📋 GET结果:`);
                console.log(`  状态: ${getResult.status}`);
                console.log(`  成功: ${getAnalysis.errorInfo?.success || false}`);
                console.log(`  JWT字段: ${getAnalysis.jwtFields.length}`);
                if (getAnalysis.jwtFields.length > 0) {
                    getAnalysis.jwtFields.forEach(field => {
                        console.log(`    ${field.field}: ${field.isJWTLike ? 'JWT格式' : '非JWT格式'}`);
                    });
                }
                console.log(`  原始响应: ${JSON.stringify(getResult.data, null, 2)}`);
                
                console.log(`\n📋 POST结果:`);
                console.log(`  状态: ${postResult.status}`);
                console.log(`  成功: ${postAnalysis.errorInfo?.success || false}`);
                console.log(`  JWT字段: ${postAnalysis.jwtFields.length}`);
                if (postAnalysis.jwtFields.length > 0) {
                    postAnalysis.jwtFields.forEach(field => {
                        console.log(`    ${field.field}: ${field.isJWTLike ? 'JWT格式' : '非JWT格式'}`);
                    });
                }
                console.log(`  原始响应: ${JSON.stringify(postResult.data, null, 2)}`);
                
            } catch (error) {
                console.log(`❌ 分析失败: ${error.message}`);
                results.push({
                    api: api,
                    error: error.message
                });
            }
            
            await this.sleep(1000);
        }
        
        // 输出总结
        console.log('\n' + '='.repeat(60));
        console.log('📊 详细分析结果总结');
        console.log('='.repeat(60));
        
        const jwtAPIs = results.filter(r => 
            (r.get?.analysis?.hasJWT) || (r.post?.analysis?.hasJWT)
        );
        
        const successAPIs = results.filter(r => 
            (r.get?.analysis?.errorInfo?.success) || (r.post?.analysis?.errorInfo?.success)
        );
        
        console.log(`\n🎯 关键发现:`);
        console.log(`  📊 总分析数: ${results.length}`);
        console.log(`  🔑 包含JWT的API: ${jwtAPIs.length}`);
        console.log(`  ✅ 返回成功的API: ${successAPIs.length}`);
        
        if (jwtAPIs.length > 0) {
            console.log(`\n🎉 发现JWT的API:`);
            jwtAPIs.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.api.domain}${result.api.path}`);
                if (result.get?.analysis?.hasJWT) {
                    console.log(`     GET: ${result.get.analysis.jwtFields.map(f => f.field).join(', ')}`);
                }
                if (result.post?.analysis?.hasJWT) {
                    console.log(`     POST: ${result.post.analysis.jwtFields.map(f => f.field).join(', ')}`);
                }
            });
        }
        
        if (successAPIs.length > 0) {
            console.log(`\n✅ 成功的API:`);
            successAPIs.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.api.domain}${result.api.path}`);
                if (result.get?.analysis?.errorInfo?.success) {
                    console.log(`     GET: 成功 (${result.get.analysis.errorInfo.msg || '无消息'})`);
                }
                if (result.post?.analysis?.errorInfo?.success) {
                    console.log(`     POST: 成功 (${result.post.analysis.errorInfo.msg || '无消息'})`);
                }
            });
        }
        
        return results;
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出类
module.exports = Analyze200Responses;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new Analyze200Responses();
    
    console.log('🔍 200响应详细分析器');
    console.log('🎯 专门分析返回200状态码的API');
    console.log('🔑 寻找JWT和认证相关信息');
    console.log('');
    
    // 运行详细分析
    analyzer.runDetailedAnalysis().then(results => {
        const hasJWT = results.some(r => 
            (r.get?.analysis?.hasJWT) || (r.post?.analysis?.hasJWT)
        );
        
        if (hasJWT) {
            console.log('\n🎊 找到了JWT！Authorization生成方法已发现！');
        } else {
            console.log('\n🤔 虽然API返回200，但没有找到JWT字段');
            console.log('💡 可能需要不同的参数或认证方式');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
