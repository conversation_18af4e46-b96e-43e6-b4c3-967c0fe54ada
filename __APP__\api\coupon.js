Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),t=o(require("./base.js")),n=o(require("./../utils/Page.js"));function o(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(o){function s(){return r(this,s),i(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,t.default),e(s,null,[{key:"page",value:function(){var e=this.baseUrl+"/coupons?by=accept_time&sort=desc";return new n.default(e,this._processCouponItem.bind(this))}},{key:"all",value:function(){var e=this,t=this.baseUrl+"/coupons/all";return this.get(t).then((function(t){var n=t.owned,o=t.show;return{pickCoupons:e.processCouponsList(o,e._processPickItem.bind(e)),ownCoupons:e.processCouponsList(n,e._processCouponItem.bind(e))}}))}},{key:"processCouponsList",value:function(e,t){return e&&e.length>0?e.map(t):[]}},{key:"list",value:function(){var e=this,t=this.baseUrl+"/coupons/show";return this.get(t).then((function(t){return t&&t.length>0?t.map(e._processPickItem.bind(e)):[]}))}},{key:"own",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"NEVER_USED",n=this.baseUrl+"/coupons/list?status="+t;return this.get(n).then((function(t){return t&&t.length>0?t.map(e._processCouponItem.bind(e)):[]}))}},{key:"pick",value:function(e){var t=this.baseUrl+"/coupons/"+e+"/get";return this.get(t)}},{key:"remove",value:function(e){var t=this.baseUrl+"/coupons/"+e;return this.delete(t)}},{key:"use",value:function(e){var t=this.baseUrl+"/coupons/use/"+e;return this.put(t)}},{key:"available",value:function(e){var t=this,n=this.baseUrl+"/coupons/order_available",o={orderGoodsInfos:e};return this.post(n,o).then((function(e){return e?e.map((function(e){return t._processCouponItem(e)})):[]}))}},{key:"campaign",value:function(e){var t=this.baseUrl+"/coupons/campaign";return this.post(t,e)}},{key:"_processPickItem",value:function(e){return e.beginTime=this._convertTimestapeToDay(e.beginTime),e.dueTime=this._convertTimestapeToDay(e.dueTime),e}},{key:"_processCouponItem",value:function(e){var t=e;if(null==e.coupon)return null;var n=e.coupon;return n.status=t.status,n.id=t.id,n.couponId=t.couponId,n.acceptTime=t.acceptTime,n.usedTime=t.usedTime,n.beginTime=this._convertTimestapeToDay(n.beginTime),n.dueTime=this._convertTimestapeToDay(n.dueTime),this._processCouponDisplayFlag(n),n}},{key:"_processCouponDisplayFlag",value:function(e){"NEVER_USED"==e.status&&(this._dayIntervalToNow(e.acceptTime)<=1&&(e.isNew=!0),this._dayIntervalToNow(e.dueTime)>=-1&&(e.isExpiring=!0))}},{key:"_dayIntervalToNow",value:function(e){var t=Date.parse(e);return Math.round((Date.now()-t)/864e5)}},{key:"_convertTimestapeToDay",value:function(e){var t=e;return-1!=e.indexOf(" ")&&(t=e.substring(0,e.indexOf(" "))),t.replace(/-/g,".")}}]),s}();exports.default=s;