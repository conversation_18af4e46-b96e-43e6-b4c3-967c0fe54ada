exports.__esModule=!0,exports.default=function(r){for(var u=arguments.length,n=new Array(u>1?u-1:0),a=1;a<u;a++)n[a-1]=arguments[a];var f=(0,t.default)((0,l.default)(n))?n.pop():{};if((0,e.default)(n.every(i.default)&&((0,i.default)(r)||(0,t.default)(r)),"Expected optional object followed by string action types"),(0,i.default)(r))return O([r].concat(n),f);return v({},b(r,f),O(n,f))};var e=p(require("./../../invariant/browser.js")),t=p(require("./utils/isPlainObject.js")),r=p(require("./utils/isFunction.js")),u=p(require("./utils/identity.js")),n=p(require("./utils/isArray.js")),i=p(require("./utils/isString.js")),a=p(require("./utils/isNil.js")),l=p(require("./utils/getLastElement.js")),f=p(require("./utils/camelCase.js")),o=p(require("./utils/arrayToObject.js")),s=p(require("./utils/flattenActionMap.js")),c=p(require("./utils/unflattenActionCreators.js")),d=p(require("./createAction.js")),j=require("./constants.js");function p(e){return e&&e.__esModule?e:{default:e}}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},u=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(u=u.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),u.forEach((function(t){y(e,t,r[t])}))}return e}function y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b(e,t){var r=q((0,s.default)(e,t));return(0,c.default)(r,t)}function q(t,i){var l=void 0===i?{}:i,f=l.prefix,s=l.namespace,c=void 0===s?j.DEFAULT_NAMESPACE:s;return(0,o.default)(Object.keys(t),(function(i,l){var o,s=t[l];(0,e.default)(function(e){if((0,r.default)(e)||(0,a.default)(e))return!0;if((0,n.default)(e)){var t=e[0],i=void 0===t?u.default:t,l=e[1];return(0,r.default)(i)&&(0,r.default)(l)}return!1}(s),"Expected function, undefined, null, or array with payload and meta functions for "+l);var j=f?""+f+c+l:l,p=(0,n.default)(s)?d.default.apply(void 0,[j].concat(s)):(0,d.default)(j,s);return v({},i,((o={})[l]=p,o))}))}function O(e,t){var r=q((0,o.default)(e,(function(e,t){var r;return v({},e,((r={})[t]=u.default,r))})),t);return(0,o.default)(Object.keys(r),(function(e,t){var u;return v({},e,((u={})[(0,f.default)(t)]=r[t],u))}))}