Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),t=o(require("./base.js")),r=o(require("./../utils/Page.js"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(o){function a(){return n(this,a),s(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default),e(a,null,[{key:"add",value:function(e){var t=this.baseUrl+"/comments";return this.post(t,e)}},{key:"page",value:function(){var e=this.baseUrl+"/comments";return new r.default(e,this._processGoodsComment.bind(this))}},{key:"list",value:function(){var e=this.baseUrl+"/comments/list";return new r.default(e,this._processGoodsComment.bind(this))}},{key:"count",value:function(e){var t=this.baseUrl+"/comments/count?goods_id="+e;return this.get(t)}},{key:"_processGoodsComment",value:function(e){var t={};t.createTime=e.createTime.substring(0,10),t.starArr=[0,0,0,0,0];for(var r=0;r<e.star;r++)t.starArr[r]=1;return t.star=e.star,e.customer?(t.avatar=e.customer.avatarUrl,t.nickName=e.customer.nickName):(t.avatar="/images/icons/customer.png",t.nickName="微信用户"),t.comment=e.comment,null!==e.goods&&(t.goods_image=e.goods.images[0].url,t.goods_name=e.goods.name),t}}]),a}();exports.default=a;