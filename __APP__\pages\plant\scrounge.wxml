<view style="height:100%">
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outer" wx:if="{{init}}">
        <view class="content2" style="background-image:{{contentBg}}">
            <view class="cloud1"></view>
            <view class="cloud2"></view>
            <view class="header-bar">
                <view class="avatar">
                    <view class="img-content">
                        <image class="avatar-image" src="{{userInfo.head_imgurl}}"></image>
                    </view>
                    <view class="name">{{userInfo.nick_name}}的农场</view>
                </view>
            </view>
            <view class="wbox">
                <view bindtap="redirecttoWithLogined" class="goPlant" data-url="/pages/plant/friends?friendId={{userInfo.member_id}}"></view>
            </view>
            <view class="wineBox">
                <view class="jar" hoverClass="jar-animation" style="background-image:url({{jarImage}})">
                    <view class="wine-num {{userInfo.wine==0?'wine-num-0':''}}">{{userInfo.wine}}L</view>
                </view>
                <view bindtap="upToMiss" class="yuanbing yuanbing{{index+1}} {{notgain[index]}}" data-show="{{index}}" id="{{item.id}}" wx:if="{{popShow[index].show}}" wx:for="{{popShow}}" wx:key="index">{{backend[index].time?backend[index].time:''}}<view class="add" wx:if="{{current==index?add:false}}">+{{harWine.wine}}L</view>
                </view>
            </view>
            <view class="bottom-bar">
                <view bindtap="showModal_firends" class="item" data-wpyshowmodal_firends-a="" hoverClass="hover1"></view>
                <view bindtap="gotoWithLogined" class="item" data-url="/pages/plant/index" hoverClass="hover1"></view>
            </view>
            <view animation="{{animationData}}" class="commodity_attr_box_friends" wx:if="{{showModalStatus_firends1}}">
                <view class="title">我的微信好友</view>
                <scroll-view bindscrolltolower="friend_listShow" class="friends_cont" scrollY="true" style="height:{{friend_notlogin.length?'60%':'80%'}}">
                    <view bindtap="redirecttoWithLogined" class="friends" data-url="/pages/plant/scrounge?id={{item.member_id}}&url={{item.head_imgurl}}&nick={{item.nick_name}}&hum={{item.sorghum}}&wine={{item.wine}}&real={{item.real_name}}" wx:for="{{friend_list}}" wx:for-index="tindex" wx:key="tindex">
                        <view class="task_name">
                            <view class="tLeft">
                                <text class="ranks">{{tindex+1}}</text>
                                <image class="img" src="{{item.head_imgurl}}"></image>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name?item.nick_name:item.real_name}}</view>
                                </view>
                            </view>
                            <view class="task_btn">{{item.wine}}L</view>
                        </view>
                        <view class="handbg" wx:if="{{item.is_can_steal}}"></view>
                    </view>
                    <view class="nomore" wx:if="{{more_friend}}">您没有更多好友啦，到每日任务里通过分享即可邀请并和TA成为游戏好友！</view>
                </scroll-view>
                <view wx:if="{{friend_notlogin.length}}">
                    <view class="title2">您的好友已经迷路了，快去找他回来</view>
                    <view class="friends notlogin" wx:for="{{friend_notlogin}}" wx:key="index">
                        <view class="task_name">
                            <view class="tLeft">
                                <view class="img"></view>
                                <view class="abc">
                                    <view class="font1">{{item.nick_name}}</view>
                                </view>
                            </view>
                            <button class="invite_btn" openType="share">邀请TA</button>
                        </view>
                    </view>
                </view>
            </view>
            <view bindtap="hideModal" catchtouchmove="true" class="commodity_screen" wx:if="{{showModalStatus}}"></view>
        </view>
    </view>
</view>
