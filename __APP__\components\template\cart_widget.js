Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=a(require("./../../npm/wepy/lib/wepy.js")),n=require("./../../npm/wepy-redux/lib/index.js"),o=a(require("./../../store/utils.js")),u=a(require("./../../utils/Cart.js")),i=a(require("./../../mixins/router.js"));function a(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=(0,n.connect)({cart:o.default.get("cart")})(e=function(e){function n(){var e,t,r;c(this,n);for(var o=arguments.length,a=Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=r=f(this,(e=n.__proto__||Object.getPrototypeOf(n)).call.apply(e,[this].concat(a))),r.props={},r.cartManager=u.default.create(),r.methods={},r.mixins=[i.default],f(r,t)}var o,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,r.default.component),t(n,[{key:"onLoad",value:(o=regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)})),a=function(){var e=o.apply(this,arguments);return new Promise((function(t,r){return function n(o,u){try{var i=e[o](u),a=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(a)}("next")}))},function(){return a.apply(this,arguments)})}]),n}())||e;exports.default=s;