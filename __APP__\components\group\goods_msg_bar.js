Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=o(require("./../../npm/wepy/lib/wepy.js")),t=o(require("./../../mixins/router.js")),r=o(require("./../../mixins/countdown.js"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(o){function u(){var e,o,a;n(this,u);for(var s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];return o=a=i(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(c))),a.props={rule:{},saveText:{default:"拼团立省￥"},saveNum:{default:0}},a.data={},a.methods={},a.watch={rule:function(e){e.price&&e.goods.maxPrice&&(this.saveNum=(1*e.goods.maxPrice-1*e.price).toFixed(2))}},a.mixins=[t.default,r.default],i(a,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,e.default.component),u}();exports.default=u;