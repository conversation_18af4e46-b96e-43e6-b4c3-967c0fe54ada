/**
 * 积分API发现分析器
 * 基于源码搜索发现的积分相关API进行深度分析
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class JifenAPIDiscoveryAnalyzer {
    constructor() {
        // 已知认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 从源码搜索中发现的积分相关API
        this.discoveredAPIs = [
            // 基础API配置
            {
                name: 'jifenShopApiUrl',
                baseUrl: 'https://apimallwm.exijiu.com/api',
                description: '积分商城API基础URL'
            },
            
            // 具体的积分API端点
            {
                name: 'duanwujc_get_share_token',
                url: '/duanwujc/poster/get_share_token',
                method: 'GET',
                description: '端午积分活动分享token'
            },
            {
                name: 'daily_lottery',
                url: '/daily_lottery/lottery/index',
                method: 'GET',
                description: '每日抽奖列表'
            },
            {
                name: 'weekly_lottery',
                url: '/weekly_lottery/lottery/index',
                method: 'GET',
                description: '每周抽奖列表'
            },
            {
                name: 'duanwu_material_share',
                url: '/duanwu/material/share',
                method: 'GET',
                description: '端午素材分享'
            },
            {
                name: 'duanwu_member_getH5url',
                url: '/duanwu/member/getH5url',
                method: 'GET',
                description: '端午会员H5链接'
            },
            {
                name: 'duanwu2023_createShare',
                url: '/duanwu2023/createShare',
                method: 'GET',
                description: '端午2023创建分享'
            },
            {
                name: 'activity_jump_createShare',
                url: '/activity/jump/createShare',
                method: 'GET',
                description: '活动跳转创建分享'
            },
            {
                name: 'zq2021_scangift_info',
                url: '/zq2021/scangift/info',
                method: 'GET',
                description: '足球2021扫码礼品信息'
            },
            {
                name: 'survey_question_indexs',
                url: '/survey/question/indexs',
                method: 'GET',
                description: '调查问卷索引'
            },
            {
                name: 'member_birthday_is_show',
                url: '/member/birthday/is_show',
                method: 'GET',
                description: '会员生日显示'
            },
            {
                name: 'member_birthday_receive',
                url: '/member/birthday/receive',
                method: 'GET',
                description: '会员生日领取'
            },
            {
                name: 'member_member_isSubscribe',
                url: '/member/member/isSubscribe',
                method: 'GET',
                description: '会员订阅状态'
            },
            {
                name: 'member_member_unbindwechat',
                url: '/member/member/unbindwechat',
                method: 'POST',
                description: '会员解绑微信'
            }
        ];
        
        // 从JWT中提取的信息
        this.memberID = 6865357;
        this.unionID = 'oA4oD1fddsj8tqw_UU2Z52eWTSpg';
        
        console.log('🔧 积分API发现分析器初始化完成');
        console.log(`🎯 发现了 ${this.discoveredAPIs.length} 个积分相关API`);
    }

    /**
     * 分析发现的API模式
     */
    analyzeDiscoveredAPIPatterns() {
        console.log('\n🔍 分析发现的API模式...');
        
        // 按功能分类
        const categories = {
            lottery: [],
            member: [],
            activity: [],
            share: [],
            survey: [],
            other: []
        };
        
        this.discoveredAPIs.forEach(api => {
            if (api.url && api.url.includes('lottery')) {
                categories.lottery.push(api);
            } else if (api.url && api.url.includes('member')) {
                categories.member.push(api);
            } else if (api.url && (api.url.includes('activity') || api.url.includes('duanwu') || api.url.includes('zq'))) {
                categories.activity.push(api);
            } else if (api.url && (api.url.includes('share') || api.url.includes('Share'))) {
                categories.share.push(api);
            } else if (api.url && (api.url.includes('survey') || api.url.includes('question'))) {
                categories.survey.push(api);
            } else {
                categories.other.push(api);
            }
        });
        
        console.log('📊 API分类统计:');
        Object.entries(categories).forEach(([category, apis]) => {
            if (apis.length > 0) {
                console.log(`  ${category}: ${apis.length}个API`);
                apis.forEach(api => {
                    console.log(`    - ${api.name}: ${api.url}`);
                });
            }
        });
        
        console.log('\n💡 关键发现:');
        console.log('1. 大量的积分活动API (端午、足球等)');
        console.log('2. 会员相关API (生日、订阅等)');
        console.log('3. 抽奖系统API (每日、每周)');
        console.log('4. 分享机制API');
        console.log('5. 调查问卷API');
        
        return categories;
    }

    /**
     * 测试所有发现的API
     */
    async testAllDiscoveredAPIs() {
        console.log('\n🧪 测试所有发现的API...');
        
        const results = {
            successful: [],
            failed: [],
            tokenGenerating: []
        };
        
        for (let i = 0; i < this.discoveredAPIs.length; i++) {
            const api = this.discoveredAPIs[i];
            
            // 跳过baseUrl配置
            if (api.name === 'jifenShopApiUrl') continue;
            
            console.log(`\n🔍 [${i}/${this.discoveredAPIs.length}] 测试API: ${api.name}`);
            console.log(`📍 URL: ${api.url}`);
            
            try {
                const result = await this.testSingleAPI(api);
                
                if (result.success) {
                    console.log('✅ 测试成功！');
                    results.successful.push(result);
                    
                    // 检查是否返回了token相关信息
                    if (result.data && this.containsTokenInfo(result.data)) {
                        console.log('🔑 发现Token相关信息！');
                        results.tokenGenerating.push(result);
                    }
                    
                    // 显示响应数据
                    if (result.data) {
                        console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    }
                } else {
                    console.log('❌ 测试失败');
                    results.failed.push(result);
                    
                    if (result.data && result.data.msg) {
                        console.log(`📄 错误信息: ${result.data.msg}`);
                    }
                }
                
            } catch (error) {
                console.log(`💥 测试异常: ${error.message}`);
                results.failed.push({
                    api: api,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 测试单个API
     */
    async testSingleAPI(api) {
        const baseUrl = 'https://apimallwm.exijiu.com/api';
        const fullUrl = baseUrl + api.url;
        
        // 尝试多种认证方式
        const authMethods = [
            {
                name: '完整认证',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': this.validAuth,
                    'login_code': this.loginCode,
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
                }
            },
            {
                name: '仅Authorization',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': this.validAuth,
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
                }
            },
            {
                name: '仅login_code',
                headers: {
                    'Content-Type': 'application/json',
                    'login_code': this.loginCode,
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
                }
            }
        ];
        
        for (const authMethod of authMethods) {
            console.log(`  🔐 尝试认证方式: ${authMethod.name}`);
            
            const result = await this.makeRequest(fullUrl, api.method || 'GET', null, authMethod.headers);
            
            if (result.success || result.status === 200) {
                return {
                    api: api,
                    success: true,
                    authMethod: authMethod.name,
                    data: result.data,
                    status: result.status
                };
            }
        }
        
        // 所有认证方式都失败
        return {
            api: api,
            success: false,
            data: null
        };
    }

    /**
     * 检查响应是否包含Token信息
     */
    containsTokenInfo(data) {
        if (!data || typeof data !== 'object') return false;
        
        const tokenFields = ['jwt', 'token', 'auth', 'authorization', 'access_token', 'refresh_token', 'crmtoken', 'share_token'];
        
        for (const field of tokenFields) {
            if (data[field] && typeof data[field] === 'string' && data[field].length > 20) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 深度分析成功的API
     */
    analyzeSuccessfulAPIs(successfulAPIs) {
        console.log('\n📊 深度分析成功的API...');
        
        if (successfulAPIs.length === 0) {
            console.log('❌ 没有成功的API可分析');
            return;
        }
        
        console.log(`✅ 成功的API数量: ${successfulAPIs.length}`);
        
        successfulAPIs.forEach((result, index) => {
            console.log(`\n📋 [${index + 1}] ${result.api.name}`);
            console.log(`  URL: ${result.api.url}`);
            console.log(`  认证方式: ${result.authMethod}`);
            console.log(`  状态码: ${result.status}`);
            
            if (result.data) {
                // 分析响应数据结构
                const dataKeys = Object.keys(result.data);
                console.log(`  响应字段: ${dataKeys.join(', ')}`);
                
                // 检查是否有有用的信息
                if (result.data.msg) {
                    console.log(`  消息: ${result.data.msg}`);
                }
                
                if (result.data.err !== undefined) {
                    console.log(`  错误码: ${result.data.err}`);
                }
                
                // 检查是否有Token相关信息
                if (this.containsTokenInfo(result.data)) {
                    console.log('  🔑 包含Token信息！');
                }
            }
        });
    }

    /**
     * 寻找可能的Token生成API
     */
    async searchForTokenGeneratingAPIs() {
        console.log('\n🔍 寻找可能的Token生成API...');
        
        // 基于发现的API模式，构建可能的Token生成API
        const potentialTokenAPIs = [
            '/member/getToken',
            '/member/refreshToken',
            '/member/generateJwt',
            '/auth/createToken',
            '/auth/refreshJwt',
            '/jifen/createJwt',
            '/jifen/getToken',
            '/crm/createJwt',
            '/crm/getToken',
            '/activity/getToken',
            '/share/getToken',
            '/poster/getToken',
            '/lottery/getToken'
        ];
        
        const results = [];
        
        for (const apiPath of potentialTokenAPIs) {
            console.log(`🧪 测试潜在API: ${apiPath}`);
            
            try {
                const result = await this.testSingleAPI({
                    name: `potential_${apiPath.replace(/\//g, '_')}`,
                    url: apiPath,
                    method: 'GET'
                });
                
                if (result.success) {
                    console.log('✅ 发现有效API！');
                    results.push(result);
                }
                
            } catch (error) {
                // 忽略错误，继续测试
            }
        }
        
        return results;
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的积分API发现分析
     */
    async runCompleteJifenAPIAnalysis() {
        console.log('🚀 开始完整的积分API发现分析...');
        console.log('🎯 基于源码搜索发现的积分相关API');
        
        try {
            // 1. 分析API模式
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一部分: API模式分析');
            console.log('='.repeat(60));
            const patterns = this.analyzeDiscoveredAPIPatterns();
            
            // 2. 测试所有发现的API
            console.log('\n' + '='.repeat(60));
            console.log('🧪 第二部分: 测试所有发现的API');
            console.log('='.repeat(60));
            const testResults = await this.testAllDiscoveredAPIs();
            
            // 3. 深度分析成功的API
            console.log('\n' + '='.repeat(60));
            console.log('📊 第三部分: 深度分析成功的API');
            console.log('='.repeat(60));
            this.analyzeSuccessfulAPIs(testResults.successful);
            
            // 4. 寻找Token生成API
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第四部分: 寻找Token生成API');
            console.log('='.repeat(60));
            const tokenAPIs = await this.searchForTokenGeneratingAPIs();
            
            // 输出最终结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 积分API发现分析结果');
            console.log('='.repeat(60));
            
            console.log(`\n📈 分析统计:`);
            console.log(`  🔍 发现的API: ${this.discoveredAPIs.length}`);
            console.log(`  ✅ 成功的API: ${testResults.successful.length}`);
            console.log(`  ❌ 失败的API: ${testResults.failed.length}`);
            console.log(`  🔑 Token相关API: ${testResults.tokenGenerating.length}`);
            console.log(`  🎯 潜在Token API: ${tokenAPIs.length}`);
            
            if (testResults.tokenGenerating.length > 0) {
                console.log('\n🔑 发现Token相关API:');
                testResults.tokenGenerating.forEach(result => {
                    console.log(`  - ${result.api.name}: ${result.api.url}`);
                });
            }
            
            if (tokenAPIs.length > 0) {
                console.log('\n🎯 发现潜在Token API:');
                tokenAPIs.forEach(result => {
                    console.log(`  - ${result.api.name}: ${result.api.url}`);
                });
            }
            
            const hasTokenAPIs = testResults.tokenGenerating.length > 0 || tokenAPIs.length > 0;
            const hasSuccessfulAPIs = testResults.successful.length > 0;
            
            if (hasTokenAPIs) {
                console.log('\n🎉 积分API发现分析大获成功！');
                console.log('🔑 发现了Token相关的API！');
                
                return {
                    success: true,
                    tokenAPIs: [...testResults.tokenGenerating, ...tokenAPIs],
                    allResults: testResults
                };
            } else if (hasSuccessfulAPIs) {
                console.log('\n💡 积分API发现分析部分成功！');
                console.log('🔧 发现了可用的API，但没有Token生成API');
                
                return {
                    success: true,
                    successfulAPIs: testResults.successful,
                    allResults: testResults
                };
            } else {
                console.log('\n🤔 积分API发现分析未获得突破');
                console.log('💡 但获得了重要的API结构信息');
                
                return {
                    success: false,
                    patterns: patterns,
                    allResults: testResults
                };
            }
            
        } catch (error) {
            console.log('\n❌ 积分API发现分析失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = JifenAPIDiscoveryAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new JifenAPIDiscoveryAnalyzer();
    
    console.log('🔍 积分API发现分析器');
    console.log('🎯 基于源码搜索发现的积分相关API进行深度分析');
    console.log('🔑 寻找Token生成和认证相关的API');
    console.log('');
    
    // 运行完整的积分API发现分析
    analyzer.runCompleteJifenAPIAnalysis().then(result => {
        if (result.success) {
            console.log('\n🎉 积分API发现分析成功！');
            
            if (result.tokenAPIs && result.tokenAPIs.length > 0) {
                console.log('\n🎊 最重要的发现 - Token相关API:');
                result.tokenAPIs.forEach(api => {
                    console.log(`🔑 ${api.api.name}: ${api.api.url}`);
                });
            }
        } else {
            console.log('\n🤔 积分API发现分析未获得突破');
            console.log('💡 但获得了重要的API结构信息');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
