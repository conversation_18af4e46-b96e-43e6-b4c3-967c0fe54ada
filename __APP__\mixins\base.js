Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=r(require("./../npm/wepy/lib/wepy.js")),n=r(require("./../utils/Tips.js"));function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(r){function i(){var e,t,r;o(this,i);for(var u=arguments.length,l=Array(u),f=0;f<u;f++)l[f]=arguments[f];return t=r=a(this,(e=i.__proto__||Object.getPrototypeOf(i)).call.apply(e,[this].concat(l))),r.methods={nopen:function(){n.default.alert("尚未开放")}},a(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,t.default.mixin),e(i,[{key:"onLoad",value:function(){var e=t.default.$instance.globalData.pageParams;if(null!=e){var n=this.$root.$wxpage.route,r=e[n];null!=r&&(console.info("[minxin] page base init path="+n+", param=",r),Object.assign(this,r),this.$apply())}}},{key:"loaded",value:function(){this.init=!0,this.$apply(),n.default.loaded()}}]),i}();exports.default=i;