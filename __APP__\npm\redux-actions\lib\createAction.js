exports.__esModule=!0,exports.default=function(n,i,o){void 0===i&&(i=t.default);(0,e.default)((0,r.default)(i)||(0,u.default)(i),"Expected payloadCreator to be a function, undefined or null");var a=(0,u.default)(i)||i===t.default?t.default:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),u=1;u<r;u++)t[u-1]=arguments[u];return e instanceof Error?e:i.apply(void 0,[e].concat(t))},l=(0,r.default)(o),d=n.toString(),f=function(){var e=a.apply(void 0,arguments),r={type:n};return e instanceof Error&&(r.error=!0),void 0!==e&&(r.payload=e),l&&(r.meta=o.apply(void 0,arguments)),r};return f.toString=function(){return d},f};var e=n(require("./../../invariant/browser.js")),r=n(require("./utils/isFunction.js")),t=n(require("./utils/identity.js")),u=n(require("./utils/isNull.js"));function n(e){return e&&e.__esModule?e:{default:e}}