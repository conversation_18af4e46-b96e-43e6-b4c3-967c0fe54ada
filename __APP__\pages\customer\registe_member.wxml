<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="container" wx:if="{{init}}">
        <form bindsubmit="updateUserInfo">
            <view class="weui-cells-form">
                <view class="weui-cell weui-cell_input">
                    <view class="weui-cell__hd">
                        <view class="weui-label">姓名</view>
                    </view>
                    <view class="weui-cell__bd">
                        <input bindinput="inputgetName" class="weui-input" maxlength="6" name="realname" placeholder="请输入姓名" value="{{userInfo.realname}}"></input>
                    </view>
                </view>
                <view class="weui-cell weui-cell_input">
                    <view class="weui-cell__hd">
                        <view class="weui-label">电话</view>
                    </view>
                    <view class="weui-cell__bd">
                        <input class="weui-input weui-form-preview__btn_default" disabled="true" maxlength="11" placeholder="请输入电话" type="number" value="{{userInfo.phone}}"></input>
                    </view>
                </view>
                <view class="weui-cell weui-cells_after-title">
                    <view class="weui-cell__hd">
                        <view class="weui-label">称呼</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="row">
                            <radio-group bindchange="radioChange" class="radio-group row" id="sex">
                                <label class="radio row">
                                    <radio checked="{{userInfo.sex==1}}" name="sex" value="1"></radio>先生</label>
                                <label class="radio ml20 row">
                                    <radio checked="{{userInfo.sex==2}}" name="sex" value="2"></radio>女士</label>
                            </radio-group>
                        </view>
                    </view>
                </view>
                <view class="weui-cell weui-cell_input">
                    <view class="weui-cell__hd">
                        <view class="weui-label">您的生日（选填）</view>
                    </view>
                    <view class="weui-cell__bd">
                        <picker bindchange="bindDateChange" disabled="{{userInfo.birthday?true:false}}" mode="date" value="{{date}}">
                            <view class="picker">{{userInfo.birthday}}</view>
                        </picker>
                        <p class="weui-cells__title weui-cell_warn">生日保存成功后无法修改</p>
                    </view>
                </view>
            </view>
            <view class="btn-panel">
                <button class="weui-btn" formType="submit" type="primary">保存</button>
            </view>
        </form>
    </view>
</view>
