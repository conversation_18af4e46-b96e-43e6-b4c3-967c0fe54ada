Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=i(require("./../../npm/wepy/lib/wepy.js")),r=i(require("./../../api/basearea.js")),n=i(require("./../../api/auth.js"));function i(e){return e&&e.__esModule?e:{default:e}}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,o){try{var a=t[i](o),s=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(s).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(s)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(i){function u(){var e,t,r;a(this,u);for(var n=arguments.length,i=Array(n),c=0;c<n;c++)i[c]=arguments[c];return t=r=s(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(i))),r.data={url:"",title:"",id:"",videoList:null,current:0,displayMultipleItems:1,previousMargin:"0px",nextMargin:"80px",userInfo:!1,isLogin:!1,blackHidden:!0,isCheat:!1},r.methods={videoChange:function(e){var t=this;return o(regeneratorRuntime.mark((function r(){var n,i;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=e.currentTarget.dataset.index,(i=t.videoList[n]).video.url!=t.url&&(t.blackHidden=!1),t.url="",t.url=i.video.url,t.title=i.video.viewName,t.id=i.id,wx.setNavigationBarTitle({title:t.title}),t.eventLogVideo(t.title);case 9:case"end":return r.stop()}}),r,t)})))()},bindloadedmetadata:function(e){var t=this;return o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:setTimeout((function(){t.blackHidden=!0,t.$apply()}),500);case 1:case"end":return e.stop()}}),e,t)})))()}},r.config={},r.components={},s(r,t)}var c,l,f;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.page),e(u,[{key:"onLoad",value:(f=o(regeneratorRuntime.mark((function e(i){var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.default.checkCheat();case 2:return this.isCheat=e.sent,this.userInfo=t.default.getStorageSync("userInfo"),this.id=i.id,e.next=7,r.default.videos();case 7:for(o in this.videoList=e.sent,this.videoList.length>=5?this.displayMultipleItems=4:this.displayMultipleItems=this.videoList.length-1,this.videoList)this.videoList[o].id==this.id&&(this.url=this.videoList[o].video.url,this.title=this.videoList[o].video.viewName);wx.setNavigationBarTitle({title:this.title}),this.$apply();case 12:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"onShow",value:(l=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.default.getStorageSync("login_code");case 2:if(!e.sent){e.next=6;break}e.t0=!0,e.next=7;break;case 6:e.t0=!1;case 7:this.isLogin=e.t0,this.$apply();case 9:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"eventLogVideo",value:(c=o(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={vipId:this.isLogin&&this.userInfo?this.userInfo.id:0,viewName:t},e.next=3,r.default.eventLogE2(n);case 3:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})}]),u}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(u,"pages/basearea/video"));