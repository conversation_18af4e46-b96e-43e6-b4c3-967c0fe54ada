/**
 * 全面文件搜索
 * 搜索所有JS文件中的认证相关关键词
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始全面文件搜索...');

const appPath = '__APP__';

// 获取所有JS文件
function getAllJSFiles(dir) {
    const files = [];
    
    try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                files.push(...getAllJSFiles(fullPath));
            } else if (item.endsWith('.js')) {
                files.push(fullPath);
            }
        }
    } catch (error) {
        // 忽略错误
    }
    
    return files;
}

const jsFiles = getAllJSFiles(appPath);
console.log(`📊 找到 ${jsFiles.length} 个JS文件`);

// 扩展的关键词列表
const keywords = [
    // Token相关
    'refreshtoken', 'refresh_token', 'accesstoken', 'access_token',
    'authtoken', 'auth_token', 'bearertoken', 'bearer_token',
    'sessiontoken', 'session_token', 'apikey', 'api_key',
    
    // JWT相关
    'jwt', 'jwttoken', 'jwt_token', 'jsonwebtoken',
    
    // 认证相关
    'authorization', 'authenticate', 'credential',
    'login', 'logout', 'signin', 'signout',
    
    // 微信相关
    'wechat', 'weixin', 'wx', 'openid', 'unionid',
    'appid', 'appsecret', 'code2session',
    
    // API相关
    'getJwt', 'getJifenShopJwt', 'createJwt', 'refreshJwt',
    'getAuthData', 'setAuthData', 'checkSession',
    
    // 特定字符串
    'Member/getJwt', 'jifenCrm/createJwt', 'wechat/login',
    'authorized_token', 'third_session',
    
    // 其他可能的关键词
    'token', 'auth', 'session', 'secret', 'key'
];

console.log(`🔍 搜索 ${keywords.length} 个关键词...`);

const results = {};
let totalMatches = 0;

// 搜索每个关键词
for (const keyword of keywords) {
    console.log(`\n🔍 搜索: ${keyword}`);
    
    const keywordResults = [];
    
    for (const filePath of jsFiles) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            
            if (content.toLowerCase().includes(keyword.toLowerCase())) {
                // 找到匹配的行
                const lines = content.split('\n');
                const matchingLines = [];
                
                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].toLowerCase().includes(keyword.toLowerCase())) {
                        matchingLines.push({
                            lineNumber: i + 1,
                            line: lines[i].trim()
                        });
                        
                        // 限制每个文件最多显示3个匹配
                        if (matchingLines.length >= 3) break;
                    }
                }
                
                if (matchingLines.length > 0) {
                    keywordResults.push({
                        file: filePath,
                        matches: matchingLines
                    });
                }
            }
        } catch (error) {
            // 忽略读取错误
        }
    }
    
    if (keywordResults.length > 0) {
        results[keyword] = keywordResults;
        totalMatches += keywordResults.length;
        
        console.log(`  ✅ 找到 ${keywordResults.length} 个文件包含此关键词`);
        
        // 显示前几个匹配
        for (const result of keywordResults.slice(0, 3)) {
            console.log(`    📄 ${result.file}:`);
            for (const match of result.matches.slice(0, 2)) {
                console.log(`      ${match.lineNumber}: ${match.line}`);
            }
        }
    } else {
        console.log(`  ❌ 未找到 ${keyword}`);
    }
}

// 输出总结
console.log('\n' + '='.repeat(60));
console.log('📊 搜索结果总结');
console.log('='.repeat(60));

console.log(`\n📈 总体统计:`);
console.log(`  🔢 搜索关键词数: ${keywords.length}`);
console.log(`  ✅ 找到匹配的关键词: ${Object.keys(results).length}`);
console.log(`  📊 总匹配文件数: ${totalMatches}`);

if (Object.keys(results).length > 0) {
    console.log('\n🔍 关键发现:');
    
    // 按匹配数排序
    const sortedResults = Object.entries(results)
        .sort(([,a], [,b]) => b.length - a.length);
    
    for (const [keyword, matches] of sortedResults.slice(0, 10)) {
        console.log(`\n📌 "${keyword}" - ${matches.length} 个文件:`);
        
        for (const match of matches.slice(0, 5)) {
            console.log(`  📄 ${match.file}:`);
            for (const line of match.matches.slice(0, 1)) {
                console.log(`    ${line.lineNumber}: ${line.line}`);
            }
        }
    }
    
    // 特别关注认证相关的关键词
    const authKeywords = [
        'refreshtoken', 'refresh_token', 'getJwt', 'createJwt',
        'authorized_token', 'third_session', 'getAuthData',
        'authorization', 'jwt'
    ];
    
    console.log('\n🎯 认证相关关键发现:');
    let foundAuth = false;
    
    for (const keyword of authKeywords) {
        if (results[keyword]) {
            foundAuth = true;
            console.log(`\n🔑 "${keyword}":`);
            for (const match of results[keyword].slice(0, 3)) {
                console.log(`  📄 ${match.file}:`);
                for (const line of match.matches) {
                    console.log(`    ${line.lineNumber}: ${line.line}`);
                }
            }
        }
    }
    
    if (!foundAuth) {
        console.log('❌ 没有找到明显的认证相关关键词');
        console.log('💡 但找到了其他相关关键词，可能包含有用信息');
    }
    
} else {
    console.log('\n❌ 没有找到任何匹配的关键词');
    console.log('💡 可能的原因:');
    console.log('1. JS文件被压缩或混淆');
    console.log('2. 使用了不同的命名约定');
    console.log('3. 关键逻辑可能在其他文件中');
}

console.log('\n🎉 搜索完成！');
