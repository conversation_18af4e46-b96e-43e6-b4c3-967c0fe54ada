/**
 * API调试脚本
 * 测试正确的API地址和Token组合
 */

const https = require('https');

// 忽略SSL证书验证错误
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Token配置
const tokens = {
    authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI',
    loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY'
};

// API配置
const apiConfig = {
    baseUrl: 'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
    appId: 'wx489f950decfeb93e'
};

console.log('🚀 开始API调试...');
console.log('Base URL:', apiConfig.baseUrl);
console.log('App ID:', apiConfig.appId);

// 构建请求头
function buildHeaders(useAuth = true, useLogin = true) {
    const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
        'X-WX-AppId': apiConfig.appId
    };
    
    if (useAuth) {
        headers['Authorization'] = tokens.authorization;
    }
    
    if (useLogin) {
        headers['login_code'] = tokens.loginCode;
    }
    
    return headers;
}

// 发起请求
function makeRequest(path, headers) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, apiConfig.baseUrl);
        
        const options = {
            hostname: url.hostname,
            port: 443,
            path: url.pathname + url.search,
            method: 'GET',
            headers: headers,
            timeout: 10000,
            rejectUnauthorized: false // 忽略SSL证书错误
        };

        console.log(`\n📡 请求: ${url.toString()}`);
        console.log('请求头:', JSON.stringify(headers, null, 2));

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                console.log('响应头:', JSON.stringify(res.headers, null, 2));
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log('响应数据:', JSON.stringify(jsonData, null, 2));
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    console.log('原始响应:', data.substring(0, 500));
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error.message);
            reject(error);
        });

        req.on('timeout', () => {
            console.log('⏰ 请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

// 测试不同的认证组合
async function testAuthCombinations() {
    const testCases = [
        { name: '仅Authorization', useAuth: true, useLogin: false },
        { name: '仅login_code', useAuth: false, useLogin: true },
        { name: '两个Token都用', useAuth: true, useLogin: true },
        { name: '都不用', useAuth: false, useLogin: false }
    ];
    
    const testPath = '/garden/Gardenmemberinfo/getMemberInfo';
    
    for (const testCase of testCases) {
        console.log(`\n🧪 测试: ${testCase.name}`);
        try {
            const headers = buildHeaders(testCase.useAuth, testCase.useLogin);
            const result = await makeRequest(testPath, headers);
            
            if (result.status === 200 && result.data.err === 0) {
                console.log('✅ 成功！');
                return testCase;
            } else {
                console.log('❌ 失败');
            }
        } catch (error) {
            console.log('❌ 错误:', error.message);
        }
        
        // 等待1秒避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return null;
}

// 测试不同的API端点
async function testEndpoints() {
    const endpoints = [
        '/garden/Gardenmemberinfo/getMemberInfo',
        '/garden/sorghum/index',
        '/garden/sign/dailySign',
        '/member/info',
        '/user/profile',
        '/api/member/info'
    ];
    
    const headers = buildHeaders(true, true); // 使用所有Token
    
    for (const endpoint of endpoints) {
        console.log(`\n🎯 测试端点: ${endpoint}`);
        try {
            const result = await makeRequest(endpoint, headers);
            
            if (result.status === 200) {
                console.log('✅ 端点可访问');
                if (result.data.err === 0 || result.data.code === 0) {
                    console.log('🎉 数据获取成功！');
                }
            } else {
                console.log('❌ 端点不可访问');
            }
        } catch (error) {
            console.log('❌ 错误:', error.message);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

// 主函数
async function main() {
    try {
        console.log('\n=== 第一阶段: 测试认证组合 ===');
        const workingAuth = await testAuthCombinations();
        
        if (workingAuth) {
            console.log(`\n🎉 找到有效的认证方式: ${workingAuth.name}`);
        } else {
            console.log('\n❌ 没有找到有效的认证方式');
        }
        
        console.log('\n=== 第二阶段: 测试API端点 ===');
        await testEndpoints();
        
        console.log('\n✅ 调试完成！');
        
    } catch (error) {
        console.error('调试失败:', error);
    }
}

main();
