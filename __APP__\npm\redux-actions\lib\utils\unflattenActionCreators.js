exports.__esModule=!0,exports.default=function(a,s){var u=void 0===s?{}:s,n=u.namespace,i=void 0===n?e.DEFAULT_NAMESPACE:n,o=u.prefix;var c={};return Object.getOwnPropertyNames(a).forEach((function(e){var s=o?e.replace(""+o+i,""):e;return function e(s,u,n){var i=(0,t.default)(n.shift());(0,r.default)(n)?u[i]=a[s]:(u[i]||(u[i]={}),e(s,u[i],n))}(e,c,s.split(i))})),c};var e=require("./../constants.js"),r=a(require("./isEmpty.js")),t=a(require("./camelCase.js"));function a(e){return e&&e.__esModule?e:{default:e}}