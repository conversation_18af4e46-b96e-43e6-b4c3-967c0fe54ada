Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),r=(o(require("./../../api/auth.js")),o(require("./../../mixins/base.js"))),n=o(require("./../../api/product.js"));o(require("./../../utils/Tips.js")),o(require("./../../utils/WxUtils.js"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(o){function u(){var e,t,n;i(this,u);for(var o=arguments.length,c=Array(o),s=0;s<o;s++)c[s]=arguments[s];return t=n=a(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(c))),n.data={productImageHeight:"100px",detail:{},productInfo:null,init:!1,getInfo:null,page:{list:[]},commentInit:!1,isShow:!0},n.methods={previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})}},n.computed={},n.components={},n.mixins=[r.default],n.config={navigationBarTitleText:"产品详情",navigationBarBackgroundColor:"#f3f3f3",navigationBarTextStyle:"black"},a(n,t)}var c,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.page),e(u,[{key:"onLoad",value:(c=regeneratorRuntime.mark((function e(t){var r,o=t.productErpNo,i=t.productDate;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.productImageWidth=wx.getSystemInfoSync().windowWidth,this.productImageHeight=.8*this.productImageWidth*1/1+"px",this.productErpNo=o,e.next=5,n.default.infoByErpNo(o,i);case 5:this.detail=e.sent,r=this.detail.net_content,this.detail.net_content=parseInt(r),this.loaded();case 9:case"end":return e.stop()}}),e,this)})),s=function(){var e=c.apply(this,arguments);return new Promise((function(t,r){return function n(o,i){try{var a=e[o](i),u=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(u)}("next")}))},function(e){return s.apply(this,arguments)})},{key:"onUnload",value:function(){}},{key:"params",value:function(){return{status:"ALL",productErpNo:this.productErpNo}}}]),u}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(u,"pages/anticounterfeiting/detail"));