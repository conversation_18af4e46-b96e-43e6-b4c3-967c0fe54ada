Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),n=i(require("./../npm/wepy/lib/wepy.js")),o=i(require("./../utils/ForumCommentPage.js")),u=i(require("./../utils/ForumHttp.js")),a=i(require("./../utils/DateUtils.js"));function i(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=(t=e=function(e){function t(){return f(this,t),l(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}var n,i;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,u.default),r(t,null,[{key:"comments",value:(n=regeneratorRuntime.mark((function e(t){var r,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.forumBaseUrl+"/forum/topic/details/comment/"+t,n&&(r=this.forumBaseUrl+"/forum/topic/comment/details/all/"+t),e.abrupt("return",new o.default(r,(function(e){return e.formatCreateDate=a.default.formatDate(e.createDate),e})));case 3:case"end":return e.stop()}}),e,this)})),i=function(){var e=n.apply(this,arguments);return new Promise((function(t,r){return function n(o,u){try{var a=e[o](u),i=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(i)}("next")}))},function(e){return i.apply(this,arguments)})}]),t}(),e.forumBaseUrl=n.default.$instance.globalData.forumBaseUrl,t);exports.default=s;