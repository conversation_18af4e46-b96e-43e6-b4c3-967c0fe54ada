Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./base.js")),r=n(require("./../utils/Page.js"));function n(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(n){function u(){return s(this,u),a(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}var i,o;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default),e(u,null,[{key:"page",value:function(){var e=this.baseUrl+"/addresses";return new r.default(e,this._processAddress.bind(this))}},{key:"available",value:function(e){var t=this,r=this.baseUrl+"/addresses/available";return this.post(r,e).then((function(e){return e.map(t._processAddress.bind(t))})).then((function(e){var t=[],r=[];return e.forEach((function(e){e.available?t.push(e):r.push(e)})),{available:t,disable:r}}))}},{key:"defaultAvailable",value:(i=regeneratorRuntime.mark((function e(t){var r,n,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.available(t);case 2:if(r=e.sent,!(null==(n=r.available)||n.length<1)){e.next=6;break}return e.abrupt("return",null);case 6:if(!(s=n.find((function(e){return 1==e.isDefault})))){e.next=11;break}return e.abrupt("return",this._processAddress(s));case 11:return e.abrupt("return",this._processAddress(n[0]));case 12:case"end":return e.stop()}}),e,this)})),o=function(){var e=i.apply(this,arguments);return new Promise((function(t,r){return function n(s,a){try{var u=e[s](a),i=u.value}catch(e){return void r(e)}if(!u.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(i)}("next")}))},function(e){return o.apply(this,arguments)})},{key:"save",value:function(e){var t=this.baseUrl+"/addresses";return this.post(t,e)}},{key:"update",value:function(e,t){var r=this.baseUrl+"/addresses/"+e;return this.put(r,t)}},{key:"info",value:function(e){var t=this.baseUrl+"/addresses/"+e;return this.get(t,u).then((function(e){return e.location=e.fullAddress.replace(e.detail,""),e}))}},{key:"setDefault",value:function(e){var t=this.baseUrl+"/addresses/"+e+"/default";return this.put(t)}},{key:"remove",value:function(e){var t=this.baseUrl+"/addresses/"+e;return this.delete(t)}},{key:"_processAddress",value:function(e){if(e){var t=e.fullAddress,r=e.province,n=e.city,s=e.country;e.simpleAddress=t.replace(r,"").replace(n,"").replace(s,""),e.sexText="",1==e.sex?e.sexText="先生":2==e.sex&&(e.sexText="女士")}return e}}]),u}();exports.default=u;