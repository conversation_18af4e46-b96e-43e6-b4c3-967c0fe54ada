/**
 * 简化的生产环境API测试
 * 专门测试login_code的长期有效性
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Token配置
const tokens = {
    authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI',
    loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY'
};

console.log('🚀 开始测试生产环境API...');

// 解析Token信息
function decodeJWT(token) {
    try {
        const parts = token.split('.');
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        return payload;
    } catch (e) {
        return null;
    }
}

const authPayload = decodeJWT(tokens.authorization);
const loginPayload = decodeJWT(tokens.loginCode);

console.log('\n📊 Token信息:');
console.log('会员ID:', authPayload.memberInfo.id);
console.log('UnionID:', loginPayload.unionid);
console.log('Authorization过期:', new Date(authPayload.expireTime * 1000).toLocaleString('zh-CN'));
console.log('Login Code过期:', new Date(loginPayload.expire * 1000).toLocaleString('zh-CN'));

// 测试配置
const testConfigs = [
    {
        name: '仅使用Authorization',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'Authorization': tokens.authorization,
            'X-WX-AppId': 'wx489f950decfeb93e'
        }
    },
    {
        name: '仅使用login_code',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'login_code': tokens.loginCode,
            'X-WX-AppId': 'wx489f950decfeb93e'
        }
    },
    {
        name: '两个Token都使用',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'Authorization': tokens.authorization,
            'login_code': tokens.loginCode,
            'X-WX-AppId': 'wx489f950decfeb93e'
        }
    }
];

// 发起请求
function makeRequest(url, headers) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: 443,
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            headers: headers,
            timeout: 10000,
            rejectUnauthorized: false
        };

        console.log(`\n📡 请求: ${url}`);
        console.log('认证方式:', headers.Authorization ? 'Authorization' : '', headers.login_code ? 'login_code' : '');

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log('响应:', JSON.stringify(jsonData, null, 2));
                    resolve({ 
                        status: res.statusCode, 
                        data: jsonData,
                        success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0)
                    });
                } catch (e) {
                    console.log('原始响应:', data.substring(0, 300));
                    resolve({ 
                        status: res.statusCode, 
                        data: data,
                        success: false
                    });
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error.message);
            reject(error);
        });

        req.on('timeout', () => {
            console.log('⏰ 请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

// 主测试函数
async function main() {
    // 测试URL列表
    const testUrls = [
        'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
        'https://wap.exijiu.com/index.php/API/garden/sorghum/index',
        'https://wap.exijiu.com/index.php/API/garden/sign/dailySign',
        'https://apiforum.exijiu.com/api/member/info',
        'https://apimallwm.exijiu.com/api/member/profile',
        'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2/garden/Gardenmemberinfo/getMemberInfo'
    ];
    
    console.log('\n=== 开始测试不同的认证方式 ===');
    
    for (const config of testConfigs) {
        console.log(`\n🧪 测试配置: ${config.name}`);
        
        for (const url of testUrls) {
            try {
                const result = await makeRequest(url, config.headers);
                
                if (result.success) {
                    console.log('✅ 成功！找到有效配置');
                    console.log(`🎉 有效URL: ${url}`);
                    console.log(`🔑 有效认证: ${config.name}`);
                    return { url, config: config.name, result };
                } else if (result.status === 200) {
                    console.log('⚠️ 可访问但有业务错误');
                } else {
                    console.log('❌ 不可访问');
                }
            } catch (error) {
                console.log('❌ 网络错误:', error.message);
            }
            
            // 短暂等待
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    console.log('\n❌ 没有找到有效的API配置');
    console.log('\n💡 建议:');
    console.log('1. 检查Token是否真的有效');
    console.log('2. 可能需要特殊的请求头或参数');
    console.log('3. API可能需要POST方法而不是GET');
    console.log('4. 可能需要在微信环境中才能访问');
}

main().catch(console.error);
