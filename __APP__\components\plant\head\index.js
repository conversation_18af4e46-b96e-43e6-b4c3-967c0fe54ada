Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=require("./../../../npm/wepy/lib/wepy.js"),o=(e=t)&&e.__esModule?e:{default:e};function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function t(){var e,o,i;n(this,t);for(var s=arguments.length,u=Array(s),f=0;f<s;f++)u[f]=arguments[f];return o=i=r(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(u))),i.props={rankInfo:{rank:""},showLoginCover:!1,showRegisterCover:!1,showIproveCover:!1,userInfo:{}},i.methods={gotoWithLogined:function(e){this.rankInfo.rank&&this.$emit("gotoWithLogined",e)},gotoImprove:function(e){this.$emit("gotoImprove",e)},login:function(e){console.log(123),this.$emit("login",e)},getPhoneNumber:function(e){this.$emit("getPhoneNumber",e)}},r(i,o)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,o.default.component),t}();exports.default=i;