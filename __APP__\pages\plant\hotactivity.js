Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),n=(o(require("./../../api/member.js")),o(require("./../../api/duanwu.js")),o(require("./../../mixins/base.js"))),r=o(require("./../../components/common/loading.js")),i=(o(require("./../../utils/Tips.js")),o(require("./../../api/garden.js")));function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=function(o){function c(){var e,t,i;a(this,c);for(var o=arguments.length,s=Array(o),p=0;p<o;p++)s[p]=arguments[p];return t=i=u(this,(e=c.__proto__||Object.getPrototypeOf(c)).call.apply(e,[this].concat(s))),i.data={init:!1,hotActivity:""},i.methods={},i.mixins=[n.default],i.$repeat={},i.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},i.$events={},i.components={Loading:r.default},i.config={navigationBarBackgroundColor:"#71B5FA",navigationBarTitleText:"活动介绍"},u(i,t)}var s,p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(c,t.default.page),e(c,[{key:"onLoad",value:(s=regeneratorRuntime.mark((function e(t){var n,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.id,e.next=3,i.default.hotActivity(n);case 3:r=e.sent,this.hotActivity=r,this.loaded();case 6:case"end":return e.stop()}}),e,this)})),p=function(){var e=s.apply(this,arguments);return new Promise((function(t,n){return function r(i,o){try{var a=e[i](o),u=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(u).then((function(e){r("next",e)}),(function(e){r("throw",e)}));t(u)}("next")}))},function(e){return p.apply(this,arguments)})}]),c}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(c,"pages/plant/hotactivity"));