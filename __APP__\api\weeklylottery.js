Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./base.js")),r=n(require("./../utils/PageForMallwm.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(n){function u(){return o(this,u),i(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default),e(u,null,[{key:"list",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/index";return this.get(t,e).then((function(e){return e}))}},{key:"detail",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/detail";return this.get(t,{id:e}).then((function(e){return e}))}},{key:"confirm",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/confirm";return this.post(t,{lottery_id:e}).then((function(e){return e}))}},{key:"participants",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/participants";return this.get(t,{lottery_id:e}).then((function(e){return e}))}},{key:"record",value:function(){var e=this.jifenShopApiUrl+"/weekly_lottery/lottery/record";return new r.default(e)}},{key:"returnPoints",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/return_points";return this.post(t,{record_id:e}).then((function(e){return e}))}},{key:"receive",value:function(e){var t=this.jifenShopApiUrl+"/weekly_lottery/lottery/receive";return this.post(t,{record_id:e}).then((function(e){return e}))}}]),u}();exports.default=u;