Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./../npm/wepy/lib/wepy.js")),r=n(require("./../utils/Tips.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(o,a){try{var i=t[o](a),s=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(s).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(s)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(n){function s(){var e,t,r;a(this,s);for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return t=r=i(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(o))),r.config={},r.components={},r.methods={},r.events={},r.data={isPageLoading:!1,isPageEmpty:!1,isPageReachBottom:!1},i(r,t)}var u,c,p,f,l;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,t.default.mixin),e(s,[{key:"replayPostNext",value:(l=o(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!this.replayPostPage.reachBottom){e.next=3;break}return e.abrupt("return");case 3:return this.isPageLoading=!0,t=this.params?this.params():{},e.next=7,this.replayPostPage.replayPostNext(t);case 7:this.isPageReachBottom=this.replayPostPage.reachBottom,this.isPageEmpty=0==this.replayPostPage.list.length,this.onPageLoad&&this.onPageLoad();case 10:return e.prev=10,this.isPageLoading=!1,this.init=!0,this.$apply(),r.default.loaded(),e.finish(10);case 16:case"end":return e.stop()}}),e,this,[[0,,10,16]])}))),function(){return l.apply(this,arguments)})},{key:"onReachBottom",value:(f=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.replayPostNext();case 2:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"reload",value:(p=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.replayPostPage||!this.replayPostPage.reset){e.next=5;break}return this.replayPostPage.reset(),e.next=4,this.replayPostNext();case 4:t.default.stopPullDownRefresh();case 5:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"onPullDownRefresh",value:(c=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.reload();case 2:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"update",value:(u=o(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.reload();case 2:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})}]),s}();exports.default=s;