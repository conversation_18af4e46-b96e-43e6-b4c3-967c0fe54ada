Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),r=(n(require("./base.js")),n(require("./../utils/Page.js")),n(require("./../utils/Pages.js")),n(require("./../utils/BoostorHttp.js"))),t=n(require("./../utils/DateUtils.js"));n(require("./auth.js")),n(require("./member.js")),n(require("./../npm/wepy/lib/wepy.js"));function n(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(u,a){try{var i=r[u](a),o=i.value}catch(e){return void t(e)}if(!i.done)return Promise.resolve(o).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(o)}("next")}))}}function a(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function i(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var o=function(n){function o(){return a(this,o),i(this,(o.__proto__||Object.getPrototypeOf(o)).apply(this,arguments))}var s,c,p,h,f,g,m,d,l,y,v,w,k,R,b,x,_,G,q,P,j,S,I,T,M,U,D,E,O,A,F,W,B,C,V,z,N,H,L,Q,J,K,X,Y,Z,$,ee,re,te,ne,ue,ae,ie,oe,se,ce,pe,he,fe;return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(o,r.default),e(o,null,[{key:"login",value:(fe=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/wechat/login",e.abrupt("return",this.custom({url:t,method:"GET",data:r},!0).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return fe.apply(this,arguments)})},{key:"getAuth",value:(he=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/wechat/auth",e.abrupt("return",this.custom({url:t,method:"GET",data:r},!0).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return he.apply(this,arguments)})},{key:"getPhone",value:(pe=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/wechat/register",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return pe.apply(this,arguments)})},{key:"notice",value:(ce=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/notice/index").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return ce.apply(this,arguments)})},{key:"memberInfo",value:(se=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenmemberinfo/getMemberInfo",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return se.apply(this,arguments)})},{key:"rank",value:(oe=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/Gardenmemberinfo/getGardenWineProvinceRank",e.abrupt("return",this.custom({url:t,method:"GET",data:r},!0).then((function(e){return e.data.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return oe.apply(this,arguments)})},{key:"Introduction",value:(ie=u(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={id:1,name:"1",content:"1"},e.abrupt("return",this.get("/garden/Notice/introduction",r).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(){return ie.apply(this,arguments)})},{key:"dailySign",value:(ae=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sign/dailySign",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return ae.apply(this,arguments)})},{key:"hotActivity",value:(ue=u(regeneratorRuntime.mark((function e(r){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log(r),t={},!r){e.next=5;break}return n="/garden/Notice/hotActivity?id="+r,e.abrupt("return",this.get(n,t).then((function(e){return e})));case 5:return e.abrupt("return",this.get("/garden/Notice/hotActivity").then((function(e){return e})));case 7:case"end":return e.stop()}}),e,this)}))),function(e){return ue.apply(this,arguments)})},{key:"poptips",value:(ne=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/poptips/random",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return ne.apply(this,arguments)})},{key:"realScene",value:(te=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/notice/realScene").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return te.apply(this,arguments)})},{key:"reward",value:(re=u(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/garden/realscene/reward",e.abrupt("return",this.custom({url:r,method:"GET"},!0).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return re.apply(this,arguments)})},{key:"Gardenquestiontask",value:(ee=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenquestiontask/index").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return ee.apply(this,arguments)})},{key:"answerResults",value:($=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/Gardenquestiontask/answerResults",e.abrupt("return",this.custom({url:t,method:"GET",data:r}).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return $.apply(this,arguments)})},{key:"goods",value:(Z=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/goods?tags=garden_recommend_product").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return Z.apply(this,arguments)})},{key:"dailyShare",value:(Y=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/gardenmemberinfo/dailyShare").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return Y.apply(this,arguments)})},{key:"getDetailByMemberId",value:(X=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/getDetailByMemberId",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return X.apply(this,arguments)})},{key:"getSorghumByMemberId",value:(K=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/sorghum/index",r).then((function(e){return e.forEach((function(e){10!=e.status&&11!=e.status||(e.formatPlantDate=t.default.plantformatDate(e.crop_time))})),e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return K.apply(this,arguments)})},{key:"seeds",value:(J=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/seed",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return J.apply(this,arguments)})},{key:"watering",value:(Q=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/watering",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return Q.apply(this,arguments)})},{key:"manuring",value:(L=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/manuring",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return L.apply(this,arguments)})},{key:"harvest",value:(H=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/harvest",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return H.apply(this,arguments)})},{key:"extend",value:(N=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/sorghum/extend",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return N.apply(this,arguments)})},{key:"can_i_extend",value:(z=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/sorghum/can_i_extend",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return z.apply(this,arguments)})},{key:"wineList",value:(V=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenmemberwine/index",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return V.apply(this,arguments)})},{key:"dischargeGrain",value:(C=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenmemberwine/dischargeGrain",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return C.apply(this,arguments)})},{key:"harvestWine",value:(B=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/Gardenmemberwine/harvestWine",e.abrupt("return",this.custom({url:t,method:"GET",data:r}).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return B.apply(this,arguments)})},{key:"stealWine",value:(W=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/Gardenmemberwine/stealWine",e.abrupt("return",this.custom({url:t,method:"GET",data:r}).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return W.apply(this,arguments)})},{key:"exchange",value:(F=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenjifenshop/exchange",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return F.apply(this,arguments)})},{key:"stealRecord",value:(A=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Gardenmemberwine/stealRecord",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return A.apply(this,arguments)})},{key:"helpRecord",value:(O=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/sorghum/friendHelpManureRecord",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return O.apply(this,arguments)})},{key:"address",value:(E=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/address",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return E.apply(this,arguments)})},{key:"ImproveInfo",value:(D=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/member/member/"+r.id,e.abrupt("return",this.custom({url:t,method:"PUT",data:r}).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return D.apply(this,arguments)})},{key:"getImprovedInfo",value:(U=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="/member/member/"+r.id,e.abrupt("return",this.get(t,r.id).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return U.apply(this,arguments)})},{key:"tasks",value:(M=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/tasks/index").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return M.apply(this,arguments)})},{key:"addFriendToken",value:(T=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/friends/addFriendToken",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return T.apply(this,arguments)})},{key:"addFriend",value:(I=u(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/garden/friends/add",e.abrupt("return",this.custom({url:t,method:"POST",data:r},!0).then((function(e){return e.data})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return I.apply(this,arguments)})},{key:"friend_list",value:(S=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/friends/index",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return S.apply(this,arguments)})},{key:"read",value:(j=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/lottery/read",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return j.apply(this,arguments)})},{key:"beforeDraw",value:(P=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/lottery/beforeDraw",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return P.apply(this,arguments)})},{key:"draw",value:(q=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/lottery/draw",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return q.apply(this,arguments)})},{key:"lotteryRecord",value:(G=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/lottery/lotteryRecord",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return G.apply(this,arguments)})},{key:"remainFreeDrawChance",value:(_=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/lottery/remainFreeDrawChance",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return _.apply(this,arguments)})},{key:"lotteryAmount",value:(x=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/lottery/lotteryAmount").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return x.apply(this,arguments)})},{key:"lotteryRecordRoll",value:(b=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Lottery/lotteryRoll").then((function(e){for(var r=0;r<e.length;r++)e[r].formatCreateDate=t.default.formatDate(e[r].create_time);return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return b.apply(this,arguments)})},{key:"subscribeMessageStatus",value:(R=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/gardenmemberinfo/subscribeMessageStatus",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return R.apply(this,arguments)})},{key:"subscribeMessage",value:(k=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/gardenmemberinfo/subscribeMessage",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"getUserPianqu",value:(w=u(regeneratorRuntime.mark((function e(r){var t,n,u,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.province,n=r.city,u=r.district,a="/garden/notice/getPianquActivity?province="+t+"&city="+n+"&district="+u,e.abrupt("return",this.post(a,r).then((function(e){return e})));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return w.apply(this,arguments)})},{key:"gitQuestiontask",value:(v=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/wcrd/question/get").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return v.apply(this,arguments)})},{key:"answerQuestion",value:(y=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/wcrd/question/answer",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"isShareFrend",value:(l=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/wcrd/question/wcrdMemberInfo").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"shareFrends",value:(d=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/wcrd/question/share").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"receiveInte",value:(m=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/wcrd/question/getPrize").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return m.apply(this,arguments)})},{key:"agreeRule",value:(g=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/wcrd/question/agree").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return g.apply(this,arguments)})},{key:"banners",value:(f=u(regeneratorRuntime.mark((function e(r,t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="/banners",r&&(n="/banners?type="+r),e.abrupt("return",this.get(n,t).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e,r){return f.apply(this,arguments)})},{key:"onclickcollection",value:(h=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/Sorghum/harvestAll").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"getValidateInfo",value:(p=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/slide_validate/getValidateInfo").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"toValidate",value:(c=u(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/garden/slide_validate/toValidate",r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"getSubscribePrize",value:(s=u(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/garden/tasks/getSubscribePrize").then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})}]),o}();exports.default=o;