Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),r=require("./../../npm/wepy/lib/wepy.js"),i=(e=r)&&e.__esModule?e:{default:e};function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var o=function(e){function r(){var e,t,i;a(this,r);for(var o=arguments.length,u=Array(o),s=0;s<o;s++)u[s]=arguments[s];return t=i=n(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(u))),i.props={mode:{type:String,value:"date",observer:"modeChange"},disabled:{type:Boolean,value:!1},placeholder:String},i.data={pickerArray:[],pickerIndex:[],dateString:""},i.methods={modeChange:function(e,t){this.getPickerArray(e)},pickerChange:function(e){this.dateString=this.getPickerValue(e.detail.value);var t={value:this.dateString};this.$emit("onPickerChange",t),this.$apply()},pickerColumnChange:function(e){var t=e.detail,r=this.pickerArray[0][this.pickerIndex[0]].id;if("time"!==this.mode&&1===t.column){for(var i=this._getNumOfDays(r,t.value+1),a=[],n=1;n<=i;n++)a.push({id:n,name:n+"日"});this.pickerArray[2]=a,this.$apply()}},pickerCancel:function(e){}},i.events={},n(i,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,i.default.component),t(r,[{key:"getPickerValue",value:function(e){var t=this,r=this.data.pickerArray.map((function(r,i){return t.data.pickerArray[i][e[i]].id}));return this.formateDateTime(r)}},{key:"formateDateTime",value:function(e){switch(this.data.mode){case"date":return e.map(this.formatNumber).join("-");case"time":return e.map(this.formatNumber).join(":");case"dateTime":return e.slice(0,3).map(this.formatNumber).join("-")+" "+e.slice(3,6).map(this.formatNumber).join(":")}}},{key:"_getNumOfDays",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return new Date(e,t,r).getDate()}},{key:"formatNumber",value:function(e){return(e=e.toString())[1]?e:"0"+e}},{key:"getPickerArray",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.data.mode,t=new Date,r=[],i=[],a=t.getFullYear()-5;a<=t.getFullYear()+5;a++)i.push({id:a,name:a+"年"});var n=t.getFullYear(),o=i.findIndex((function(e){return e.id==n}));r.push({picker:"year",value:i,pickerIndex:o});for(var u=[],s=1;s<=12;s++)u.push({id:s,name:s+"月"});r.push({picker:"month",value:u,pickerIndex:t.getMonth()});for(var c=this._getNumOfDays(t.getFullYear(),t.getMonth()+1),p=[],l=1;l<=c;l++)p.push({id:l,name:l+"日"});r.push({picker:"day",value:p,pickerIndex:t.getDate()-1});for(var h=[],f=0;f<=23;f++)h.push({id:f,name:this.formatNumber(f)});r.push({picker:"time",value:h,pickerIndex:t.getHours()});for(var d=[],m=0;m<=59;m++)d.push({id:m,name:this.formatNumber(m)});r.push({picker:"minutes",value:d,pickerIndex:t.getMinutes()});for(var y=[],v=0;v<=59;v++)y.push({id:v,name:this.formatNumber(v)});r.push({picker:"seconds",value:y,pickerIndex:t.getSeconds()});var k=[],g=function(){switch(e){case"date":return["year","month","day"];case"dateTime":return["year","month","day","time","minutes","seconds"];case"time":return["time","minutes","seconds"]}},b=g(e),x=[];r.map((function(e){b.indexOf(e.picker)>=0&&(k.push(e.pickerIndex),x.push(e.value))})),this.pickerArray=x,this.pickerIndex=k,this.dateString=this.getPickerValue(k),this.$apply()}},{key:"onLoad",value:function(){this.getPickerArray()}}]),r}();exports.default=o;