Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),r=u(require("./../../mixins/router.js")),t=u(require("./../../npm/wepy/lib/wepy.js")),n=u(require("./../../api/order.js")),a=u(require("./../../utils/Sku.js")),o=u(require("./../../utils/Cart.js"));function u(e){return e&&e.__esModule?e:{default:e}}function i(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(a,o){try{var u=r[a](o),i=u.value}catch(e){return void t(e)}if(!u.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}function l(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function s(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var c=function(u){function c(){var e,t,u;l(this,c);for(var f=arguments.length,p=Array(f),d=0;d<f;d++)p[d]=arguments[d];return t=u=s(this,(e=c.__proto__||Object.getPrototypeOf(c)).call.apply(e,[this].concat(p))),u.props={detail:{}},u.data={isFav:!1,state:"bargain",id:""},u.skuManager=null,u.cartManager=o.default.create(),u.methods={buy:function(){var e=this;return i(regeneratorRuntime.mark((function r(){var t,a,o,u,i,l,s;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(e.sku.next){r.next=2;break}return r.abrupt("return");case 2:t=e.sku.num,(a=e.detail.rule.goods).originalPrice=e.detail.rule.skuDetail.price,a.sellPrice=e.detail.balance,null,o=e.cartManager.createCart(a,null,t),u=n.default.createCartTrade([o],{orderType:10}),i=e.detail,l=i.id,s=i.ruleId,Object.assign(u,{ruleId:s,id:l}),e.$root.$preload("params",{trade:u,type:e.state}),e.$root.$navigate("../order/trade"),e.clear();case 14:case"end":return r.stop()}}),r,e)})))()}},u.computed={sku:function(){if(null!=this.skuManager)return this.skuManager.export()}},u.watch={detail:function(e,r){var t=this;return i(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(null==r||null==r.id){n.next=2;break}return n.abrupt("return");case 2:t.skuManager=new a.default(e.rule.goods),t.skuManager.action=t.state,t.$apply();case 5:case"end":return n.stop()}}),n,t)})))()}},u.mixins=[r.default],s(u,t)}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(c,t.default.component),e(c,[{key:"clear",value:function(){console.info("[SilderPanel] clear"),this.goods=null,this.action="cart",this.display=!1}}]),c}();exports.default=c;