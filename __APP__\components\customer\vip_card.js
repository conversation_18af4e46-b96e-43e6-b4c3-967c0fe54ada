var e;Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t=s(require("./../../npm/wepy/lib/wepy.js")),r=require("./../../npm/wepy-redux/lib/index.js"),n=s(require("./../../store/utils.js")),i=s(require("./../../mixins/base.js"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=(0,r.connect)({member:n.default.get("member"),card:n.default.get("card"),user:n.default.get("user")})(e=function(e){function r(){var e,t,n;u(this,r);for(var s=arguments.length,a=Array(s),l=0;l<s;l++)a[l]=arguments[l];return t=n=o(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(a))),n.props={isDetail:{default:"true"}},n.methods={regist:function(){this.$root.$navigate("/pages/customer/registe_member")},detail:function(){"false"!=this.isDetail&&this.$root.$navigate("/pages/customer/vip_detail")},info:function(){this.$root.$navigate("/pages/customer/vip_info")}},n.computed={avatarUrl:function(){return null==this.user||null==this.user.avatarUrl?"http://img.leshare.shop/shop/other/customer_white.png":this.user.avatarUrl},nickName:function(){return null==this.user||null==this.user.nickName?"微信用户":this.user.nickName},levelName:function(){return this.member&&this.card&&1==this.card.supplyDiscount&&this.member.discountRule?this.member.discountRule.levelName:"普通会员"},parsentText:function(){var e=this.card&&this.card.memberPresentRules&&this.card.memberPresentRules.length>0,t=this.member&&this.member.memberPresentDetails&&this.member.memberPresentDetails.length>0;if(!e||!t)return"";var r=this.card.memberPresentRules[0].unit;return e&&t?"当前已累计"+this.member.memberPresentDetails[0].currentCount+r:"当前已累计0"+r},numberText:function(){if(null!=this.member&&null!=this.member.memberNumber){for(var e=this.member.memberNumber,t="",r=0;r<e.length;r++)t+=e[r],(r+1)%4==0&&(t+=" ");return t}},discountText:function(){if(null!=this.member&&null!=this.member.discountRule){var e=this.member,t=e.discountRule,r=e.customDiscount,n=null!=r&&r>0&&r<=100?r:t.discount;if(n<100&&n>0){var i=(n/10).toFixed(1);return"持卡享受"+i+"折优惠"}}}},n.mixins=[i.default],o(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,t.default.component),r}())||e;exports.default=a;