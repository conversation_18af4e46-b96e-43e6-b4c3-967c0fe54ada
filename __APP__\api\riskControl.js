Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./base.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function r(){return o(this,r),u(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default),t(r,null,[{key:"scanQrcode",value:function(e){var t=this.baseUrl+"/RiskControl/scanQrcode";return this.post(t,e).then((function(e){return e}))}},{key:"register",value:function(e){var t=this.baseUrl+"/RiskControl/register";return this.post(t,e).then((function(e){return e}))}},{key:"saveBlackBox",value:function(e){var t=this.baseUrl+"/RiskControl/saveBlackBox";return this.post(t,e).then((function(e){return e}))}}]),r}();exports.default=i;