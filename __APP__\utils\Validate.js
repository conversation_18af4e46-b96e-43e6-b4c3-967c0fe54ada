Object.defineProperty(exports,"__esModule",{value:!0});var t=function(){function t(t,e){for(var n=0;n<e.length;n++){var u=e[n];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(e,n,u){return n&&t(e.prototype,n),u&&t(e,u),e}}();var e=function(){function e(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e)}return t(e,null,[{key:"required",value:function(t){if("number"==typeof t)t=t.toString();else if("boolean"==typeof t)return!0;return t&&t.length>0}},{key:"noDuplicate",value:function(t){for(var e=0;e<t.length;e++)for(var n=0;n<t.length;n++)if(t[e]==t[n]&&e!=n)return!1;return!0}},{key:"email",value:function(t){return this.optional(t)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)}},{key:"tel",value:function(t){return this.optional(t)||/^1[34578]\d{9}$/.test(t)}},{key:"url",value:function(t){return this.optional(t)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(t)}},{key:"date",value:function(t){return this.optional(t)||!/Invalid|NaN/.test(new Date(t).toString())}},{key:"dateISO",value:function(t){return this.optional(t)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(t)}},{key:"number",value:function(t){return this.optional(t)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(t)}},{key:"digits",value:function(t){return this.optional(t)||/^\d+$/.test(t)}},{key:"idcard",value:function(t){return this.optional(t)||/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(t)}},{key:"equalTo",value:function(t,e){return this.optional(t)||t===that.scope.detail.value[e]}},{key:"contains",value:function(t,e){return this.optional(t)||t.indexOf(e)>=0}},{key:"minlength",value:function(t,e){return this.optional(t)||t.length>=e}},{key:"maxlength",value:function(t,e){return this.optional(t)||t.length<=e}},{key:"rangelength",value:function(t,e){return this.optional(t)||t.length>=e[0]&&t.length<=e[1]}},{key:"min",value:function(t,e){return this.optional(t)||Number(t)>=Number(e)}},{key:"max",value:function(t,e){return this.optional(t)||Number(t)<=Number(e)}},{key:"after",value:function(t,e){return this.optional(t)||t>=e}},{key:"before",value:function(t,e){return this.optional(t)||t<=e}},{key:"range",value:function(t,e){return this.optional(t)||t>=e[0]&&t<=e[1]}},{key:"optional",value:function(t){return!this.required(t)&&"dependency-mismatch"}},{key:"money",value:function(t){return this.optional(t)||/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(t)}},{key:"special",value:function(t){return this.optional(t)||!/^(?=[@#$￥%^&*]+$)/.test(t)}},{key:"specialpassword",value:function(t){return this.optional(t)||/^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])).*$/.test(t)}}]),e}();exports.default=e;