Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),t=c(require("./../../npm/wepy/lib/wepy.js")),r=(c(require("./../../api/auth.js")),c(require("./../../mixins/base.js"))),a=c(require("./../../mixins/input.js")),n=c(require("./../../api/product.js")),o=c(require("./../../components/weui/tips.js")),i=c(require("./../../utils/Tips.js")),u=(c(require("./../../utils/WxUtils.js")),c(require("./../../components/common/loading.js")));function c(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function a(n,o){try{var i=t[n](o),u=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(u).then((function(e){a("next",e)}),(function(e){a("throw",e)}));e(u)}("next")}))}}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=function(c){function l(){var e,c,p;f(this,l);for(var b=arguments.length,h=Array(b),m=0;m<b;m++)h[m]=arguments[m];return c=p=d(this,(e=l.__proto__||Object.getPrototypeOf(l)).call.apply(e,[this].concat(h))),p.data={init:!1,showModal:!1,feedbackphone:"************",imageUrl:["http://wap.exijiu.cn/Public/Qrcode/images/telQuery.jpg","http://wap.exijiu.cn/Public/Qrcode/images/messageQuery.jpg","http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(8).jpg"],page:{list:[]},mode:"create",feedback:{}},p.methods={phonefeedback:function(){this.setData({showModal:!0})},preventTouchMove:function(){},gos:function(){this.setData({showModal:!1})},feedbackphone:function(){wx.makePhoneCall({phoneNumber:this.data.feedbackphone})},previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})},confirm:function(e){var r=this,a=e.detail;return s(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object.assign(r.feedback,a.value),0!=r.data.feedback.desc){e.next=5;break}i.default.confirm("您好！请输入您要咨询的问题。"),e.next=44;break;case 5:if(!/^(?=[@#$￥%^&*]+$)/.test(r.data.feedback.desc)){e.next=9;break}i.default.confirm("您好！您输入要咨询的问题中含有特殊字符（如：@，#、￥、%、&、*、^、&、*请从新输入"),e.next=44;break;case 9:if(0!=r.data.feedback.name){e.next=13;break}i.default.confirm("您好！请输入您的姓名。"),e.next=44;break;case 13:if(/^[a-zA-Z\u4e00-\u9fa5]+$/.test(r.data.feedback.name)){e.next=17;break}i.default.confirm("您好！姓名只能是中文或英文字符！"),e.next=44;break;case 17:if(0!=r.data.feedback.phone){e.next=21;break}i.default.confirm("您好！请输入您的电话，以便我们能够联系您！"),e.next=44;break;case 21:if(!(r.data.feedback.phone<11)){e.next=25;break}i.default.confirm("您好！您输入的手机号码少于11位，请您核对！"),e.next=44;break;case 25:if(/^1[3456789]\d{9}$/.test(r.data.feedback.phone)){e.next=29;break}i.default.confirm("您好！您输入的手机不合法！"),e.next=44;break;case 29:return i.default.loading(),e.prev=30,e.next=33,n.default.save(r.feedback);case 33:e.next=41;break;case 35:return e.prev=35,e.t0=e.catch(30),console.log(e.t0),e.next=40,i.default.error("提交失败");case 40:return e.abrupt("return");case 41:return e.next=43,i.default.success("反馈成功",3e3);case 43:t.default.navigateBack();case 44:case"end":return e.stop()}}),e,r,[[30,35]])})))()}},p.computed={},p.components={Tips:o.default,Loading:u.default},p.mixins=[r.default,a.default],p.config={navigationBarTitleText:"客户服务"},d(p,c)}var p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,t.default.page),e(l,[{key:"onLoad",value:(p=s(regeneratorRuntime.mark((function e(r){var a=r.redirect,n=r.showModal,o=r.qrCode;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.default.setNavigationBarColor({backgroundColor:"#299192",frontColor:"#ffffff"}),this.feedback.qrCode=o,this.redirect=a,this.showModal=n,this.loaded();case 5:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"validate",value:function(){var e=[{value:this.feedback.desc,method:"required",message:"请输入您的问题！"},{value:this.feedback.name,method:"required",message:"请输入姓名!"},{value:this.feedback.phone,method:"required",message:"请输入联系电话"},{value:this.feedback.phone,method:"tel",message:"请输入合法手机号码"}];return this.check(e)}}]),l}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(l,"pages/anticounterfeiting/reportconsultation"));