Object.defineProperty(exports,"__esModule",{value:!0});var n,t,e=function(){function n(n,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}}return function(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}}();var i=(t=n=function(){function n(){!function(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n)}return e(n,null,[{key:"success",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;if(wx.showToast({title:n,icon:"success",mask:!0,duration:t}),t>0)return new Promise((function(n,e){setTimeout((function(){n()}),t)}))}},{key:"toasts",value:function(n,t){wx.showToast({title:n,icon:"none",width:180,mask:!0,duration:3e3}),t&&setTimeout((function(){t()}),3e3)}},{key:"modal",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"提示";return new Promise((function(e,i){wx.showModal({title:t,content:n,showCancel:!0,success:function(n){e(n)},fail:function(n){i(n)}})}))}},{key:"confirm",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"提示";return new Promise((function(i,o){wx.showModal({title:e,content:n,showCancel:!1,success:function(n){n.confirm?i(t):n.cancel&&o(t)},fail:function(n){o(t)}})}))}},{key:"confirms",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"提示",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"去抽奖";return new Promise((function(o,a){wx.showModal({title:e,content:n,showCancel:!0,confirmText:i,success:function(n){n.confirm?o(t):n.cancel&&a(t)},fail:function(n){a(t)}})}))}},{key:"toast",value:function(n,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"success";wx.showToast({title:n,icon:e,mask:!0,duration:500}),t&&setTimeout((function(){t()}),500)}},{key:"alert",value:function(n){return wx.showToast({title:n,image:"/images/icons/alert.png",mask:!0,duration:3e3}),new Promise((function(n,t){setTimeout((function(){n()}),3e3)}))}},{key:"error",value:function(n,t){wx.showToast({title:n,image:"/images/icons/error.png",mask:!0,duration:500}),t&&setTimeout((function(){t()}),500)}},{key:"loading",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"加载中";this.isLoading||(this.isLoading=!0,wx.showLoading?wx.showLoading({title:n,mask:!0,duration:2e3}):wx.showNavigationBarLoading())}},{key:"longLoading",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"加载中";this.isLoading||(this.isLoading=!0,wx.showLoading?wx.showLoading({title:n,mask:!0}):wx.showNavigationBarLoading())}},{key:"loaded",value:function(){this.isLoading&&(this.isLoading=!1,wx.hideLoading?wx.hideLoading():wx.hideNavigationBarLoading())}},{key:"action",value:function(){for(var n=arguments.length,t=Array(n),e=0;e<n;e++)t[e]=arguments[e];return new Promise((function(n,e){wx.showActionSheet({itemList:t,success:function(e){var i={index:e.tapIndex,text:t[e.tapIndex]};n(i)},fail:function(n){e(n.errMsg)}})}))}},{key:"actionWithFunc",value:function(n){for(var t=arguments.length,e=Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];wx.showActionSheet({itemList:n,success:function(n){var t=n.tapIndex;t>=0&&t<e.length&&e[t]()}})}},{key:"share",value:function(t,e,i){return{title:t,path:e,desc:i,success:function(t){n.toast("分享成功")}}}},{key:"setLoading",value:function(){this.isLoading=!0}}]),n}(),n.isLoading=!1,n.pause=!1,t);exports.default=i;