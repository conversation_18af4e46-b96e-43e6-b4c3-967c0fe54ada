var t=require("./8BitByte.js"),e=require("./RSBlock.js"),o=require("./BitBuffer.js"),r=require("./util.js"),i=require("./Polynomial.js");function s(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var n=s.prototype;n.addData=function(e){var o=new t(e);this.dataList.push(o),this.dataCache=null},n.isDark=function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},n.getModuleCount=function(){return this.moduleCount},n.make=function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var i=e.getRSBlocks(t,this.errorCorrectLevel),s=new o,n=0,u=0;u<i.length;u++)n+=i[u].dataCount;for(u=0;u<this.dataList.length;u++){var h=this.dataList[u];s.put(h.mode,4),s.put(h.getLength(),r.getLengthInBits(h.mode,t)),h.write(s)}if(s.getLengthInBits()<=8*n)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},n.makeImpl=function(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var o=0;o<this.moduleCount;o++){this.modules[o]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[o][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=s.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)},n.setupPositionProbePattern=function(t,e){for(var o=-1;o<=7;o++)if(!(t+o<=-1||this.moduleCount<=t+o))for(var r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+o][e+r]=0<=o&&o<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==o||6==o)||2<=o&&o<=4&&2<=r&&r<=4)},n.getBestMaskPattern=function(){for(var t=0,e=0,o=0;o<8;o++){this.makeImpl(!0,o);var i=r.getLostPoint(this);(0==o||t>i)&&(t=i,e=o)}return e},n.createMovieClip=function(t,e,o){var r=t.createEmptyMovieClip(e,o);this.make();for(var i=0;i<this.modules.length;i++)for(var s=1*i,n=0;n<this.modules[i].length;n++){var u=1*n;this.modules[i][n]&&(r.beginFill(0,100),r.moveTo(u,s),r.lineTo(u+1,s),r.lineTo(u+1,s+1),r.lineTo(u,s+1),r.endFill())}return r},n.setupTimingPattern=function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},n.setupPositionAdjustPattern=function(){for(var t=r.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var o=0;o<t.length;o++){var i=t[e],s=t[o];if(null==this.modules[i][s])for(var n=-2;n<=2;n++)for(var u=-2;u<=2;u++)this.modules[i+n][s+u]=-2==n||2==n||-2==u||2==u||0==n&&0==u}},n.setupTypeNumber=function(t){for(var e=r.getBCHTypeNumber(this.typeNumber),o=0;o<18;o++){var i=!t&&1==(e>>o&1);this.modules[Math.floor(o/3)][o%3+this.moduleCount-8-3]=i}for(o=0;o<18;o++){i=!t&&1==(e>>o&1);this.modules[o%3+this.moduleCount-8-3][Math.floor(o/3)]=i}},n.setupTypeInfo=function(t,e){for(var o=this.errorCorrectLevel<<3|e,i=r.getBCHTypeInfo(o),s=0;s<15;s++){var n=!t&&1==(i>>s&1);s<6?this.modules[s][8]=n:s<8?this.modules[s+1][8]=n:this.modules[this.moduleCount-15+s][8]=n}for(s=0;s<15;s++){n=!t&&1==(i>>s&1);s<8?this.modules[8][this.moduleCount-s-1]=n:s<9?this.modules[8][15-s-1+1]=n:this.modules[8][15-s-1]=n}this.modules[this.moduleCount-8][8]=!t},n.mapData=function(t,e){for(var o=-1,i=this.moduleCount-1,s=7,n=0,u=this.moduleCount-1;u>0;u-=2)for(6==u&&u--;;){for(var h=0;h<2;h++)if(null==this.modules[i][u-h]){var a=!1;n<t.length&&(a=1==(t[n]>>>s&1)),r.getMask(e,i,u-h)&&(a=!a),this.modules[i][u-h]=a,-1==--s&&(n++,s=7)}if((i+=o)<0||this.moduleCount<=i){i-=o,o=-o;break}}},s.PAD0=236,s.PAD1=17,s.createData=function(t,i,n){for(var u=e.getRSBlocks(t,i),h=new o,a=0;a<n.length;a++){var l=n[a];h.put(l.mode,4),h.put(l.getLength(),r.getLengthInBits(l.mode,t)),l.write(h)}var m=0;for(a=0;a<u.length;a++)m+=u[a].dataCount;if(h.getLengthInBits()>8*m)throw new Error("code length overflow. ("+h.getLengthInBits()+">"+8*m+")");for(h.getLengthInBits()+4<=8*m&&h.put(0,4);h.getLengthInBits()%8!=0;)h.putBit(!1);for(;!(h.getLengthInBits()>=8*m||(h.put(s.PAD0,8),h.getLengthInBits()>=8*m));)h.put(s.PAD1,8);return s.createBytes(h,u)},s.createBytes=function(t,e){for(var o=0,s=0,n=0,u=new Array(e.length),h=new Array(e.length),a=0;a<e.length;a++){var l=e[a].dataCount,m=e[a].totalCount-l;s=Math.max(s,l),n=Math.max(n,m),u[a]=new Array(l);for(var d=0;d<u[a].length;d++)u[a][d]=255&t.buffer[d+o];o+=l;var f=r.getErrorCorrectPolynomial(m),g=new i(u[a],f.getLength()-1).mod(f);h[a]=new Array(f.getLength()-1);for(d=0;d<h[a].length;d++){var v=d+g.getLength()-h[a].length;h[a][d]=v>=0?g.get(v):0}}var p=0;for(d=0;d<e.length;d++)p+=e[d].totalCount;var C=new Array(p),c=0;for(d=0;d<s;d++)for(a=0;a<e.length;a++)d<u[a].length&&(C[c++]=u[a][d]);for(d=0;d<n;d++)for(a=0;a<e.length;a++)d<h[a].length&&(C[c++]=h[a][d]);return C},module.exports=s;