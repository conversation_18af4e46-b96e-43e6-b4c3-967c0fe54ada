Object.defineProperty(exports,"__esModule",{value:!0});var e,r=require("./qr.js/index.js"),t=(e=r)&&e.__esModule?e:{default:e};exports.default={setDefaults:function(){return{typeNumber:-1,errorCorrectLevel:2,width:200,height:200,fgColor:"black",bgColor:"white"}},init:function(e,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=Object.assign({},this.setDefaults(),o),l=(0,t.default)(this.utf16to8(r),{typeNumber:a.typeNumber,errorCorrectLevel:a.errorCorrectLevel}),i=wx.createCanvasContext(e),n=l.modules,h=a.width/n.length,f=a.height/n.length;i.scale(1,1),n.forEach((function(e,r){e.forEach((function(e,t){i.setFillStyle(e?a.fgColor:a.bgColor);var o=Math.ceil((t+1)*h)-Math.floor(t*h),l=Math.ceil((r+1)*f)-Math.floor(r*f);i.fillRect(Math.round(t*h),Math.round(r*f),o,l)}))})),i.draw()},utf16to8:function(e){for(var r=e.length,t="",o=0;o<r;o++){var a=e.charCodeAt(o);a>=1&&a<=127?t+=e.charAt(o):a>2047?(t+=String.fromCharCode(224|a>>12&15),t+=String.fromCharCode(128|a>>6&63),t+=String.fromCharCode(128|a>>0&63)):(t+=String.fromCharCode(192|a>>6&31),t+=String.fromCharCode(128|a>>0&63))}return t}};