Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=r(require("./../../npm/wepy/lib/wepy.js")),t=r(require("./../../mixins/router.js"));function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(r){function i(){var e,r,u;o(this,i);for(var a=arguments.length,p=Array(a),c=0;c<a;c++)p[c]=arguments[c];return r=u=n(this,(e=i.__proto__||Object.getPrototypeOf(i)).call.apply(e,[this].concat(p))),u.props={param:{}},u.mixins=[t.default],n(u,r)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,e.default.component),i}();exports.default=i;