/**
 * 深度算法猎手
 * 专注于从源码中挖掘真正的Authorization生成算法
 * 不依赖现有的Authorization，寻找算法本质
 */

const fs = require('fs');
const path = require('path');

class DeepAlgorithmHunter {
    constructor() {
        this.sourceDir = '__APP__';
        this.algorithmClues = {
            // JWT相关的关键词
            jwtKeywords: [
                'jwt', 'JWT', 'token', 'Token', 'auth', 'Auth',
                'sign', 'signature', 'secret', 'key', 'encode', 'decode'
            ],
            
            // 加密相关的关键词
            cryptoKeywords: [
                'crypto', 'md5', 'sha', 'hmac', 'base64', 'encrypt', 'decrypt',
                'hash', 'cipher', 'algorithm'
            ],
            
            // 认证流程相关的关键词
            authKeywords: [
                'login', 'getJwt', 'createJwt', 'Authorization', 'login_code',
                'getJifenShopJwt', 'authorized_token', 'expire_time'
            ],
            
            // 微信相关的关键词
            wechatKeywords: [
                'wx.', 'wechat', 'miniprogram', 'appId', 'appSecret', 'unionid', 'openid'
            ]
        };
        
        this.foundAlgorithms = [];
        this.codePatterns = [];
        
        console.log('🔍 深度算法猎手初始化完成');
        console.log('🎯 专注于挖掘真正的Authorization生成算法');
        console.log('🚫 忽略现有Authorization，寻找算法本质');
    }

    /**
     * 扫描所有源码文件
     */
    async scanAllSourceFiles() {
        console.log('\n🔍 开始扫描所有源码文件...');
        
        const jsFiles = this.findJSFiles(this.sourceDir);
        console.log(`📁 找到 ${jsFiles.length} 个JS文件`);
        
        for (let i = 0; i < jsFiles.length; i++) {
            const file = jsFiles[i];
            console.log(`\n📄 [${i+1}/${jsFiles.length}] 分析文件: ${file}`);
            
            try {
                await this.analyzeFile(file);
            } catch (error) {
                console.log(`❌ 分析文件失败: ${error.message}`);
            }
        }
        
        return this.foundAlgorithms;
    }

    /**
     * 查找所有JS文件
     */
    findJSFiles(dir) {
        const files = [];
        
        const scan = (currentDir) => {
            try {
                const items = fs.readdirSync(currentDir);
                
                for (const item of items) {
                    const fullPath = path.join(currentDir, item);
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory()) {
                        scan(fullPath);
                    } else if (item.endsWith('.js')) {
                        files.push(fullPath);
                    }
                }
            } catch (error) {
                // 忽略无法访问的目录
            }
        };
        
        scan(dir);
        return files;
    }

    /**
     * 分析单个文件
     */
    async analyzeFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 1. 寻找JWT相关算法
            this.findJWTAlgorithms(filePath, content);
            
            // 2. 寻找加密算法
            this.findCryptoAlgorithms(filePath, content);
            
            // 3. 寻找认证流程
            this.findAuthFlows(filePath, content);
            
            // 4. 寻找微信相关算法
            this.findWechatAlgorithms(filePath, content);
            
            // 5. 寻找关键函数
            this.findKeyFunctions(filePath, content);
            
        } catch (error) {
            throw new Error(`读取文件失败: ${error.message}`);
        }
    }

    /**
     * 寻找JWT相关算法
     */
    findJWTAlgorithms(filePath, content) {
        // 寻找JWT生成相关的代码
        const jwtPatterns = [
            /jwt\s*=\s*[^;]+/gi,
            /createJwt\s*\([^)]*\)\s*{[^}]+}/gi,
            /getJwt\s*\([^)]*\)\s*{[^}]+}/gi,
            /sign\s*\([^)]*\)\s*{[^}]+}/gi,
            /\.sign\s*\([^)]+\)/gi,
            /hmac\s*\([^)]+\)/gi,
            /base64\s*\([^)]+\)/gi
        ];
        
        jwtPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    this.foundAlgorithms.push({
                        type: 'JWT算法',
                        file: filePath,
                        code: match,
                        description: 'JWT相关算法代码'
                    });
                    console.log(`🔑 发现JWT算法: ${match.substring(0, 100)}...`);
                });
            }
        });
    }

    /**
     * 寻找加密算法
     */
    findCryptoAlgorithms(filePath, content) {
        // 寻找加密相关的代码
        const cryptoPatterns = [
            /crypto\.[^;]+/gi,
            /md5\s*\([^)]+\)/gi,
            /sha\d*\s*\([^)]+\)/gi,
            /hmac\s*\([^)]+\)/gi,
            /encrypt\s*\([^)]+\)/gi,
            /decrypt\s*\([^)]+\)/gi,
            /hash\s*\([^)]+\)/gi,
            /secret\s*[=:]\s*['""][^'"]+['"]/gi
        ];
        
        cryptoPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    this.foundAlgorithms.push({
                        type: '加密算法',
                        file: filePath,
                        code: match,
                        description: '加密相关算法代码'
                    });
                    console.log(`🔐 发现加密算法: ${match}`);
                });
            }
        });
    }

    /**
     * 寻找认证流程
     */
    findAuthFlows(filePath, content) {
        // 寻找认证流程相关的代码
        const authPatterns = [
            /getJifenShopJwt[^}]+}/gi,
            /Authorization\s*[=:][^;]+/gi,
            /login_code[^}]+}/gi,
            /authorized_token[^;]+/gi,
            /expire_time[^;]+/gi,
            /globalData\.auth[^;]+/gi,
            /setStorageSync\s*\(\s*['""]auth[^)]+\)/gi
        ];
        
        authPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    this.foundAlgorithms.push({
                        type: '认证流程',
                        file: filePath,
                        code: match,
                        description: '认证流程相关代码'
                    });
                    console.log(`🔓 发现认证流程: ${match.substring(0, 100)}...`);
                });
            }
        });
    }

    /**
     * 寻找微信相关算法
     */
    findWechatAlgorithms(filePath, content) {
        // 寻找微信相关的算法
        const wechatPatterns = [
            /wx\.[^;]+/gi,
            /appId\s*[=:]\s*['""][^'"]+['"]/gi,
            /appSecret\s*[=:]\s*['""][^'"]+['"]/gi,
            /unionid[^;]+/gi,
            /openid[^;]+/gi,
            /wx\.login\s*\([^}]+}/gi,
            /wx\.getUserInfo[^}]+}/gi
        ];
        
        wechatPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    this.foundAlgorithms.push({
                        type: '微信算法',
                        file: filePath,
                        code: match,
                        description: '微信相关算法代码'
                    });
                    console.log(`📱 发现微信算法: ${match}`);
                });
            }
        });
    }

    /**
     * 寻找关键函数
     */
    findKeyFunctions(filePath, content) {
        // 寻找关键函数定义
        const functionPatterns = [
            /function\s+\w*[Jj]wt\w*\s*\([^}]+}/gi,
            /function\s+\w*[Aa]uth\w*\s*\([^}]+}/gi,
            /function\s+\w*[Ll]ogin\w*\s*\([^}]+}/gi,
            /function\s+\w*[Ss]ign\w*\s*\([^}]+}/gi,
            /\w+\s*:\s*function\s*\([^}]+}/gi
        ];
        
        functionPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    this.foundAlgorithms.push({
                        type: '关键函数',
                        file: filePath,
                        code: match,
                        description: '关键函数定义'
                    });
                    console.log(`⚙️ 发现关键函数: ${match.substring(0, 80)}...`);
                });
            }
        });
    }

    /**
     * 深度分析特定文件
     */
    async deepAnalyzeFile(filePath) {
        console.log(`\n🔬 深度分析文件: ${filePath}`);
        
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 按行分析
            const lines = content.split('\n');
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                // 寻找包含关键词的行
                const keywords = [
                    ...this.algorithmClues.jwtKeywords,
                    ...this.algorithmClues.cryptoKeywords,
                    ...this.algorithmClues.authKeywords,
                    ...this.algorithmClues.wechatKeywords
                ];
                
                for (const keyword of keywords) {
                    if (line.toLowerCase().includes(keyword.toLowerCase())) {
                        console.log(`📍 第${i+1}行包含关键词"${keyword}": ${line}`);
                        
                        // 获取上下文
                        const context = this.getContext(lines, i, 3);
                        console.log(`📄 上下文:\n${context}`);
                        
                        this.codePatterns.push({
                            file: filePath,
                            line: i + 1,
                            keyword: keyword,
                            code: line,
                            context: context
                        });
                    }
                }
            }
            
        } catch (error) {
            console.log(`❌ 深度分析失败: ${error.message}`);
        }
    }

    /**
     * 获取代码上下文
     */
    getContext(lines, lineIndex, contextSize) {
        const start = Math.max(0, lineIndex - contextSize);
        const end = Math.min(lines.length, lineIndex + contextSize + 1);
        
        const contextLines = [];
        for (let i = start; i < end; i++) {
            const marker = i === lineIndex ? '>>> ' : '    ';
            contextLines.push(`${marker}${i+1}: ${lines[i]}`);
        }
        
        return contextLines.join('\n');
    }

    /**
     * 分析算法模式
     */
    analyzeAlgorithmPatterns() {
        console.log('\n📊 分析发现的算法模式...');
        
        // 按类型分组
        const byType = {};
        this.foundAlgorithms.forEach(algo => {
            if (!byType[algo.type]) {
                byType[algo.type] = [];
            }
            byType[algo.type].push(algo);
        });
        
        console.log('📋 算法类型统计:');
        Object.entries(byType).forEach(([type, algos]) => {
            console.log(`  ${type}: ${algos.length}个`);
        });
        
        // 寻找最有价值的算法
        console.log('\n🎯 最有价值的算法发现:');
        
        // JWT算法优先级最高
        if (byType['JWT算法']) {
            console.log('🔑 JWT算法发现:');
            byType['JWT算法'].forEach((algo, index) => {
                console.log(`  ${index+1}. 文件: ${algo.file}`);
                console.log(`     代码: ${algo.code}`);
            });
        }
        
        // 认证流程次之
        if (byType['认证流程']) {
            console.log('🔓 认证流程发现:');
            byType['认证流程'].forEach((algo, index) => {
                console.log(`  ${index+1}. 文件: ${algo.file}`);
                console.log(`     代码: ${algo.code}`);
            });
        }
        
        return byType;
    }

    /**
     * 生成算法分析报告
     */
    generateReport() {
        console.log('\n📄 生成算法分析报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            totalAlgorithms: this.foundAlgorithms.length,
            totalPatterns: this.codePatterns.length,
            algorithms: this.foundAlgorithms,
            patterns: this.codePatterns,
            summary: this.analyzeAlgorithmPatterns()
        };
        
        // 保存报告
        const reportFile = 'algorithm_analysis_report.json';
        try {
            fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
            console.log(`📁 报告已保存到: ${reportFile}`);
        } catch (error) {
            console.log(`❌ 保存报告失败: ${error.message}`);
        }
        
        return report;
    }

    /**
     * 运行完整的深度算法分析
     */
    async runDeepAlgorithmAnalysis() {
        console.log('🚀 开始深度算法分析...');
        console.log('🎯 目标：挖掘真正的Authorization生成算法');
        console.log('🚫 不依赖现有Authorization');
        
        try {
            // 1. 扫描所有源码文件
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 扫描所有源码文件');
            console.log('='.repeat(60));
            await this.scanAllSourceFiles();
            
            // 2. 深度分析关键文件
            console.log('\n' + '='.repeat(60));
            console.log('🔬 第二阶段: 深度分析关键文件');
            console.log('='.repeat(60));
            
            const keyFiles = [
                '__APP__/api/auth.js',
                '__APP__/api/member.js',
                '__APP__/api/base.js',
                '__APP__/app.js'
            ];
            
            for (const file of keyFiles) {
                if (fs.existsSync(file)) {
                    await this.deepAnalyzeFile(file);
                }
            }
            
            // 3. 分析算法模式
            console.log('\n' + '='.repeat(60));
            console.log('📊 第三阶段: 分析算法模式');
            console.log('='.repeat(60));
            const patterns = this.analyzeAlgorithmPatterns();
            
            // 4. 生成报告
            console.log('\n' + '='.repeat(60));
            console.log('📄 第四阶段: 生成分析报告');
            console.log('='.repeat(60));
            const report = this.generateReport();
            
            // 输出结论
            console.log('\n' + '='.repeat(60));
            console.log('🎯 深度算法分析结论');
            console.log('='.repeat(60));
            
            console.log(`📊 总计发现 ${this.foundAlgorithms.length} 个算法片段`);
            console.log(`📍 总计发现 ${this.codePatterns.length} 个代码模式`);
            
            if (this.foundAlgorithms.length > 0) {
                console.log('\n🎉 算法分析成功！');
                console.log('🔑 发现了多个算法相关的代码片段');
                console.log('📄 详细信息请查看生成的报告文件');
                
                return {
                    success: true,
                    algorithmsFound: this.foundAlgorithms.length,
                    patternsFound: this.codePatterns.length,
                    report: report
                };
            } else {
                console.log('\n🤔 未发现明显的算法代码');
                console.log('💡 可能需要更深入的逆向分析');
                
                return {
                    success: false,
                    message: '未发现明显的算法代码',
                    report: report
                };
            }
            
        } catch (error) {
            console.log('\n❌ 深度算法分析失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = DeepAlgorithmHunter;

// 如果直接运行此文件
if (require.main === module) {
    const hunter = new DeepAlgorithmHunter();
    
    console.log('🔍 深度算法猎手');
    console.log('🎯 专注于挖掘真正的Authorization生成算法');
    console.log('🚫 不依赖现有Authorization，寻找算法本质');
    console.log('');
    
    // 运行深度算法分析
    hunter.runDeepAlgorithmAnalysis().then(result => {
        if (result.success) {
            console.log('\n🎉 深度算法分析成功！');
            console.log(`🔑 发现了 ${result.algorithmsFound} 个算法片段`);
        } else {
            console.log('\n🤔 深度算法分析需要进一步研究');
            console.log('💡 建议进行更深入的逆向工程');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
