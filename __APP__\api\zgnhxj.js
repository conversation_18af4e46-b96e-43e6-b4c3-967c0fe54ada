Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),r=t(require("./base.js"));t(require("./../utils/Page.js")),t(require("./../utils/Pages.js")),t(require("./../npm/wepy/lib/wepy.js"));function t(e){return e&&e.__esModule?e:{default:e}}function n(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(i,u){try{var o=r[i](u),a=o.value}catch(e){return void t(e)}if(!o.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}function i(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function u(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var o=function(t){function o(){return i(this,o),u(this,(o.__proto__||Object.getPrototypeOf(o)).apply(this,arguments))}var a,s,p,c,h,f,l,g,m,v,y,b,k,j;return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(o,r.default),e(o,null,[{key:"saveCompetitionInfo",value:(j=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/save",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return j.apply(this,arguments)})},{key:"reSaveCompetitionInfo",value:(k=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/resave",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"getCompetitionInfo",value:function(e){var r=this.jifenShopApiUrl+"/zgnhxj/poster/read?member_id="+e;return this.get(r)}},{key:"getHelpersInfo",value:function(e){var r=this.jifenShopApiUrl+"/zgnhxj/poster/helpers?poster_id="+e;return this.get(r)}},{key:"getsharetoken",value:(b=n(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/zgnhxj/poster/get_share_token",e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return b.apply(this,arguments)})},{key:"helpFrends",value:(y=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/help",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"ranks",value:(v=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/ranks?member_id="+r,e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"myPrizeList",value:(m=n(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/zgnhxj/prize/index",e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return m.apply(this,arguments)})},{key:"receivePrize",value:(g=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/prize/receive",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return g.apply(this,arguments)})},{key:"myPrizeByCount",value:(l=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/prize/indexByCount?member_id="+r,e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"violationMemberList",value:(f=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/abnormaluser/index",e.abrupt("return",this.get(t,r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"report",value:(h=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/saveReport",e.abrupt("return",this.post(t,r));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return h.apply(this,arguments)})},{key:"hositoryRank",value:(c=n(regeneratorRuntime.mark((function e(r){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/zgnhxj/poster/history_rank?date="+r,e.abrupt("return",this.get(t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"getRedpacketRainH5url",value:(p=n(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/zgn2022/member/getRedpacketRainH5url",e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"getFokaH5url",value:(s=n(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/zgn2022/member/getFokaH5url",e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"getMyCiphertext",value:(a=n(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/zgn2023/member/getMyCiphertext",e.abrupt("return",this.get(r));case 2:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})}]),o}();exports.default=o;