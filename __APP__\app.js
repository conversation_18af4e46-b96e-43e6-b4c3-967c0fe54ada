Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,a){for(var t=0;t<a.length;t++){var n=a[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(a,t,n){return t&&e(a.prototype,t),n&&e(a,n),a}}(),a=s(require("./npm/wepy/lib/wepy.js")),t=s(require("./utils/WxUtils.js"));require("./npm/wepy-async-function/index.js");var n=require("./npm/wepy-redux/lib/index.js"),o=s(require("./store/index.js")),r=s(require("./utils/cdp/QDTracker.js")),i=require("./config-prod.js");function s(e){return e&&e.__esModule?e:{default:e}}function c(e){return function(){var a=e.apply(this,arguments);return new Promise((function(e,t){return function n(o,r){try{var i=a[o](r),s=i.value}catch(e){return void t(e)}if(!i.done)return Promise.resolve(s).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(s)}("next")}))}}r.default.init({debug:!0,appid:i.service.appIds.club,appkey:"0MA006CODFY0JR66",application:"习酒会员俱乐部",apiHost:"https://event-sdap.gzxijiu.cn",encryptMode:"close",track_interval:0,batch_max_time:10,session_interval:18e5,autoTrack:{appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0,pageHide:!0,pageShare:!0,mpClick:!1,tag_attr:["element_id","element_name","element_content","element_path","element_type","url"]}}),r.default.setCommonData({app_version:"v3.2.6"});var u=(0,o.default)();(0,n.setStore)(u);var p=function(n){function o(){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,o);var e=function(e,a){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!a||"object"!=typeof a&&"function"!=typeof a?e:a}(this,(o.__proto__||Object.getPrototypeOf(o)).call(this));return e.globalData={version:"v3.2.6",auth:{},scene:null,regScene:"",shopType:"1",shopName:"防伪查询",appCode:"owVHb1gHrvktni80kjMlFMzSDJDWY0xR",baseUrl:i.service.domains.baseUrl,forumBaseUrl:i.service.domains.forumBaseUrl,wechatRedpacketUrl:i.service.domains.wechatRedpacketUrl,xijiuweixinUrl:i.service.domains.xijiuweixinUrl,jifenShopUrl:i.service.domains.jifenShopUrl,jifenShopApiUrl:i.service.domains.jifenShopApiUrl,pointUrl:i.service.domains.pointUrl,userLocationApiUrl:i.service.domains.userLocationApiUrl,baseAreaHost:i.service.domains.baseAreaHost,wapExijiuActiveUrl:i.service.domains.wapExijiuActiveUrl,_fmOpt:{partnerCode:"xijiu",appName:"xijiu_xcx",env:"PRODUCTION"},_fpOpt:{organization:"PQLv5QK0xkETr7mfztsw",publicKey:"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCN15/bTNUbGF+TQFALZKJ0TOUtZDy4g66kkOD9WboBPdaZVpm8rc8heQHfxpjGpLJQmO/XJEqDMY/0V0FyFaAc7LfyFqVJ/xFVvT539XETeXSL679RJYT0ZRs02m/1DV7ypRtFhWBCj7OLAOElGWTbaYo0aVjOMzjeqg+Fx8A6bwIDAQAB",apiHost:"fp-it.fengkongcloud.com"},navHeight:"",navTop:"",windowHeight:"",duanwuLuckDraw:"",componentsParams:{},height:"",second:10,pageParams:{"pages/goods/category":{buyPanelType:"POPUP",isCoupon:!1},"pages/goods/detail":{buyPanelType:"SLIDER",cartType:"BAR",isCoupon:!0,isComment:!0,isContact:!0,isShare:!0,isSales:!0},"pages/goods/search":{hotKeywords:["抽纸","卷纸","面巾纸","卫生巾","湿纸巾","维达","维达","波斯猫","妙洁"]}},showSlideValidate:!1,qDTracker:r.default,appIds:i.service.appIds},e.config={pages:["pages/customer/index","pages/home/<USER>","pages/anticounterfeiting/index","pages/anticounterfeiting/detail","pages/anticounterfeiting/validate","pages/anticounterfeiting/reportconsultation","pages/web/fangwei","pages/web/redpacket","pages/web/webView","pages/anticounterfeiting/process","pages/customer/registe_member","pages/customer/upgrade_strategy","pages/customer/store_commission","pages/customer/customer_service","pages/customer/goToUmall","pages/customer/goToJph","pages/customer/goToposter","pages/customer/birthday_present","pages/anticounterfeiting/active_rule","pages/web/navigateToMiniProgram","pages/web/web2MiniProgram","pages/customer/activity_list","pages/web/active2023","pages/web/zgnhxj","pages/web/qingyunian","pages/web/zhijiaobncx2024","pages/web/pjhLive","pages/web/webviewForOutsourcing","pages/web/midautumnfestival2024","pages/activity/subscribemsg","pages/customer/networkAuth","pages/web/xjzhijiao2024","pages/web/chineseyear2025","pages/web/chineseyear20252","pages/web/navigateToChannel","pages/web/traeweb"],subPackages:[{root:"pagesforum",pages:["pages/index","pages/guidelines","pages/introduction","pages/appraisal","pages/community","pages/comment","pages/personal_center","pages/comment_area","pages/member_post","pages/view_all_replies","pages/identified_detail","pages/my_identification","pages/my_focus","pages/my_message","pages/feedback","pages/add_feedback","pages/add_tip","pages/wine_taster","pages/complaint","pages/other_complaint","pages/gotoWebView","pages/gotoNewersWebView","pages/add_tips","pages/my_fans","pages/my_focus_user","pages/my_feedback","pages/identification_instructions","pages/topicagreement","pages/all_drawer","pages/active_list","pages/goOfficialAccount"]},{root:"pages/plant",pages:["map","index","notice","Introduction","friends","ranking","Improve","answer","reality","goodwine","exchangeStore","test","scrounge","luck_draw","order","hotactivity"]},{root:"pages/consumerday",pages:["consumerday"]},{root:"pages/duanwujc",pages:["index","active_rule","active_share","competition","hository_rank","violation","person","rank_rule","prize"]},{root:"pages/basearea",pages:["index","web","video","order","mapdetail","mapweb","scenic"]},{root:"pages/inspect",pages:["wuliu","fahuodanhao","xiangma","chanpinpihao","clickcheck","fclogin","redpacket"]},{root:"chinayeardrinkxj",pages:["pages/index","pages/active_rule","pages/active_share","pages/competition","pages/hository_rank","pages/violation","pages/generate_poster","pages/help_frends","pages/my_prize"]},{root:"pages/active",pages:["jcmidautumn/all_index","jcmidautumn/index","jcmidautumn/address","jcmidautumn/rank","jcmidautumn/rule","jcmidautumn/jinqiu","jcmidautumn/jinqiu_rule","jcmidautumn/network","jcmidautumn/recorde","jzdraw/draw","questionnaire/area","questionnaire/qestion","questionnaire/question1","yuan4to1/index","yuan4to1/address","yuan4to1/record","yx2025/index","yx2025/sign-add","yx2025/sign-info"]},{root:"weeklyLottery",pages:["pages/list","pages/detail","pages/record"]},{root:"jinzuan2022",pages:["pages/index","pages/record","pages/apply"]},{root:"pjh",pages:["pages/detail","pages/sign","pages/checkSign","pages/leaderSessionList","pages/leaderLocation","pages/sessionList","pages/sessionLocation"]},{root:"jiaocangzq",pages:["pages/index","pages/rule","pages/apply","pages/detail","pages/rank","pages/notice","pages/confirm"]},{root:"chongyang2022",pages:["pages/index","pages/exchange"]},{root:"mysteryboxes",pages:["pages/index"]},{root:"pages/md",pages:["echarts","mdascription"]},{root:"pages/auth",pages:["setting","avatarAndNick","login"]},{root:"pages/business",pages:["card","buy5gain6Page/buy5gain6","buy5gain6Page/address"]}],permission:{"scope.userLocation":{desc:"你的位置信息将用于防伪溯源"}},window:{backgroundTextStyle:"dark",backgroundColor:"#f3f3f3",navigationBarBackgroundColor:"#024236"},requiredPrivateInfos:["chooseLocation","getLocation"]},e.use("requestfix"),e.use("promisify"),e.intercept("request",{config:function(e){return e.header=this.createAuthHeader(),e}}),e.intercept("uploadFile",{config:function(e){return e.header=this.createAuthHeader(),e},success:function(e){return e.data}}),e}var s,u,p,g;return function(e,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function, not "+typeof a);e.prototype=Object.create(a&&a.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(e,a):e.__proto__=a)}(o,a.default.app),e(o,[{key:"createAuthHeader",value:function(){var e=a.default.$instance.globalData.auth.login_code,t=a.default.$instance.globalData.auth.Authorization||a.default.getStorageSync("Authorization"),n={};return e&&(n.login_code=e),t&&(n.Authorization=t),n}},{key:"login",value:(g=c(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null==(t=a.default.getStorageSync("login_code"))||""==t){e.next=17;break}return e.prev=2,a.default.$instance.globalData.auth.login_code=t,e.next=6,this.checkLoginCode(t);case 6:return e.abrupt("return",e.sent);case 9:return e.prev=9,e.t0=e.catch(2),console.warn("check login code fial",t),e.next=14,this.doLogin();case 14:return e.abrupt("return",e.sent);case 15:e.next=21;break;case 17:return console.warn("login code not exists",t),e.next=20,this.doLogin();case 20:return e.abrupt("return",e.sent);case 21:case"end":return e.stop()}}),e,this,[[2,9]])}))),function(){return g.apply(this,arguments)})},{key:"checkLoginCode",value:(p=c(regeneratorRuntime.mark((function e(t){var n,o,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=a.default.$instance.globalData.baseUrl+"/auth/checkSession?loginCode="+t,o={url:n,method:"GET"},e.next=4,a.default.request(o);case 4:if(200==(r=e.sent).statusCode&&0==r.data.code){e.next=7;break}return e.abrupt("return",!1);case 7:return e.abrupt("return",r.data.data);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"doLogin",value:(u=c(regeneratorRuntime.mark((function e(){var t,n,o,r,i,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a.default.login();case 3:return t=e.sent,n=t.code,o=a.default.$instance.globalData.baseUrl+"/auth/session?code="+n,r={url:o,method:"GET"},e.next=9,a.default.request(r);case 9:if(200==(i=e.sent).statusCode&&0==i.data.code){e.next=12;break}return e.abrupt("return",!1);case 12:return s=i.data.data,a.default.setStorageSync("login_code",s.login_code),a.default.$instance.globalData.auth.login_code=s.login_code,a.default.setStorageSync("third_session",s.third_session),a.default.$instance.globalData.auth.third_session=s.third_session,e.abrupt("return",s);case 20:return e.prev=20,e.t0=e.catch(0),console.log("doLogin catch",e.t0),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,this,[[0,20]])}))),function(){return u.apply(this,arguments)})},{key:"onLaunch",value:(s=c(regeneratorRuntime.mark((function e(n){var o,s,c,u=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=a.default.getExtConfigSync(),console.info("[ext] init ext data",o),o.globalConfig&&(console.info("[ext] init ext global config data",o.globalConfig),Object.assign(o,o.globalConfig)),o.http_code=i.service.http_code,Object.assign(a.default.$instance.globalData,o),this.syncStoreConfig("login_code"),this.syncStoreConfig("third_session"),this.syncStoreConfig("user"),console.info("onLaunch param:",n),n&&n.scene&&(console.info("[scene]onLaunch scene",n.scene),a.default.$instance.globalData.scene=n.scene,console.info("[auth]onLaunch end")),n.query.hasOwnProperty("regScene")&&(a.default.$instance.globalData.regScene=n.query.regScene),console.info("onLaunch regScene:",a.default.$instance.globalData.regScene),s=wx.getMenuButtonBoundingClientRect(),wx.getSystemInfo({success:function(e){var a=e.statusBarHeight,t=s.top,n=a+s.height+2*(s.top-a);u.globalData.navHeight=n,u.globalData.navTop=t,u.globalData.windowHeight=e.windowHeight},fail:function(e){console.log(e)}}),t.default.checkSDK(),e.next=17,this.login();case 17:c=e.sent,console.info("[auth]onLaunch end",c),c&&r.default.setAccountInfo({wx_unionid:c.unionid,wx_applet_openid:i.service.appIds.club+"_"+c.openid}),r.default.setCommonData({req_scene:a.default.$instance.globalData.regScene});case 21:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"syncStoreConfig",value:function(e){try{var t=a.default.getStorageSync(e);""!==t&&(console.info("[auth]"+e+" sync success "),a.default.$instance.globalData.auth[e]=t)}catch(a){console.warn("[auth]"+e+" sync fail ")}}},{key:"watch",value:function(e,a){var t=this.globalData,n=t[e];n&&a(n),Object.defineProperty(t,e,{configurable:!0,enumerable:!0,set:function(t){this["_"+e]=t,a(t)},get:function(){return void 0===this["_"+e]?n?(this["_"+e]=n,n):void 0:this["_"+e]}})}}]),o}();App(require("./npm/wepy/lib/wepy.js").default.$createApp(p,{})),require("./_wepylogs.js");