<view class="container" wx:if="{{init}}">
    <view>
        <swiper autoplay="true" class="goods-swiper" indicatorDots="true" interval="5000">
            <swiper-item>
                <image bindtap="previewImage" class="slide-image" data-imageurl="{{detail.product_url}}" mode="aspectFill" src="{{detail.product_url}}"></image>
            </swiper-item>
        </swiper>
        <view class="goods-info-box column">
            <view class="row mt10">
                <text class="goodsinfo">净含量</text>
                <text class="goodsmsg" wx:if="{{detail.net_content!=null}}">{{detail.net_content}}ml</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">酒精度数</text>
                <text class="goodsmsg" wx:if="{{detail.degree!=null}}">{{detail.degree}}</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">香型</text>
                <text class="goodsmsg" wx:if="{{detail.odor_type!=null}}">{{detail.odor_type}}</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">食品添加剂</text>
                <text class="goodsmsg" wx:if="{{detail.addiitive!=null}}">{{detail.addiitive}}</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">箱规</text>
                <text class="goodsmsg" wx:if="{{detail.carton!=null}}">{{detail.carton}}</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">原料</text>
                <text class="goodsmsg" wx:if="{{detail.meterial!=null}}">{{detail.meterial}}</text>
            </view>
            <view class="row mt10">
                <text class="goodsinfo">存储条件</text>
                <text class="goodsmsg" wx:if="{{detail.store_condition!=null}}">{{detail.store_condition}}</text>
            </view>
            <view wx:if="{{detail.strain!='特许经营'}}">
                <view class="row mt10">
                    <text class="goodsinfo">厂家热线</text>
                    <text class="goodsmsg" wx:if="{{detail.factory_phone!=null}}">{{detail.factory_phone}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">厂家名称</text>
                    <text class="goodsmsg" wx:if="{{detail.factory_name!=null}}">{{detail.factory_name}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">厂家地址</text>
                    <text class="goodsmsg" wx:if="{{detail.factory_addr!=null}}">{{detail.factory_addr}}</text>
                </view>
            </view>
            <view wx:else>
                <view class="row mt10">
                    <text class="goodsinfo">客户名称</text>
                    <text class="goodsmsg" wx:if="{{detail.custname!=null}}">{{detail.custname}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">公司网页</text>
                    <text class="goodsmsg" wx:if="{{detail.custhttp!=null}}">{{detail.custhttp}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">联系人</text>
                    <text class="goodsmsg" wx:if="{{detail.custlinker!=null}}">{{detail.custlinker}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">客户电话</text>
                    <text class="goodsmsg" wx:if="{{detail.custtelnum!=null}}">{{detail.custtelnum}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">规格</text>
                    <text class="goodsmsg" wx:if="{{detail.custspec!=null}}">{{detail.custspec}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">零售价</text>
                    <text class="goodsmsg" wx:if="{{detail.custprice!=null}}">{{detail.custprice}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">生产商名</text>
                    <text class="goodsmsg" wx:if="{{detail.custproducer!=null}}">{{detail.custproducer}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">产品产地</text>
                    <text class="goodsmsg" wx:if="{{detail.custorigine!=null}}">{{detail.custorigine}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">附加信息</text>
                    <text class="goodsmsg" wx:if="{{detail.custnote!=null}}">{{detail.custnote}}</text>
                </view>
            </view>
            <view class="detail-box">
                <view class="detail-title row-center" style="border-bottom:2px solid #ccc;">
                    <text class="primary lg" style="height:100rpx;font-size:16px;line-height:100rpx">── 质检报告 ──</text>
                </view>
                <view>
                    <view wx:if="{{zhijianimg.image>=1}}">
                        <image bindtap="previewImage" data-imageurl="{{detail.quality_report_url}}" mode="widthFix" src="{{detail.quality_report_url}}"></image>
                    </view>
                    <view wx:else>
                        <image lazyLoad="lazy-load" mode="widthFix" src="http://wap.exijiu.cn/Public/Qrcode/images/commodity.png"></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <CouponPickPanel></CouponPickPanel>
</view>
