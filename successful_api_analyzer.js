/**
 * 成功API深度分析器
 * 深入分析发现的9个成功API端点
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class SuccessfulAPIAnalyzer {
    constructor() {
        // 已知的认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 发现的成功API端点
        this.successfulAPIs = [
            {
                url: 'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt',
                name: 'jifenCrmCreateJwt',
                priority: 'HIGH', // 最有可能生成新Token
                description: '积分CRM创建JWT'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/Member/getJwt',
                name: 'memberGetJwt',
                priority: 'HIGH', // 最有可能生成新Token
                description: '会员获取JWT'
            },
            {
                url: 'https://statistics.exijiu.com/garden/wechat/login',
                name: 'wechatLogin',
                priority: 'MEDIUM',
                description: '微信登录'
            },
            {
                url: 'https://statistics.exijiu.com/garden/wechat/auth',
                name: 'wechatAuth',
                priority: 'MEDIUM',
                description: '微信认证'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
                name: 'getMemberInfo',
                priority: 'LOW',
                description: '获取会员信息'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/Member/saveLastAuth',
                name: 'saveLastAuth',
                priority: 'MEDIUM',
                description: '保存最后认证'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/Member/hasDataMsCenterUser',
                name: 'hasDataMsCenterUser',
                priority: 'LOW',
                description: '检查数据中心用户'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/Member/getYonyouMemberInfo',
                name: 'getYonyouMemberInfo',
                priority: 'LOW',
                description: '获取用友会员信息'
            },
            {
                url: 'https://wap.exijiu.com/index.php/API/Member/pointsRecord',
                name: 'pointsRecord',
                priority: 'LOW',
                description: '积分记录'
            }
        ];
        
        this.analysisResults = {
            tokenGeneratingAPIs: [],
            userInfoAPIs: [],
            authRelatedAPIs: [],
            newTokens: []
        };
        
        console.log('🔧 成功API深度分析器初始化完成');
        console.log(`🎯 将深入分析 ${this.successfulAPIs.length} 个成功的API端点`);
    }

    /**
     * 深度分析所有成功的API
     */
    async deepAnalyzeAllSuccessfulAPIs() {
        console.log('\n🔍 开始深度分析所有成功的API...');
        
        // 按优先级排序
        const sortedAPIs = this.successfulAPIs.sort((a, b) => {
            const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
        
        for (let i = 0; i < sortedAPIs.length; i++) {
            const api = sortedAPIs[i];
            console.log(`\n🔍 [${i+1}/${sortedAPIs.length}] 深度分析: ${api.name}`);
            console.log(`📍 URL: ${api.url}`);
            console.log(`⭐ 优先级: ${api.priority}`);
            console.log(`📝 描述: ${api.description}`);
            
            const result = await this.deepAnalyzeAPI(api);
            
            if (result.success) {
                console.log('✅ 分析成功！');
                
                // 分类API
                if (result.hasToken) {
                    this.analysisResults.tokenGeneratingAPIs.push(result);
                    console.log('🔑 发现Token生成API！');
                }
                
                if (result.hasUserInfo) {
                    this.analysisResults.userInfoAPIs.push(result);
                    console.log('👤 发现用户信息API！');
                }
                
                if (result.isAuthRelated) {
                    this.analysisResults.authRelatedAPIs.push(result);
                    console.log('🔐 发现认证相关API！');
                }
                
                // 如果发现新Token，立即验证
                if (result.extractedToken) {
                    console.log('🔑 发现新Token，立即验证...');
                    const isValid = await this.validateToken(result.extractedToken, api.url);
                    
                    if (isValid) {
                        this.analysisResults.newTokens.push({
                            token: result.extractedToken,
                            source: api.url,
                            timestamp: new Date().toISOString(),
                            validated: true
                        });
                        console.log('🎉 新Token验证成功！');
                    }
                }
            } else {
                console.log('❌ 分析失败');
            }
        }
        
        return this.analysisResults;
    }

    /**
     * 深度分析单个API
     */
    async deepAnalyzeAPI(api) {
        console.log(`🔧 开始深度分析: ${api.name}`);
        
        const result = {
            api: api,
            success: false,
            responses: [],
            hasToken: false,
            extractedToken: null,
            hasUserInfo: false,
            userInfo: null,
            isAuthRelated: false,
            authInfo: null
        };
        
        // 尝试多种认证方式和参数组合
        const testCases = [
            {
                name: '无认证',
                headers: this.getBaseHeaders(),
                params: {}
            },
            {
                name: '仅login_code',
                headers: {...this.getBaseHeaders(), 'login_code': this.loginCode},
                params: {}
            },
            {
                name: '仅Authorization',
                headers: {...this.getBaseHeaders(), 'Authorization': this.validAuth},
                params: {}
            },
            {
                name: '完整认证',
                headers: {...this.getBaseHeaders(), 'login_code': this.loginCode, 'Authorization': this.validAuth},
                params: {}
            },
            {
                name: '带参数的完整认证',
                headers: {...this.getBaseHeaders(), 'login_code': this.loginCode, 'Authorization': this.validAuth},
                params: { refresh: true, force: true, timestamp: Date.now() }
            }
        ];
        
        for (const testCase of testCases) {
            console.log(`  🧪 测试: ${testCase.name}`);
            
            try {
                // 尝试GET请求
                const getResponse = await this.makeRequest(api.url, 'GET', null, testCase.headers);
                if (getResponse.status === 200) {
                    console.log(`    ✅ GET成功: ${getResponse.data?.msg || '无消息'}`);
                    result.responses.push({
                        method: 'GET',
                        testCase: testCase.name,
                        data: getResponse.data,
                        status: getResponse.status
                    });
                    result.success = true;
                    
                    // 分析响应数据
                    const analysis = this.analyzeResponseData(getResponse.data);
                    if (analysis.hasToken) {
                        result.hasToken = true;
                        result.extractedToken = analysis.token;
                    }
                    if (analysis.hasUserInfo) {
                        result.hasUserInfo = true;
                        result.userInfo = analysis.userInfo;
                    }
                    if (analysis.isAuthRelated) {
                        result.isAuthRelated = true;
                        result.authInfo = analysis.authInfo;
                    }
                }
                
                // 尝试POST请求（如果有参数）
                if (Object.keys(testCase.params).length > 0) {
                    const postResponse = await this.makeRequest(api.url, 'POST', testCase.params, testCase.headers);
                    if (postResponse.status === 200) {
                        console.log(`    ✅ POST成功: ${postResponse.data?.msg || '无消息'}`);
                        result.responses.push({
                            method: 'POST',
                            testCase: testCase.name,
                            data: postResponse.data,
                            status: postResponse.status
                        });
                        result.success = true;
                        
                        // 分析POST响应数据
                        const analysis = this.analyzeResponseData(postResponse.data);
                        if (analysis.hasToken && !result.hasToken) {
                            result.hasToken = true;
                            result.extractedToken = analysis.token;
                        }
                    }
                }
                
            } catch (error) {
                console.log(`    💥 异常: ${error.message}`);
            }
        }
        
        return result;
    }

    /**
     * 分析响应数据
     */
    analyzeResponseData(data) {
        const analysis = {
            hasToken: false,
            token: null,
            hasUserInfo: false,
            userInfo: null,
            isAuthRelated: false,
            authInfo: null
        };
        
        if (!data || typeof data !== 'object') {
            return analysis;
        }
        
        // 检查Token字段
        const tokenFields = ['jwt', 'token', 'auth', 'authorization', 'access_token', 'refresh_token', 'crmtoken', 'authorized_token'];
        for (const field of tokenFields) {
            if (data[field] && typeof data[field] === 'string' && data[field].length > 20) {
                analysis.hasToken = true;
                analysis.token = data[field];
                console.log(`    🔑 发现Token字段: ${field}`);
                break;
            }
        }
        
        // 检查用户信息
        if (data.memberInfo || data.user || data.member || data.userInfo) {
            analysis.hasUserInfo = true;
            analysis.userInfo = data.memberInfo || data.user || data.member || data.userInfo;
            console.log(`    👤 发现用户信息`);
        }
        
        // 检查认证相关信息
        const authFields = ['expire_time', 'expireTime', 'session', 'login', 'auth'];
        for (const field of authFields) {
            if (data[field]) {
                analysis.isAuthRelated = true;
                analysis.authInfo = { [field]: data[field] };
                console.log(`    🔐 发现认证信息: ${field}`);
                break;
            }
        }
        
        return analysis;
    }

    /**
     * 验证Token
     */
    async validateToken(token, sourceAPI) {
        console.log(`🔍 验证Token (来源: ${sourceAPI})`);
        
        // 使用会员信息API验证Token
        const testAPI = 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo';
        
        try {
            const result = await this.makeRequest(testAPI, 'GET', null, {
                ...this.getBaseHeaders(),
                'Authorization': token,
                'login_code': this.loginCode
            });
            
            if (result.success && result.data && !result.data.err) {
                console.log('✅ Token验证成功！');
                return true;
            } else {
                console.log('❌ Token验证失败');
                return false;
            }
            
        } catch (error) {
            console.log('💥 Token验证异常:', error.message);
            return false;
        }
    }

    /**
     * 尝试强制刷新Token
     */
    async forceRefreshToken() {
        console.log('\n🔄 尝试强制刷新Token...');
        
        const refreshAPIs = [
            'https://wap.exijiu.com/index.php/API/Member/getJwt',
            'https://statistics.exijiu.com/api/v2/jifenCrm/createJwt'
        ];
        
        for (const api of refreshAPIs) {
            console.log(`🔧 尝试API: ${api}`);
            
            // 尝试多种强制刷新的方法
            const refreshMethods = [
                {
                    name: '强制刷新参数',
                    method: 'GET',
                    url: `${api}?force=true&refresh=true&t=${Date.now()}`,
                    headers: {...this.getBaseHeaders(), 'login_code': this.loginCode}
                },
                {
                    name: 'POST强制刷新',
                    method: 'POST',
                    url: api,
                    data: { force: true, refresh: true, login_code: this.loginCode },
                    headers: this.getBaseHeaders()
                },
                {
                    name: '带Authorization强制刷新',
                    method: 'GET',
                    url: `${api}?refresh=true`,
                    headers: {...this.getBaseHeaders(), 'login_code': this.loginCode, 'Authorization': this.validAuth}
                }
            ];
            
            for (const method of refreshMethods) {
                console.log(`  🧪 尝试: ${method.name}`);
                
                try {
                    const result = await this.makeRequest(method.url, method.method, method.data, method.headers);
                    
                    if (result.success && result.data) {
                        console.log(`  ✅ 成功: ${result.data.msg || '无消息'}`);
                        
                        // 检查是否有新Token
                        const analysis = this.analyzeResponseData(result.data);
                        if (analysis.hasToken) {
                            console.log('🔑 强制刷新获得新Token！');
                            
                            // 验证新Token
                            const isValid = await this.validateToken(analysis.token, api);
                            if (isValid) {
                                console.log('🎉 强制刷新的Token验证成功！');
                                return analysis.token;
                            }
                        }
                    } else {
                        console.log(`  ❌ 失败: ${result.data?.msg || '未知错误'}`);
                    }
                    
                } catch (error) {
                    console.log(`  💥 异常: ${error.message}`);
                }
            }
        }
        
        return null;
    }

    /**
     * 获取基础请求头
     */
    getBaseHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || this.getBaseHeaders();

        return new Promise((resolve) => {
            const urlObj = new URL(url);

            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({
                            success: false,
                            data: responseData,
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的成功API分析
     */
    async runCompleteSuccessfulAPIAnalysis() {
        console.log('🚀 开始完整的成功API分析...');
        console.log('🎯 深入分析所有发现的成功API端点');

        try {
            // 第一阶段：深度分析所有成功API
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 深度分析所有成功API');
            console.log('='.repeat(60));
            const analysisResults = await this.deepAnalyzeAllSuccessfulAPIs();

            // 第二阶段：尝试强制刷新Token
            console.log('\n' + '='.repeat(60));
            console.log('🔄 第二阶段: 尝试强制刷新Token');
            console.log('='.repeat(60));
            const refreshedToken = await this.forceRefreshToken();

            // 输出分析结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 成功API分析结果');
            console.log('='.repeat(60));

            console.log(`\n📈 分析统计:`);
            console.log(`  🔑 Token生成API: ${analysisResults.tokenGeneratingAPIs.length}`);
            console.log(`  👤 用户信息API: ${analysisResults.userInfoAPIs.length}`);
            console.log(`  🔐 认证相关API: ${analysisResults.authRelatedAPIs.length}`);
            console.log(`  🆕 发现的新Token: ${analysisResults.newTokens.length}`);

            if (analysisResults.tokenGeneratingAPIs.length > 0) {
                console.log('\n🔑 Token生成API详情:');
                analysisResults.tokenGeneratingAPIs.forEach((api, index) => {
                    console.log(`  ${index + 1}. ${api.api.name}`);
                    console.log(`     URL: ${api.api.url}`);
                    if (api.extractedToken) {
                        console.log(`     Token: ${api.extractedToken.substring(0, 50)}...`);
                    }
                });
            }

            if (analysisResults.userInfoAPIs.length > 0) {
                console.log('\n👤 用户信息API详情:');
                analysisResults.userInfoAPIs.forEach((api, index) => {
                    console.log(`  ${index + 1}. ${api.api.name}`);
                    console.log(`     URL: ${api.api.url}`);
                    if (api.userInfo) {
                        console.log(`     用户信息: ${JSON.stringify(api.userInfo)}`);
                    }
                });
            }

            if (analysisResults.newTokens.length > 0) {
                console.log('\n🆕 发现的新Token:');
                analysisResults.newTokens.forEach((tokenInfo, index) => {
                    console.log(`  ${index + 1}. 来源: ${tokenInfo.source}`);
                    console.log(`     Token: ${tokenInfo.token.substring(0, 50)}...`);
                    console.log(`     验证状态: ${tokenInfo.validated ? '已验证' : '未验证'}`);
                    console.log(`     时间: ${tokenInfo.timestamp}`);
                });
            }

            if (refreshedToken) {
                console.log('\n🔄 强制刷新Token:');
                console.log(`  Token: ${refreshedToken.substring(0, 50)}...`);
            }

            // 成功判断
            const hasNewTokens = analysisResults.newTokens.length > 0 || refreshedToken;
            const hasTokenAPIs = analysisResults.tokenGeneratingAPIs.length > 0;
            const hasUserInfoAPIs = analysisResults.userInfoAPIs.length > 0;

            if (hasNewTokens || hasTokenAPIs || hasUserInfoAPIs) {
                console.log('\n🎊 成功API分析圆满成功！');

                if (hasNewTokens) {
                    console.log('🔑 发现了新的有效Token！');
                }

                if (hasTokenAPIs) {
                    console.log('🔧 发现了Token生成API！');
                }

                if (hasUserInfoAPIs) {
                    console.log('👤 发现了用户信息API！');
                }

                console.log('\n💡 下一步建议:');
                console.log('1. 使用新发现的Token进行后续操作');
                console.log('2. 深入研究Token生成API的参数');
                console.log('3. 建立自动化Token刷新机制');

                return {
                    success: true,
                    analysisResults: analysisResults,
                    refreshedToken: refreshedToken,
                    newTokens: analysisResults.newTokens
                };
            } else {
                console.log('\n🤔 未发现新的Token，但获得了有价值的API信息');
                console.log('💡 所有API都可以正常访问，这本身就是重要发现');

                return {
                    success: true,
                    analysisResults: analysisResults,
                    message: '虽然未发现新Token，但所有API都可访问'
                };
            }

        } catch (error) {
            console.log('\n❌ 成功API分析失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = SuccessfulAPIAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new SuccessfulAPIAnalyzer();

    console.log('🔍 成功API深度分析器');
    console.log('🎯 深入分析发现的9个成功API端点');
    console.log('🔑 重点寻找Token生成和用户信息API');
    console.log('');

    // 运行完整的成功API分析
    analyzer.runCompleteSuccessfulAPIAnalysis().then(result => {
        if (result.success) {
            console.log('\n🎉 成功API分析完成！');

            if (result.newTokens && result.newTokens.length > 0) {
                console.log('\n🎊 最重要的发现 - 新Token:');
                result.newTokens.forEach(token => {
                    console.log(`🔑 ${token.token.substring(0, 100)}...`);
                });
            }

            if (result.refreshedToken) {
                console.log('\n🔄 强制刷新获得的Token:');
                console.log(`🔑 ${result.refreshedToken.substring(0, 100)}...`);
            }
        } else {
            console.log('\n😔 成功API分析遇到问题');
            console.log('💡 但这个深度分析过程本身很有价值');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
