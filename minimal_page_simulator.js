/**
 * 最小化页面模拟器
 * 基于测试结果，使用最少的认证信息进入种植页面
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class MinimalPageSimulator {
    constructor() {
        // 只保留必要的认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxUU9NbjJ3amlRVlZ4RjVLRzN2Smc3QSIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTY1MTc1MzM0M30.VbrOMiLdmQFciDOe8biqYfl2RYoEP_jkWp2ZQ3EpdZM';
        
        // 基于测试结果，使用有效的API域名
        this.workingApiUrl = 'https://apimallwm.exijiu.com/api';
        
        // 小程序基础配置
        this.appConfig = {
            appId: 'wx489f950decfeb93e',
            version: 'v3.2.6',
            path: 'pages/plant/index'
        };
        
        // 页面状态
        this.pageState = {
            sessionId: this.generateSessionId(),
            isEntered: false,
            canAccessPublicAPIs: false,
            needsAuthForPrivateAPIs: true
        };
        
        console.log('🔧 最小化页面模拟器初始化完成');
        console.log('🌐 使用API域名:', this.workingApiUrl);
        console.log('📱 目标页面:', this.appConfig.path);
        console.log('💡 策略: 最小化认证，专注页面进入');
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `minimal_session_${timestamp}_${random}`;
    }

    /**
     * 构建最小化请求头
     */
    buildMinimalHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
            'login_code': this.loginCode,
            'X-WX-AppId': this.appConfig.appId
        };
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        const headers = this.buildMinimalHeaders();
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.workingApiUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 测试公开API访问
     */
    async testPublicAPIs() {
        console.log('\n🌐 测试公开API访问...');
        
        const publicAPIs = [
            { path: '/garden/notice/index', name: '通知列表' },
            { path: '/garden/poptips/random', name: '随机提示' },
            { path: '/banners', name: '横幅信息' },
            { path: '/garden/tasks/index', name: '任务列表' },
            { path: '/garden/notice/realScene', name: '真实场景' }
        ];
        
        let successCount = 0;
        
        for (const api of publicAPIs) {
            try {
                console.log(`🧪 测试${api.name}: ${api.path}`);
                const result = await this.makeRequest(api.path);
                
                if (result.success) {
                    console.log(`✅ ${api.name} - 访问成功`);
                    successCount++;
                } else {
                    console.log(`⚠️ ${api.name} - 访问失败: ${result.data?.msg || '未知错误'}`);
                }
                
            } catch (error) {
                console.log(`❌ ${api.name} - 请求失败: ${error.message}`);
            }
        }
        
        this.pageState.canAccessPublicAPIs = successCount > 0;
        console.log(`\n📊 公开API测试结果: ${successCount}/${publicAPIs.length} 成功`);
        
        return successCount > 0;
    }

    /**
     * 模拟页面进入流程
     */
    async simulatePageEntry() {
        console.log('\n📱 模拟页面进入流程...');
        
        try {
            // 1. 页面初始化
            console.log('🔧 步骤1: 页面初始化...');
            this.pageState.sessionId = this.generateSessionId();
            
            // 2. 测试基础连接
            console.log('🌐 步骤2: 测试基础连接...');
            const publicAPISuccess = await this.testPublicAPIs();
            
            if (!publicAPISuccess) {
                console.log('❌ 基础连接失败，无法进入页面');
                return false;
            }
            
            // 3. 模拟页面加载事件
            console.log('📄 步骤3: 模拟页面加载事件...');
            await this.simulatePageLoad();
            
            // 4. 模拟页面显示事件
            console.log('👁️ 步骤4: 模拟页面显示事件...');
            await this.simulatePageShow();
            
            // 5. 标记页面已进入
            this.pageState.isEntered = true;
            
            console.log('\n🎉 页面进入流程完成！');
            return true;
            
        } catch (error) {
            console.log('\n❌ 页面进入流程失败:', error.message);
            return false;
        }
    }

    /**
     * 模拟页面加载 (onLoad)
     */
    async simulatePageLoad() {
        console.log('📄 执行页面onLoad生命周期...');
        
        try {
            // 获取页面基础数据
            const result = await this.makeRequest('/garden/notice/index');
            if (result.success) {
                console.log('✅ 页面基础数据加载成功');
            }
            
            // 模拟页面初始化完成
            console.log('✅ 页面onLoad执行完成');
            
        } catch (error) {
            console.log('⚠️ 页面onLoad执行失败:', error.message);
        }
    }

    /**
     * 模拟页面显示 (onShow)
     */
    async simulatePageShow() {
        console.log('👁️ 执行页面onShow生命周期...');
        
        try {
            // 刷新页面数据
            const promises = [
                this.makeRequest('/garden/notice/realScene'),
                this.makeRequest('/garden/poptips/random')
            ];
            
            const results = await Promise.allSettled(promises);
            
            let successCount = 0;
            results.forEach((result, index) => {
                const names = ['真实场景', '随机提示'];
                if (result.status === 'fulfilled' && result.value.success) {
                    console.log(`✅ ${names[index]}刷新成功`);
                    successCount++;
                } else {
                    console.log(`⚠️ ${names[index]}刷新失败`);
                }
            });
            
            console.log(`✅ 页面onShow执行完成 (${successCount}/2 成功)`);
            
        } catch (error) {
            console.log('⚠️ 页面onShow执行失败:', error.message);
        }
    }

    /**
     * 保持页面活跃
     */
    async keepPageActive(intervalMinutes = 10) {
        if (!this.pageState.isEntered) {
            console.log('⚠️ 页面未进入，无法保持活跃');
            return;
        }
        
        console.log(`\n🔄 开始保持页面活跃，间隔${intervalMinutes}分钟`);
        
        const interval = setInterval(async () => {
            console.log('\n💓 刷新页面状态...');
            
            try {
                // 简单的心跳请求
                const result = await this.makeRequest('/garden/notice/index');
                if (result.success) {
                    console.log('✅ 页面心跳成功');
                } else {
                    console.log('⚠️ 页面心跳失败');
                }
                
            } catch (error) {
                console.log('❌ 页面心跳错误:', error.message);
            }
            
        }, intervalMinutes * 60 * 1000);
        
        // 处理退出信号
        process.on('SIGINT', () => {
            console.log('\n🛑 收到退出信号，正在退出...');
            clearInterval(interval);
            this.simulatePageHide().then(() => {
                console.log('✅ 页面已安全退出');
                process.exit(0);
            });
        });
        
        return interval;
    }

    /**
     * 模拟页面隐藏 (onHide)
     */
    async simulatePageHide() {
        console.log('\n🙈 模拟页面隐藏 (onHide)...');
        
        try {
            // 清理页面状态
            this.pageState.isEntered = false;
            
            console.log('✅ 页面隐藏完成');
            
        } catch (error) {
            console.log('❌ 页面隐藏失败:', error.message);
        }
    }

    /**
     * 主要方法：进入种植页面
     */
    async enterPlantPage() {
        console.log('🌱 开始进入种植页面...');
        console.log('📱 页面路径:', this.appConfig.path);
        console.log('🆔 会话ID:', this.pageState.sessionId);
        console.log('💡 策略: 最小化认证，专注页面进入');
        
        try {
            // 执行页面进入流程
            const success = await this.simulatePageEntry();
            
            if (success) {
                console.log('\n🎉 成功进入种植页面！');
                console.log('📊 页面状态:');
                console.log(`  ✅ 页面已进入: ${this.pageState.isEntered}`);
                console.log(`  ✅ 公开API可访问: ${this.pageState.canAccessPublicAPIs}`);
                console.log(`  ⚠️ 私有API需认证: ${this.pageState.needsAuthForPrivateAPIs}`);
                console.log(`  🆔 会话ID: ${this.pageState.sessionId}`);
                
                console.log('\n💡 功能说明:');
                console.log('  ✅ 成功模拟页面进入流程');
                console.log('  ✅ 可访问公开API接口');
                console.log('  ✅ 页面生命周期正常执行');
                console.log('  ⚠️ 种植操作需要authtoken');
                
                return true;
            } else {
                console.log('\n❌ 进入种植页面失败');
                return false;
            }
            
        } catch (error) {
            console.log('\n❌ 进入种植页面异常:', error.message);
            return false;
        }
    }

    /**
     * 获取页面状态
     */
    getPageState() {
        return {
            ...this.pageState,
            appConfig: this.appConfig,
            workingApiUrl: this.workingApiUrl,
            hasLoginCode: !!this.loginCode,
            currentTime: new Date().toLocaleString('zh-CN')
        };
    }
}

// 导出类
module.exports = MinimalPageSimulator;

// 如果直接运行此文件
if (require.main === module) {
    const simulator = new MinimalPageSimulator();
    
    console.log('🌱 最小化页面模拟器');
    console.log('🎯 目标: 用最少认证信息进入种植页面');
    console.log('💡 基于测试结果优化的版本');
    console.log('');
    
    // 进入种植页面
    simulator.enterPlantPage().then(success => {
        if (success) {
            console.log('\n🎊 页面进入成功！');
            console.log('🔄 现在将保持页面活跃状态...');
            console.log('🛑 按 Ctrl+C 退出');
            
            // 保持页面活跃
            simulator.keepPageActive(10);
            
        } else {
            console.log('\n😔 页面进入失败');
            process.exit(1);
        }
    }).catch(error => {
        console.error('💥 程序异常:', error);
        process.exit(1);
    });
}
