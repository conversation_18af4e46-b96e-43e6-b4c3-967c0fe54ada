Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./../../npm/wepy/lib/wepy.js")),r=n(require("./../../utils/WxUtils.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(n){function i(){var e,t,r;o(this,i);for(var n=arguments.length,u=Array(n),c=0;c<n;c++)u[c]=arguments[c];return t=r=a(this,(e=i.__proto__||Object.getPrototypeOf(i)).call.apply(e,[this].concat(u))),r.data={init:!1,appId:"",path:""},r.config={navigationBarTitleText:"跳转中..."},a(r,t)}var u,c;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,t.default.page),e(i,[{key:"onLoad",value:(u=regeneratorRuntime.mark((function e(t){var n,o,a,i=t.path,u=t.q;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i?this.path=decodeURIComponent(i):u&&(n=decodeURIComponent(u),o=r.default.parseUrl(n),(a=r.default.getUrlkey(o.query)).path&&(this.path=decodeURIComponent(a.path))),this.$redirect(this.path);case 2:case"end":return e.stop()}}),e,this)})),c=function(){var e=u.apply(this,arguments);return new Promise((function(t,r){return function n(o,a){try{var i=e[o](a),u=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(u)}("next")}))},function(e){return c.apply(this,arguments)})}]),i}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(i,"pages/web/web2MiniProgram"));