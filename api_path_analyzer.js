/**
 * API路径分析器
 * 基于错误信息分析正确的API路径
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class APIPathAnalyzer {
    constructor() {
        // 已知的认证信息
        this.auth = {
            loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY'
        };
        
        // 基于错误分析的可能API路径
        this.possibleAPIPaths = [
            // 基础路径变体
            'https://wap.exijiu.com/index.php/API/member/getJwt',
            'https://wap.exijiu.com/index.php/api/member/getJwt',
            'https://wap.exijiu.com/index.php/Member/getJwt',
            'https://wap.exijiu.com/index.php/member/getJwt',
            
            // 不同的控制器名称
            'https://wap.exijiu.com/index.php/API/auth/getJwt',
            'https://wap.exijiu.com/index.php/API/user/getJwt',
            'https://wap.exijiu.com/index.php/API/login/getJwt',
            
            // 不同的方法名称
            'https://wap.exijiu.com/index.php/API/Member/getJifenShopJwt',
            'https://wap.exijiu.com/index.php/API/member/getJifenShopJwt',
            'https://wap.exijiu.com/index.php/API/Member/jwt',
            'https://wap.exijiu.com/index.php/API/Member/token',
            
            // 基于源码发现的路径
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
            'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getPlantingInfo',
            
            // 其他可能的路径
            'https://wap.exijiu.com/api/member/getJwt',
            'https://wap.exijiu.com/api/auth/getJwt',
            'https://wap.exijiu.com/member/getJwt',
            'https://wap.exijiu.com/auth/getJwt'
        ];
        
        console.log('🔧 API路径分析器初始化完成');
        console.log('🎯 基于错误信息分析正确的API路径');
        console.log(`📋 待测试路径数量: ${this.possibleAPIPaths.length}`);
    }

    /**
     * 分析错误信息
     */
    analyzeErrorMessage() {
        console.log('\n🔍 分析错误信息...');
        
        console.log('📋 关键错误信息:');
        console.log('- "无法加载控制器:Member"');
        console.log('- "FILE: /home/<USER>/www/ThinkPHP/Library/Think/App.class.php"');
        console.log('- 状态码: 200 (说明服务器正常响应)');
        
        console.log('\n💡 错误分析:');
        console.log('1. 使用的是ThinkPHP框架');
        console.log('2. 控制器名称"Member"可能不存在或路径错误');
        console.log('3. 可能需要小写的"member"');
        console.log('4. 可能需要不同的API路径结构');
        
        console.log('\n🎯 解决策略:');
        console.log('1. 测试不同的控制器名称大小写');
        console.log('2. 测试不同的API路径结构');
        console.log('3. 测试已知有效的API端点');
        console.log('4. 分析响应模式找出正确路径');
        
        return {
            framework: 'ThinkPHP',
            issue: 'controller_not_found',
            controller: 'Member',
            suggestions: ['lowercase', 'different_structure', 'test_known_endpoints']
        };
    }

    /**
     * 系统性测试所有可能的API路径
     */
    async testAllPossiblePaths() {
        console.log('\n🧪 系统性测试所有可能的API路径...');
        
        const results = {
            successful: [],
            failed: [],
            errors: []
        };
        
        for (let i = 0; i < this.possibleAPIPaths.length; i++) {
            const apiPath = this.possibleAPIPaths[i];
            console.log(`\n🔍 [${i+1}/${this.possibleAPIPaths.length}] 测试: ${apiPath}`);
            
            try {
                const result = await this.testSingleAPIPath(apiPath);
                
                if (result.success) {
                    console.log('✅ 路径有效！');
                    results.successful.push({
                        path: apiPath,
                        response: result.response
                    });
                    
                    // 如果找到JWT相关的响应，立即报告
                    if (result.response && (result.response.jwt || result.response.token || result.response.auth)) {
                        console.log('🎉 发现JWT相关响应！');
                        console.log('📊 响应数据:', JSON.stringify(result.response, null, 2));
                    }
                } else {
                    console.log('❌ 路径无效');
                    results.failed.push({
                        path: apiPath,
                        error: result.error,
                        statusCode: result.statusCode
                    });
                }
                
            } catch (error) {
                console.log(`💥 测试异常: ${error.message}`);
                results.errors.push({
                    path: apiPath,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 测试单个API路径
     */
    async testSingleAPIPath(apiPath) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'login_code': this.auth.loginCode,
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'Referer': 'https://servicewechat.com/wx489f950decfeb93e/v3.2.6/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        };
        
        try {
            const response = await this.makeRequest(apiPath, 'GET', null, headers);
            
            // 检查是否是有效的JSON响应
            if (response.statusCode === 200 && !response.rawResponse) {
                return {
                    success: true,
                    response: response
                };
            } else {
                return {
                    success: false,
                    error: response.msg || 'Invalid response',
                    statusCode: response.statusCode
                };
            }
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                statusCode: 0
            };
        }
    }

    /**
     * 分析测试结果
     */
    analyzeTestResults(results) {
        console.log('\n📊 分析测试结果...');
        
        console.log(`📈 测试统计:`);
        console.log(`  ✅ 成功: ${results.successful.length}`);
        console.log(`  ❌ 失败: ${results.failed.length}`);
        console.log(`  💥 异常: ${results.errors.length}`);
        
        if (results.successful.length > 0) {
            console.log('\n🎉 发现有效的API路径:');
            results.successful.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.path}`);
                if (result.response && (result.response.jwt || result.response.token)) {
                    console.log(`     🔑 包含Token信息！`);
                }
            });
        }
        
        if (results.failed.length > 0) {
            console.log('\n❌ 失败的路径模式分析:');
            const errorPatterns = {};
            results.failed.forEach(result => {
                const error = result.error || 'unknown';
                if (!errorPatterns[error]) {
                    errorPatterns[error] = [];
                }
                errorPatterns[error].push(result.path);
            });
            
            Object.entries(errorPatterns).forEach(([error, paths]) => {
                console.log(`  "${error}": ${paths.length}个路径`);
            });
        }
        
        return {
            hasValidPaths: results.successful.length > 0,
            hasTokenPaths: results.successful.some(r => r.response && (r.response.jwt || r.response.token)),
            validPaths: results.successful,
            errorPatterns: results.failed
        };
    }

    /**
     * HTTP请求方法
     */
    async makeRequest(url, method = 'GET', data = null, headers = null) {
        const requestHeaders = headers || {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: requestHeaders,
                timeout: 5000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve(jsonData);
                    } catch (e) {
                        // 如果不是JSON，返回错误信息
                        resolve({
                            err: 1,
                            msg: 'Non-JSON response',
                            rawResponse: responseData.substring(0, 200),
                            statusCode: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ 
                    err: 1, 
                    msg: 'Request error',
                    statusCode: 0 
                });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ 
                    err: 1, 
                    msg: 'Request timeout',
                    statusCode: 0 
                });
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的API路径分析
     */
    async runCompleteAPIPathAnalysis() {
        console.log('🚀 开始完整的API路径分析...');
        console.log('🎯 基于错误信息找出正确的API路径');
        
        try {
            // 1. 分析错误信息
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第一阶段: 分析错误信息');
            console.log('='.repeat(60));
            const errorAnalysis = this.analyzeErrorMessage();
            
            // 2. 系统性测试所有可能路径
            console.log('\n' + '='.repeat(60));
            console.log('🧪 第二阶段: 系统性测试所有可能路径');
            console.log('='.repeat(60));
            const testResults = await this.testAllPossiblePaths();
            
            // 3. 分析测试结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 第三阶段: 分析测试结果');
            console.log('='.repeat(60));
            const analysis = this.analyzeTestResults(testResults);
            
            // 输出最终结果
            console.log('\n' + '='.repeat(60));
            console.log('📊 API路径分析结果');
            console.log('='.repeat(60));
            
            if (analysis.hasValidPaths) {
                console.log('\n🎉 API路径分析成功！');
                console.log(`✅ 发现 ${analysis.validPaths.length} 个有效路径`);
                
                if (analysis.hasTokenPaths) {
                    console.log('🔑 发现包含Token的API路径！');
                    const tokenPaths = analysis.validPaths.filter(r => r.response && (r.response.jwt || r.response.token));
                    tokenPaths.forEach(result => {
                        console.log(`🎯 ${result.path}`);
                        console.log(`📊 响应: ${JSON.stringify(result.response, null, 2)}`);
                    });
                    
                    return {
                        success: true,
                        hasTokenAPI: true,
                        tokenPaths: tokenPaths,
                        allValidPaths: analysis.validPaths
                    };
                } else {
                    console.log('💡 发现有效路径，但未包含Token信息');
                    return {
                        success: true,
                        hasTokenAPI: false,
                        validPaths: analysis.validPaths
                    };
                }
            } else {
                console.log('\n🤔 未发现有效的API路径');
                console.log('💡 可能需要更深入的路径分析');
                
                return {
                    success: false,
                    errorAnalysis: errorAnalysis,
                    testResults: testResults
                };
            }
            
        } catch (error) {
            console.log('\n❌ API路径分析失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 导出类
module.exports = APIPathAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new APIPathAnalyzer();
    
    console.log('🔍 API路径分析器');
    console.log('🎯 基于错误信息分析正确的API路径');
    console.log('🔑 寻找真正有效的getJwt API端点');
    console.log('');
    
    // 运行完整的API路径分析
    analyzer.runCompleteAPIPathAnalysis().then(result => {
        if (result.success) {
            console.log('\n🎉 API路径分析成功！');
            
            if (result.hasTokenAPI) {
                console.log('\n🎊 最重要的发现 - 找到Token API:');
                result.tokenPaths.forEach(path => {
                    console.log(`🔑 ${path.path}`);
                });
            }
        } else {
            console.log('\n🤔 API路径分析需要进一步研究');
            console.log('💡 建议进行更深入的路径探索');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
