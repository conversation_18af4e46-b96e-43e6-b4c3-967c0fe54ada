Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=n(require("./../../../npm/wepy/lib/wepy.js")),t=n(require("./../../../mixins/router.js")),o=n(require("./../../goods/discount_badge.js"));function n(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(n){function s(){var e,n,u;r(this,s);for(var a=arguments.length,c=Array(a),p=0;p<a;p++)c[p]=arguments[p];return n=u=i(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(c))),u.props={goods:{}},u.methods={},u.$repeat={"[goods]":{com:"DiscountBadge",props:"goods.sync"}},u.$props={DiscountBadge:{"xmlns:v-bind":{value:"",for:"[goods]",item:"item",index:"i",key:"i"},"v-bind:goods.sync":{value:"item",type:"item",for:"[goods]",item:"item",index:"i",key:"i"}}},u.$events={},u.components={DiscountBadge:o.default},u.mixins=[t.default],i(u,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,e.default.component),s}();exports.default=s;