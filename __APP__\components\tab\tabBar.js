Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}(),o=require("./../../npm/wepy/lib/wepy.js"),n=(e=o)&&e.__esModule?e:{default:e};function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var i=function(e){function o(){var e,t,n;r(this,o);for(var i=arguments.length,u=Array(i),c=0;c<i;c++)u[c]=arguments[c];return t=n=a(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(u))),n.props={active:{twoWay:!0}},n.data={},n.methods={change:function(e,t){this.active=+e}},a(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:function(){}}]),o}();exports.default=i;