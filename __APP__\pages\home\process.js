Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,r,i){return r&&e(t.prototype,r),i&&e(t,i),t}}(),t=n(require("./../../npm/wepy/lib/wepy.js")),r=(n(require("./../../api/auth.js")),n(require("./../../api/goods.js")),n(require("./../../mixins/base.js"))),i=n(require("./../../components/goods/detail_list.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(n){function u(){var e,t,n;o(this,u);for(var c=arguments.length,p=Array(c),l=0;l<c;l++)p[l]=arguments[l];return t=n=a(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(p))),n.data={init:!1,detailimg:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(2).jpg","http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(3).jpg"],stepsecend:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(4).jpg"],stepthird:["http://wap.exijiu.cn/Public/Qrcode/images/productCodeDetail.jpg"],checkstyleone:["http://wap.exijiu.cn/Public/Qrcode/images/telQuery.jpg"],checkstyletwo:["http://wap.exijiu.cn/Public/Qrcode/images/messageQuery.jpg"],checkstylethree:["http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(8).jpg","http://wap.exijiu.cn/Public/Qrcode/default/fwimage/1%20(9).jpg"],checkstylefour:["http://wap.exijiu.cn/Public/Qrcode/images/guide1.jpg","http://wap.exijiu.cn/Public/Qrcode/images/guide2.jpg"],reportenter:["http://wap.exijiu.cn/Public/Qrcode/images/feedback-entry.jpg"],page:{list:[]},commentInit:!1,isShow:!0},n.methods={previewImage:function(e){var t=e.currentTarget.dataset.imageurl;wx.previewImage({current:[t],urls:[t]})}},n.computed={},n.components={DetailList:i.default},n.mixins=[r.default],n.config={navigationBarTitleText:"防伪查询流程"},a(n,t)}var c,p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.page),e(u,[{key:"onLoad",value:(c=regeneratorRuntime.mark((function e(t){return t.goodsId,regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.loaded();case 1:case"end":return e.stop()}}),e,this)})),p=function(){var e=c.apply(this,arguments);return new Promise((function(t,r){return function i(n,o){try{var a=e[n](o),u=a.value}catch(e){return void r(e)}if(!a.done)return Promise.resolve(u).then((function(e){i("next",e)}),(function(e){i("throw",e)}));t(u)}("next")}))},function(e){return p.apply(this,arguments)})},{key:"onUnload",value:function(){}}]),u}();exports.default=u;