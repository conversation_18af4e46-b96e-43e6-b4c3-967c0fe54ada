Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),r=o(require("./../../mixins/router.js")),n=o(require("./../../mixins/base_com.js"));function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(o){function u(){var e,t,o;a(this,u);for(var s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];return t=o=i(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(c))),o.props={param:{}},o.data={},o.methods={search:function(){var e=JSON.stringify(this.param);this.$root.$navigate("/pages/goods/search?param="+e)}},o.mixins=[n.default,r.default],i(o,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,t.default.component),e(u,[{key:"onLoad",value:function(){}}]),u}();exports.default=u;