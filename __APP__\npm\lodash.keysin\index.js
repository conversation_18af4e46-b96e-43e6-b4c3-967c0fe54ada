var r=require("./../lodash.isarguments/index.js"),t=require("./../lodash.isarray/index.js"),e=/^\d+$/,n=Object.prototype.hasOwnProperty;function o(r,t){return t=null==t?9007199254740991:t,(r="number"==typeof r||e.test(r)?+r:-1)>-1&&r%1==0&&r<t}module.exports=function(e){if(null==e)return[];var u,a;a=typeof(u=e),(!u||"object"!=a&&"function"!=a)&&(e=Object(e));var c=e.length;c=c&&function(r){return"number"==typeof r&&r>-1&&r%1==0&&r<=9007199254740991}(c)&&(t(e)||r(e))&&c||0;for(var i=e.constructor,s=-1,f="function"==typeof i&&i.prototype===e,p=Array(c),l=c>0;++s<c;)p[s]=s+"";for(var y in e)l&&o(y,c)||"constructor"==y&&(f||!n.call(e,y))||p.push(y);return p};