/**
 * 简单文件搜索
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始简单文件搜索...');

// 检查目录
const appPath = '__APP__';
console.log(`📁 检查路径: ${appPath}`);

if (!fs.existsSync(appPath)) {
    console.log(`❌ 路径不存在: ${appPath}`);
    process.exit(1);
}

console.log('✅ 路径存在');

// 读取目录内容
try {
    const items = fs.readdirSync(appPath);
    console.log(`📊 目录包含 ${items.length} 个项目:`);
    
    items.slice(0, 10).forEach(item => {
        const fullPath = path.join(appPath, item);
        const stat = fs.statSync(fullPath);
        const type = stat.isDirectory() ? '📁' : '📄';
        console.log(`  ${type} ${item}`);
    });
    
    // 查找JS文件
    const jsFiles = [];
    
    function findJSFiles(dir) {
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    findJSFiles(fullPath);
                } else if (item.endsWith('.js')) {
                    jsFiles.push(fullPath);
                }
            }
        } catch (error) {
            console.log(`❌ 读取目录失败: ${dir}`);
        }
    }
    
    findJSFiles(appPath);
    console.log(`\n📊 找到 ${jsFiles.length} 个JS文件:`);
    
    jsFiles.slice(0, 10).forEach(file => {
        console.log(`  📄 ${file}`);
    });
    
    // 搜索关键词
    const keywords = ['refreshtoken', 'refresh_token', 'jwt', 'authorization', 'getJwt', 'createJwt'];
    
    console.log('\n🔍 搜索关键词...');
    
    for (const keyword of keywords) {
        console.log(`\n🔍 搜索: ${keyword}`);
        let found = false;
        
        for (const filePath of jsFiles.slice(0, 5)) { // 只搜索前5个文件作为测试
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                
                if (content.toLowerCase().includes(keyword.toLowerCase())) {
                    console.log(`  ✅ 在 ${filePath} 中找到`);
                    
                    // 显示匹配的行
                    const lines = content.split('\n');
                    for (let i = 0; i < lines.length; i++) {
                        if (lines[i].toLowerCase().includes(keyword.toLowerCase())) {
                            console.log(`    ${i + 1}: ${lines[i].trim()}`);
                            found = true;
                            break; // 只显示第一个匹配
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ 读取文件失败: ${filePath}`);
            }
        }
        
        if (!found) {
            console.log(`  ❌ 未找到 ${keyword}`);
        }
    }
    
} catch (error) {
    console.log(`❌ 读取目录失败: ${error.message}`);
}
