Object.defineProperty(exports,"__esModule",{value:!0});var e=n(require("./../../npm/wepy/lib/wepy.js")),t=n(require("./../../components/plant/head/modal.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(n){function s(){var e,n,a;o(this,s);for(var i=arguments.length,p=Array(i),c=0;c<i;c++)p[c]=arguments[c];return n=a=r(this,(e=s.__proto__||Object.getPrototypeOf(s)).call.apply(e,[this].concat(p))),a.data={tipMsg:"您好！",showMsg:!1},a.$repeat={},a.$props={TipsModal:{"xmlns:v-on":"","xmlns:v-bind":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},a.$events={TipsModal:{"v-on:closeIt":"closeIt"}},a.components={TipsModal:t.default},a.methods={bindbt:function(){},closeIt:function(){this.showMsg=!1,this.$apply()}},a.config={navigationBarBackgroundColor:"#71B5FA",navigationBarTitleText:"鰼部酒谷"},r(a,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,e.default.page),s}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(s,"pages/plant/test"));