Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./native.js"),n=(e=r)&&e.__esModule?e:{default:e};var i={map:{},mq:[],running:[],MAX_REQUEST:5,push:function(e){for(e.t=+new Date;this.mq.indexOf(e.t)>-1||this.running.indexOf(e.t)>-1;)e.t+=10*Math.random()>>0;this.mq.push(e.t),this.map[e.t]=e},next:function(){var e=this;if(0!==this.mq.length&&this.running.length<this.MAX_REQUEST-1){var t=this.mq.shift(),r=this.map[t],n=r.complete;return r.complete=function(){for(var t=arguments.length,i=Array(t),o=0;o<t;o++)i[o]=arguments[o];e.running.splice(e.running.indexOf(r.t),1),delete e.map[r.t],n&&n.apply(r,i),e.next()},this.running.push(r.t),wx.request(r)}},request:function(e){return e="string"==typeof(e=e||{})?{url:e}:e,this.push(e),this.next()}},o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$addons={},this.$interceptors={},this.$pages={}}return t(e,[{key:"$init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$initAPI(e,t.noPromiseAPI),this.$wxapp=getApp()}},{key:"use",value:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];"string"==typeof e&&this[e]?(this.$addons[e]=1,this[e](r)):this.$addons[e.name]=new e(r)}},{key:"intercept",value:function(e,t){this.$interceptors[e]=t}},{key:"promisify",value:function(){}},{key:"requestfix",value:function(){}},{key:"$initAPI",value:function(e,t){var r=this,o={stopRecord:!0,getRecorderManager:!0,pauseVoice:!0,stopVoice:!0,pauseBackgroundAudio:!0,stopBackgroundAudio:!0,getBackgroundAudioManager:!0,createAudioContext:!0,createInnerAudioContext:!0,createVideoContext:!0,createCameraContext:!0,createMapContext:!0,canIUse:!0,startAccelerometer:!0,stopAccelerometer:!0,startCompass:!0,stopCompass:!0,onBLECharacteristicValueChange:!0,onBLEConnectionStateChange:!0,hideToast:!0,hideLoading:!0,showNavigationBarLoading:!0,hideNavigationBarLoading:!0,navigateBack:!0,createAnimation:!0,pageScrollTo:!0,createSelectorQuery:!0,createCanvasContext:!0,createContext:!0,drawCanvas:!0,hideKeyboard:!0,stopPullDownRefresh:!0,reportAnalytics:!0,arrayBufferToBase64:!0,base64ToArrayBuffer:!0};if(t)if(Array.isArray(t))t.forEach((function(e){return o[e]=!0}));else for(var a in t)o[a]=t[a];Object.keys(wx).forEach((function(t){o[t]||"on"===t.substr(0,2)||/\w+Sync$/.test(t)?(Object.defineProperty(n.default,t,{get:function(){return function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return wx[t].apply(wx,r)}}}),e[t]=n.default[t]):(Object.defineProperty(n.default,t,{get:function(){return function(e){if(e=e||{},r.$interceptors[t]&&r.$interceptors[t].config){var n=r.$interceptors[t].config.call(r,e);if(!1===n)return r.$addons.promisify?Promise.reject("aborted by interceptor"):void(e.fail&&e.fail("aborted by interceptor"));e=n}if("request"===t&&(e="string"==typeof e?{url:e}:e),"string"==typeof e)return wx[t](e);if(r.$addons.promisify){var o=void 0,a=new Promise((function(n,a){var s={};["fail","success","complete"].forEach((function(i){s[i]=e[i],e[i]=function(e){r.$interceptors[t]&&r.$interceptors[t][i]&&(e=r.$interceptors[t][i].call(r,e)),"success"===i?n(e):"fail"===i&&a(e)}})),r.$addons.requestfix&&"request"===t?i.request(e):o=wx[t](e)}));return"uploadFile"!==t&&"downloadFile"!==t||(a.progress=function(e){return o.onProgressUpdate(e),a},a.abort=function(e){return e&&e(),o.abort(),a}),a}var s={};if(["fail","success","complete"].forEach((function(n){s[n]=e[n],e[n]=function(e){r.$interceptors[t]&&r.$interceptors[t][n]&&(e=r.$interceptors[t][n].call(r,e)),s[n]&&s[n].call(r,e)}})),!r.$addons.requestfix||"request"!==t)return wx[t](e);i.request(e)}}}),e[t]=n.default[t])}))}}]),e}();exports.default=o;