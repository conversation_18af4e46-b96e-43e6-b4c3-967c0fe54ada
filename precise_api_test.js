/**
 * 精确的API测试 - 基于发现的可访问域名
 * 测试不同的路径和方法
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Token配置
const tokens = {
    authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI',
    loginCode: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY'
};

console.log('🎯 精确API测试 - 基于可访问的域名');

// 构建完整的请求头
function buildHeaders() {
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
        'Referer': 'https://servicewechat.com/wx489f950decfeb93e/devtools/page-frame.html',
        'X-Requested-With': 'XMLHttpRequest',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'Authorization': tokens.authorization,
        'login_code': tokens.loginCode,
        'X-WX-AppId': 'wx489f950decfeb93e',
        'X-WX-Version': 'v3.2.6',
        'X-WX-Platform': 'ios'
    };
}

// 发起请求
function makeRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const headers = buildHeaders();
        
        const options = {
            hostname: urlObj.hostname,
            port: 443,
            path: urlObj.pathname + urlObj.search,
            method: method,
            headers: headers,
            timeout: 15000,
            rejectUnauthorized: false
        };

        console.log(`\n📡 ${method} ${url}`);

        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                console.log('Content-Type:', res.headers['content-type']);
                
                try {
                    const jsonData = JSON.parse(responseData);
                    console.log('JSON响应:', JSON.stringify(jsonData, null, 2));
                    resolve({ 
                        status: res.statusCode, 
                        data: jsonData,
                        success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0)
                    });
                } catch (e) {
                    // 检查是否是HTML错误页面
                    if (responseData.includes('系统发生错误') || responseData.includes('System Error')) {
                        console.log('❌ 系统错误页面 - 可能是路径或参数问题');
                    } else {
                        console.log('原始响应:', responseData.substring(0, 200));
                    }
                    resolve({ 
                        status: res.statusCode, 
                        data: responseData,
                        success: false
                    });
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error.message);
            reject(error);
        });

        req.on('timeout', () => {
            console.log('⏰ 请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        // 发送POST数据
        if (data && method === 'POST') {
            const postData = JSON.stringify(data);
            req.write(postData);
        }

        req.end();
    });
}

// 主测试函数
async function main() {
    // 基于发现的可访问域名，测试不同的路径组合
    const baseUrl = 'https://wap.exijiu.com';
    
    const testPaths = [
        // 直接API路径
        '/index.php/api/garden/Gardenmemberinfo/getMemberInfo',
        '/index.php/api/garden/sorghum/index',
        '/index.php/api/garden/sign/dailySign',
        
        // 带API前缀的路径
        '/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
        '/index.php/API/garden/sorghum/index', 
        '/index.php/API/garden/sign/dailySign',
        
        // 简化路径
        '/api/garden/Gardenmemberinfo/getMemberInfo',
        '/api/garden/sorghum/index',
        '/api/garden/sign/dailySign',
        
        // 其他可能的路径
        '/garden/Gardenmemberinfo/getMemberInfo',
        '/garden/sorghum/index',
        '/garden/sign/dailySign',
        
        // 测试根路径
        '/index.php/api',
        '/index.php/API',
        '/api',
        
        // 测试版本路径
        '/index.php/api/v1/garden/Gardenmemberinfo/getMemberInfo',
        '/index.php/api/v2/garden/Gardenmemberinfo/getMemberInfo'
    ];
    
    console.log('\n=== 测试GET请求 ===');
    
    for (const path of testPaths) {
        try {
            const url = baseUrl + path;
            const result = await makeRequest(url, 'GET');
            
            if (result.success) {
                console.log('✅ 成功！找到有效API');
                console.log(`🎉 有效URL: ${url}`);
                return { url, method: 'GET', result };
            } else if (result.status === 200 && result.data && typeof result.data === 'object') {
                console.log('⚠️ 可能有效但返回错误');
            }
        } catch (error) {
            console.log('❌ 网络错误:', error.message);
        }
        
        // 短暂等待
        await new Promise(resolve => setTimeout(resolve, 800));
    }
    
    console.log('\n=== 测试POST请求 ===');
    
    // 测试一些关键的POST端点
    const postPaths = [
        '/index.php/api/garden/sign/dailySign',
        '/index.php/API/garden/sign/dailySign',
        '/api/garden/sign/dailySign'
    ];
    
    for (const path of postPaths) {
        try {
            const url = baseUrl + path;
            const result = await makeRequest(url, 'POST', {});
            
            if (result.success) {
                console.log('✅ POST成功！找到有效API');
                console.log(`🎉 有效URL: ${url}`);
                return { url, method: 'POST', result };
            }
        } catch (error) {
            console.log('❌ POST错误:', error.message);
        }
        
        await new Promise(resolve => setTimeout(resolve, 800));
    }
    
    console.log('\n❌ 没有找到完全有效的API端点');
    console.log('\n💡 分析结果:');
    console.log('1. ✅ 域名 wap.exijiu.com 是可访问的');
    console.log('2. ✅ Token被服务器接受（没有401错误）');
    console.log('3. ❌ API路径可能不正确，或需要特殊参数');
    console.log('4. 💡 建议使用抓包工具获取真实的API调用');
    
    console.log('\n🔧 下一步建议:');
    console.log('- 在微信小程序中抓包获取真实API地址');
    console.log('- 检查是否需要特殊的请求参数');
    console.log('- 确认API是否需要特定的Content-Type');
}

main().catch(console.error);
