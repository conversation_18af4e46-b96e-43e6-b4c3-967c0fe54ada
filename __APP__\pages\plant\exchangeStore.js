Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),t=a(require("./../../npm/wepy/lib/wepy.js")),n=a(require("./../../mixins/base.js")),r=a(require("./../../components/common/loading.js")),o=(a(require("./../../utils/Tips.js")),a(require("./../../api/garden.js"))),i=(a(require("./../../api/auth.js")),a(require("./../../components/plant/head/modal.js")));function a(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function r(o,i){try{var a=t[o](i),s=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(s).then((function(e){r("next",e)}),(function(e){r("throw",e)}));e(s)}("next")}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=function(a){function l(){var e,a,p;u(this,l);for(var f=arguments.length,g=Array(f),d=0;d<f;d++)g[d]=arguments[d];return a=p=c(this,(e=l.__proto__||Object.getPrototypeOf(l)).call.apply(e,[this].concat(g))),p.data={goodsList:[],userInfo:[],init:!1,wine:"",tipMsg:"",showMsg:!1},p.$repeat={},p.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},TipsModal:{"xmlns:v-on":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},p.$events={TipsModal:{"v-on:closeIt":"closeIt"}},p.components={Loading:r.default,TipsModal:i.default},p.mixins=[n.default],p.methods={exchange:function(){var e=this;return s(regeneratorRuntime.mark((function t(){var n,r,i;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n={wine:e.wine},t.next=3,o.default.exchange(n);case 3:return(r=t.sent).integration&&(e.tipMsg="兑换成功！获得"+r.integration+"积分。",e.showMsg=!0,e.$apply()),t.next=7,o.default.memberInfo();case 7:i=t.sent,e.userInfo=i,e.$apply();case 10:case"end":return t.stop()}}),t,e)})))()},closeIt:function(){this.showMsg=!1,this.$apply()},bangding:function(e){var t=e.currentTarget.dataset,n=e.detail.value,r=t.name;this.data[r]=n,this.wine=this.data[r],this.$apply()},gotoWithLogined:function(e){var t=e.currentTarget.dataset.url;this.$navigate(t)},gotoWithProduct:function(e){var n=this,r=e.currentTarget.dataset.url;console.log(r),t.default.getStorageSync("login_code")?this.$navigate(r):(this.tipMsg="跳转至首页成为习酒会员即可兑换商品,即将自动跳转！",this.showMsg=!0,this.$apply(),setTimeout((function(){n.$navigate("/pages/customer/index")}),3e3))}},p.config={navigationBarBackgroundColor:"#fff",navigationBarTitleText:"兑换商城",navigationBarTextStyle:"black"},c(p,a)}var p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,t.default.page),e(l,[{key:"onLoad",value:(p=s(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.default.memberInfo();case 2:t=e.sent,this.userInfo=t,console.log(t),this.loaded();case 6:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})}]),l}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(l,"pages/plant/exchangeStore"));