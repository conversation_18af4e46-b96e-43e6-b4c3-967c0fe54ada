/**
 * Authorization赋值算法分析器
 * 专门分析 Authorization= 这种赋值算法
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AuthorizationAssignmentAnalyzer {
    constructor() {
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码中发现的关键赋值算法
        this.authorizationAssignmentAlgorithm = {
            // 核心发现：s.default.$instance.globalData.auth.Authorization=r.authorized_token
            step1: "获取JWT响应: n=e.sent (来自getJifenShopJwt)",
            step2: "构建authData: r={expire_time:n.expire_time,authorized_token:n.jwt}",
            step3: "关键赋值: s.default.$instance.globalData.auth.Authorization=r.authorized_token",
            step4: "存储authData: s.default.setStorageSync('authData',r)",
            step5: "存储Authorization: s.default.setStorageSync('Authorization',r.authorized_token)"
        };
        
        // API配置
        this.baseUrl = 'https://wap.exijiu.com/index.php/API';
        this.jifenShopApiUrl = 'https://apimallwm.exijiu.com/api';
        
        console.log('🔧 Authorization赋值算法分析器初始化完成');
        console.log('🎯 专门分析 Authorization= 赋值逻辑');
    }

    /**
     * 分析Authorization赋值算法
     */
    analyzeAuthorizationAssignment() {
        console.log('\n🔍 分析Authorization赋值算法...');
        console.log('📋 基于源码发现的完整赋值流程:');
        
        console.log('\n🔑 关键发现:');
        console.log('从源码中找到的核心赋值语句:');
        console.log('s.default.$instance.globalData.auth.Authorization=r.authorized_token');
        
        console.log('\n📊 完整的赋值算法流程:');
        Object.entries(this.authorizationAssignmentAlgorithm).forEach(([step, description]) => {
            console.log(`  ${step}: ${description}`);
        });
        
        console.log('\n🔍 算法详细分析:');
        console.log('1. JWT获取阶段:');
        console.log('   - 调用 getJifenShopJwt() 方法');
        console.log('   - API: GET /Member/getJwt');
        console.log('   - 响应包含: {jwt: "...", expire_time: 123456789}');
        
        console.log('\n2. 数据转换阶段:');
        console.log('   - 将响应的 jwt 字段重命名为 authorized_token');
        console.log('   - 构建新对象: {expire_time: n.expire_time, authorized_token: n.jwt}');
        
        console.log('\n3. 关键赋值阶段:');
        console.log('   - 核心语句: globalData.auth.Authorization = authorized_token');
        console.log('   - 这就是Authorization的来源！');
        
        console.log('\n4. 持久化阶段:');
        console.log('   - 存储authData到本地');
        console.log('   - 存储Authorization到本地');
        
        console.log('\n💡 关键洞察:');
        console.log('Authorization = JWT响应中的jwt字段');
        console.log('authorized_token = jwt (只是重命名)');
        console.log('最终: Authorization = authorized_token = jwt');
    }

    /**
     * 模拟Authorization赋值算法
     */
    async simulateAuthorizationAssignment() {
        console.log('\n🔄 模拟Authorization赋值算法...');
        
        try {
            // 步骤1: 获取JWT响应 (模拟 getJifenShopJwt)
            console.log('📋 步骤1: 获取JWT响应...');
            const jwtResponse = await this.getJifenShopJwt();
            
            if (!jwtResponse) {
                throw new Error('JWT响应获取失败');
            }
            
            console.log('📊 JWT响应:', JSON.stringify(jwtResponse, null, 2));
            
            // 步骤2: 构建authData (模拟 r={expire_time:n.expire_time,authorized_token:n.jwt})
            console.log('\n📋 步骤2: 构建authData...');
            const authData = {
                expire_time: jwtResponse.expire_time,
                authorized_token: jwtResponse.jwt  // 关键转换：jwt -> authorized_token
            };
            
            console.log('📊 构建的authData:', JSON.stringify(authData, null, 2));
            
            // 步骤3: 关键赋值 (模拟 globalData.auth.Authorization=r.authorized_token)
            console.log('\n📋 步骤3: 执行关键赋值...');
            const Authorization = authData.authorized_token;
            
            console.log('🔑 关键赋值执行:');
            console.log(`  globalData.auth.Authorization = "${Authorization}"`);
            
            // 步骤4: 验证赋值结果
            console.log('\n📋 步骤4: 验证赋值结果...');
            const isValid = await this.validateAuthorization(Authorization);
            
            if (isValid) {
                console.log('✅ Authorization赋值算法模拟成功!');
                console.log('🎉 生成的Authorization有效!');
                
                return {
                    success: true,
                    Authorization: Authorization,
                    authData: authData,
                    jwtResponse: jwtResponse
                };
            } else {
                console.log('❌ 生成的Authorization无效');
                return { success: false };
            }
            
        } catch (error) {
            console.log('❌ Authorization赋值算法模拟失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 深度分析JWT到Authorization的转换
     */
    async analyzeJwtToAuthorizationConversion() {
        console.log('\n🔍 深度分析JWT到Authorization的转换...');
        
        try {
            // 获取原始JWT响应
            const jwtResponse = await this.getJifenShopJwt();
            
            if (!jwtResponse) {
                console.log('❌ 无法获取JWT响应');
                return;
            }
            
            console.log('📊 原始JWT响应分析:');
            console.log('  响应结构:', Object.keys(jwtResponse));
            console.log('  jwt字段存在:', 'jwt' in jwtResponse);
            console.log('  expire_time字段存在:', 'expire_time' in jwtResponse);
            
            if (jwtResponse.jwt) {
                console.log('\n🔍 JWT字段分析:');
                console.log('  JWT值:', jwtResponse.jwt);
                console.log('  JWT长度:', jwtResponse.jwt.length);
                console.log('  JWT格式:', this.isJWTFormat(jwtResponse.jwt) ? 'JWT格式' : '非JWT格式');
                
                // 分析JWT结构
                if (this.isJWTFormat(jwtResponse.jwt)) {
                    const jwtParts = jwtResponse.jwt.split('.');
                    console.log('  JWT部分数量:', jwtParts.length);
                    
                    try {
                        const header = JSON.parse(Buffer.from(jwtParts[0], 'base64').toString());
                        const payload = JSON.parse(Buffer.from(jwtParts[1], 'base64').toString());
                        
                        console.log('  JWT Header:', JSON.stringify(header, null, 2));
                        console.log('  JWT Payload:', JSON.stringify(payload, null, 2));
                    } catch (e) {
                        console.log('  JWT解析失败:', e.message);
                    }
                }
            }
            
            console.log('\n🔄 转换过程分析:');
            console.log('1. 原始字段名: jwt');
            console.log('2. 转换后字段名: authorized_token');
            console.log('3. 最终赋值给: Authorization');
            console.log('4. 转换公式: Authorization = jwt (直接赋值，无变换)');
            
            console.log('\n💡 关键结论:');
            console.log('Authorization的值就是API响应中的jwt字段');
            console.log('没有额外的加密、签名或变换过程');
            console.log('只是简单的字段重命名和赋值');
            
        } catch (error) {
            console.log('❌ JWT到Authorization转换分析失败:', error.message);
        }
    }

    /**
     * 检查是否为JWT格式
     */
    isJWTFormat(token) {
        if (typeof token !== 'string') return false;
        const parts = token.split('.');
        return parts.length === 3 && parts.every(part => part.length > 0);
    }

    /**
     * 获取JWT (getJifenShopJwt方法)
     */
    async getJifenShopJwt() {
        console.log('🔍 调用getJifenShopJwt...');
        
        try {
            const result = await this.makeRequest(this.baseUrl, '/Member/getJwt', 'GET');
            
            if (result.success) {
                console.log('✅ getJifenShopJwt成功');
                return result.data;
            } else {
                console.log('❌ getJifenShopJwt失败:', result.data?.msg || '未知错误');
                return null;
            }
            
        } catch (error) {
            console.log('❌ getJifenShopJwt异常:', error.message);
            return null;
        }
    }

    /**
     * 验证Authorization
     */
    async validateAuthorization(authorization) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': authorization,
                'login_code': this.loginCode,
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6'
            };
            
            const result = await this.makeRequest(
                'https://wap.exijiu.com/index.php/API',
                '/garden/Gardenmemberinfo/getMemberInfo',
                'GET',
                null,
                headers
            );
            
            return result.success;
            
        } catch (error) {
            return false;
        }
    }

    /**
     * HTTP请求
     */
    async makeRequest(baseUrl, path, method = 'GET', data = null, customHeaders = null) {
        const headers = customHeaders || {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            'login_code': this.loginCode
        };
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 运行完整的Authorization赋值分析
     */
    async runCompleteAssignmentAnalysis() {
        console.log('🚀 开始完整的Authorization赋值分析...');
        console.log('🎯 重点观察 Authorization= 这种算法');
        
        try {
            // 1. 分析赋值算法
            console.log('\n' + '='.repeat(60));
            console.log('📊 第一部分: Authorization赋值算法分析');
            console.log('='.repeat(60));
            this.analyzeAuthorizationAssignment();
            
            // 2. 模拟赋值算法
            console.log('\n' + '='.repeat(60));
            console.log('🔄 第二部分: 模拟Authorization赋值算法');
            console.log('='.repeat(60));
            const simulationResult = await this.simulateAuthorizationAssignment();
            
            // 3. 深度分析转换过程
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第三部分: JWT到Authorization转换分析');
            console.log('='.repeat(60));
            await this.analyzeJwtToAuthorizationConversion();
            
            // 4. 输出最终结论
            console.log('\n' + '='.repeat(60));
            console.log('📊 最终分析结论');
            console.log('='.repeat(60));
            
            if (simulationResult.success) {
                console.log('\n🎉 Authorization赋值算法完全破解成功!');
                console.log('\n🔑 核心算法:');
                console.log('1. 调用 GET /Member/getJwt');
                console.log('2. 从响应中提取 jwt 字段');
                console.log('3. 重命名为 authorized_token');
                console.log('4. 赋值给 globalData.auth.Authorization');
                console.log('5. 存储到本地缓存');
                
                console.log('\n💡 简化公式:');
                console.log('Authorization = API响应.jwt');
                
                console.log('\n🔑 生成的Authorization:');
                console.log(simulationResult.Authorization);
                
                return simulationResult;
                
            } else {
                console.log('\n😔 Authorization赋值算法分析失败');
                console.log('💡 但我们已经从源码中完全理解了算法逻辑');
                console.log('🔑 核心发现: Authorization = getJifenShopJwt().jwt');
            }
            
        } catch (error) {
            console.log('\n❌ 分析过程失败:', error.message);
        }
    }
}

// 导出类
module.exports = AuthorizationAssignmentAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new AuthorizationAssignmentAnalyzer();
    
    console.log('🔍 Authorization赋值算法分析器');
    console.log('🎯 专门分析 Authorization= 这种赋值算法');
    console.log('🔑 基于源码发现的核心赋值语句');
    console.log('');
    
    // 运行完整分析
    analyzer.runCompleteAssignmentAnalysis().then(result => {
        if (result && result.success) {
            console.log('\n🎊 Authorization赋值算法完全破解!');
            console.log('🔑 你现在完全掌握了Authorization的生成和赋值机制!');
        } else {
            console.log('\n🤔 虽然API调用可能失败，但算法逻辑已完全理解');
            console.log('💡 核心发现: Authorization就是JWT API响应中的jwt字段');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
