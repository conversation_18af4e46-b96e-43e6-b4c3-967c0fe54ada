/**
 * 微信环境模拟器
 * 模拟真实的微信小程序运行环境，解决"请在手机微信内操作"的问题
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class WeChatEnvironmentSimulator {
    constructor() {
        // 认证信息
        this.authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 微信环境配置
        this.wechatEnv = {
            appId: 'wx489f950decfeb93e',
            version: 'v3.2.6',
            platform: 'ios',
            system: 'iOS 16.6',
            brand: 'iPhone',
            model: 'iPhone 14 Pro',
            pixelRatio: 3,
            screenWidth: 393,
            screenHeight: 852,
            windowWidth: 393,
            windowHeight: 852,
            statusBarHeight: 47,
            language: 'zh_CN',
            wechatVersion: '8.0.47',
            SDKVersion: '3.2.6',
            scene: 1001, // 发现栏小程序主入口
            path: 'pages/index/index', // 从首页开始
            query: {},
            shareTicket: '',
            referrerInfo: {}
        };
        
        // 设备指纹
        this.deviceFingerprint = {
            deviceId: this.generateDeviceId(),
            openId: this.generateOpenId(),
            unionId: 'oA4oD1fddsj8tqw_UU2Z52eWTSpg',
            sessionId: this.generateSessionId(),
            networkType: 'wifi',
            batteryLevel: Math.floor(Math.random() * 30) + 70, // 70-100%
            isCharging: false,
            deviceOrientation: 'portrait'
        };
        
        // API域名
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api'
        ];
        
        // 页面导航历史
        this.navigationHistory = [];
        this.currentPage = null;
        
        console.log('🔧 微信环境模拟器初始化完成');
        console.log('📱 设备型号:', this.wechatEnv.model);
        console.log('🆔 设备ID:', this.deviceFingerprint.deviceId);
        console.log('📍 当前页面:', this.wechatEnv.path);
    }

    /**
     * 生成设备ID
     */
    generateDeviceId() {
        const timestamp = Date.now().toString(36);
        const random = crypto.randomBytes(8).toString('hex');
        return `${timestamp}${random}`.toUpperCase();
    }

    /**
     * 生成OpenID
     */
    generateOpenId() {
        const prefix = 'o' + this.wechatEnv.appId.substring(2, 8);
        const suffix = crypto.randomBytes(12).toString('hex');
        return prefix + suffix;
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        return crypto.randomBytes(16).toString('hex');
    }

    /**
     * 构建完整的微信环境请求头
     */
    buildWeChatHeaders() {
        const timestamp = Date.now();
        
        return {
            // 基础HTTP头
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            
            // 微信浏览器标识
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/${this.wechatEnv.wechatVersion}(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.wechatEnv.version}`,
            
            // 微信小程序环境头
            'Referer': `https://servicewechat.com/${this.wechatEnv.appId}/${this.wechatEnv.version}/page-frame.html`,
            'Origin': `https://servicewechat.com`,
            
            // 请求特征头
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            
            // 微信小程序特有头部
            'Authorization': this.authToken,
            'login_code': this.loginCode,
            
            // 微信环境信息
            'X-WX-AppId': this.wechatEnv.appId,
            'X-WX-Version': this.wechatEnv.version,
            'X-WX-Platform': this.wechatEnv.platform,
            'X-WX-System': this.wechatEnv.system,
            'X-WX-Brand': this.wechatEnv.brand,
            'X-WX-Model': this.wechatEnv.model,
            'X-WX-Language': this.wechatEnv.language,
            'X-WX-Scene': this.wechatEnv.scene.toString(),
            'X-WX-Path': this.currentPage || this.wechatEnv.path,
            'X-WX-Query': JSON.stringify(this.wechatEnv.query),
            
            // 设备指纹信息
            'X-WX-DeviceId': this.deviceFingerprint.deviceId,
            'X-WX-OpenId': this.deviceFingerprint.openId,
            'X-WX-UnionId': this.deviceFingerprint.unionId,
            'X-WX-SessionId': this.deviceFingerprint.sessionId,
            'X-WX-NetworkType': this.deviceFingerprint.networkType,
            'X-WX-DeviceOrientation': this.deviceFingerprint.deviceOrientation,
            
            // 时间戳和随机数
            'X-WX-Timestamp': timestamp.toString(),
            'X-WX-Nonce': crypto.randomBytes(8).toString('hex'),
            
            // 屏幕信息
            'X-WX-ScreenWidth': this.wechatEnv.screenWidth.toString(),
            'X-WX-ScreenHeight': this.wechatEnv.screenHeight.toString(),
            'X-WX-PixelRatio': this.wechatEnv.pixelRatio.toString(),
            
            // 微信版本信息
            'X-WX-WeChatVersion': this.wechatEnv.wechatVersion,
            'X-WX-SDKVersion': this.wechatEnv.SDKVersion
        };
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null) {
        const headers = this.buildWeChatHeaders();
        
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.tryRequest(baseUrl, path, method, data, headers);
                if (result.success || result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 模拟页面导航
     */
    navigateTo(pagePath, query = {}) {
        // 记录导航历史
        if (this.currentPage) {
            this.navigationHistory.push({
                path: this.currentPage,
                timestamp: Date.now()
            });
        }
        
        // 更新当前页面
        this.currentPage = pagePath;
        this.wechatEnv.path = pagePath;
        this.wechatEnv.query = query;
        
        console.log(`📱 导航到页面: ${pagePath}`);
        if (Object.keys(query).length > 0) {
            console.log(`📋 页面参数:`, query);
        }
    }

    /**
     * 模拟返回首页
     */
    async navigateToHome() {
        console.log('\n🏠 模拟返回首页...');
        
        // 导航到首页
        this.navigateTo('pages/index/index');
        
        // 模拟首页加载
        try {
            console.log('📄 加载首页数据...');
            
            // 获取首页基础数据
            const promises = [
                this.makeRequest('/banners'),
                this.makeRequest('/garden/notice/index'),
                this.makeRequest('/garden/tasks/index')
            ];
            
            const results = await Promise.allSettled(promises);
            
            let successCount = 0;
            const apiNames = ['横幅信息', '通知列表', '任务列表'];
            
            results.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value.success) {
                    console.log(`✅ ${apiNames[index]}加载成功`);
                    successCount++;
                } else {
                    console.log(`⚠️ ${apiNames[index]}加载失败`);
                }
            });
            
            console.log(`✅ 首页加载完成 (${successCount}/3 成功)`);
            return true;
            
        } catch (error) {
            console.log('❌ 首页加载失败:', error.message);
            return false;
        }
    }

    /**
     * 模拟进入活动页面
     */
    async navigateToPlantPage() {
        console.log('\n🌱 模拟进入种植活动页面...');
        
        // 导航到种植页面
        this.navigateTo('pages/plant/index', { from: 'home' });
        
        // 模拟页面加载
        try {
            console.log('📄 加载种植页面数据...');
            
            // 检查用户登录状态
            const userInfo = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo');
            if (!userInfo.success) {
                console.log('❌ 用户未登录或Token无效');
                return false;
            }
            
            console.log('✅ 用户登录状态验证成功');
            console.log('👤 用户:', userInfo.data.nick_name || '未知用户');
            
            // 获取土地信息
            const soilData = await this.makeRequest('/garden/sorghum/index');
            if (soilData.success) {
                console.log('✅ 土地信息加载成功');
                if (soilData.data && soilData.data.data) {
                    console.log(`🌱 找到 ${soilData.data.data.length} 块土地`);
                }
            } else {
                console.log('⚠️ 土地信息加载失败:', soilData.data?.msg || '未知错误');
            }
            
            console.log('✅ 种植页面加载完成');
            return true;
            
        } catch (error) {
            console.log('❌ 种植页面加载失败:', error.message);
            return false;
        }
    }

    /**
     * 模拟完整的页面重新进入流程
     */
    async simulatePageReentry() {
        console.log('🔄 开始模拟完整的页面重新进入流程...');
        console.log('📱 模拟场景: 解决"请在手机微信内操作"问题');
        
        try {
            // 步骤1: 返回首页
            console.log('\n📍 步骤1: 返回首页');
            const homeSuccess = await this.navigateToHome();
            if (!homeSuccess) {
                console.log('❌ 返回首页失败');
                return false;
            }
            
            // 步骤2: 等待一下，模拟用户操作
            console.log('\n⏱️ 步骤2: 等待用户操作间隔...');
            await this.sleep(2000);
            
            // 步骤3: 重新进入活动页面
            console.log('\n📍 步骤3: 重新进入种植活动页面');
            const plantSuccess = await this.navigateToPlantPage();
            if (!plantSuccess) {
                console.log('❌ 进入种植页面失败');
                return false;
            }
            
            console.log('\n🎉 页面重新进入流程完成！');
            console.log('📊 当前状态:');
            console.log(`  📱 当前页面: ${this.currentPage}`);
            console.log(`  🆔 设备ID: ${this.deviceFingerprint.deviceId}`);
            console.log(`  📍 导航历史: ${this.navigationHistory.length} 条记录`);
            
            return true;
            
        } catch (error) {
            console.log('\n❌ 页面重新进入流程失败:', error.message);
            return false;
        }
    }

    /**
     * 执行种植操作
     */
    async performPlantingOperations() {
        if (this.currentPage !== 'pages/plant/index') {
            console.log('⚠️ 当前不在种植页面，无法执行种植操作');
            return false;
        }
        
        console.log('\n🌱 开始执行种植操作...');
        
        try {
            // 获取土地信息
            const soilData = await this.makeRequest('/garden/sorghum/index');
            if (!soilData.success) {
                console.log('❌ 无法获取土地信息');
                return false;
            }
            
            const soilList = soilData.data.data || [];
            console.log(`🌱 找到 ${soilList.length} 块土地`);
            
            // 处理每块土地
            for (const soil of soilList) {
                await this.processSoil(soil);
                await this.sleep(1000);
            }
            
            console.log('✅ 种植操作执行完成');
            return true;
            
        } catch (error) {
            console.log('❌ 种植操作执行失败:', error.message);
            return false;
        }
    }

    /**
     * 处理单块土地
     */
    async processSoil(soil) {
        const { id, status, type } = soil;
        
        console.log(`🌱 处理土地${id} (状态: ${status}, 类型: ${type || '未知'})`);
        
        try {
            switch (status) {
                case 0: // 空地，可以种植
                    const plantResult = await this.makeRequest('/garden/sorghum/seed', 'POST', { id: id, type: 1 });
                    if (plantResult.success) {
                        console.log(`🌱 土地${id} 种植成功`);
                    } else {
                        console.log(`🌱 土地${id} 种植失败: ${plantResult.data?.msg || '未知错误'}`);
                    }
                    break;
                    
                case 2: // 成熟，可以收获
                    const harvestResult = await this.makeRequest('/garden/sorghum/harvest', 'POST', { id: id });
                    if (harvestResult.success) {
                        console.log(`🎉 土地${id} 收获成功`);
                        // 收获后重新种植
                        await this.sleep(1000);
                        const replantResult = await this.makeRequest('/garden/sorghum/seed', 'POST', { id: id, type: 1 });
                        if (replantResult.success) {
                            console.log(`🌱 土地${id} 重新种植成功`);
                        }
                    } else {
                        console.log(`🎉 土地${id} 收获失败: ${harvestResult.data?.msg || '未知错误'}`);
                    }
                    break;
                    
                case 10: // 需要浇水
                    const waterResult = await this.makeRequest('/garden/sorghum/watering', 'POST', { id: id });
                    if (waterResult.success) {
                        console.log(`💧 土地${id} 浇水成功`);
                    } else {
                        console.log(`💧 土地${id} 浇水失败: ${waterResult.data?.msg || '未知错误'}`);
                    }
                    break;
                    
                case 11: // 需要施肥
                    const fertilizeResult = await this.makeRequest('/garden/sorghum/manuring', 'POST', { id: id });
                    if (fertilizeResult.success) {
                        console.log(`🌿 土地${id} 施肥成功`);
                    } else {
                        console.log(`🌿 土地${id} 施肥失败: ${fertilizeResult.data?.msg || '未知错误'}`);
                    }
                    break;
                    
                default:
                    console.log(`🌱 土地${id} 状态${status} 无需操作`);
            }
        } catch (error) {
            console.log(`❌ 土地${id} 操作失败: ${error.message}`);
        }
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 主要方法：完整的微信环境模拟
     */
    async runFullSimulation() {
        console.log('🚀 启动完整的微信环境模拟...');
        console.log('🎯 目标: 解决"请在手机微信内操作"问题');
        
        try {
            // 1. 模拟页面重新进入流程
            const reentrySuccess = await this.simulatePageReentry();
            if (!reentrySuccess) {
                console.log('❌ 页面重新进入失败');
                return false;
            }
            
            // 2. 执行种植操作
            const plantingSuccess = await this.performPlantingOperations();
            if (!plantingSuccess) {
                console.log('❌ 种植操作失败');
                return false;
            }
            
            console.log('\n🎉 完整的微信环境模拟成功！');
            console.log('✅ 已解决"请在手机微信内操作"问题');
            console.log('✅ 种植操作正常执行');
            
            return true;
            
        } catch (error) {
            console.log('\n❌ 微信环境模拟失败:', error.message);
            return false;
        }
    }
}

// 导出类
module.exports = WeChatEnvironmentSimulator;

// 如果直接运行此文件
if (require.main === module) {
    const simulator = new WeChatEnvironmentSimulator();
    
    console.log('📱 微信环境模拟器');
    console.log('🎯 解决"请在手机微信内操作"问题');
    console.log('🔧 模拟完整的微信小程序环境');
    console.log('');
    
    // 运行完整模拟
    simulator.runFullSimulation().then(success => {
        if (success) {
            console.log('\n🎊 微信环境模拟成功！');
            console.log('🌱 你的种植脚本现在应该可以正常工作了');
        } else {
            console.log('\n😔 微信环境模拟失败');
            console.log('💡 可能需要进一步优化环境参数');
        }
    }).catch(error => {
        console.error('💥 程序异常:', error);
    });
}
