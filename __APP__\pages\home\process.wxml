<view class="container" wx:if="{{init}}">
    <view class="detail-box">
        <view class="detail-title row-center check-step">
            <text class="primary lg" style="color:#ffffff;font-weight:blok;font-size:16px;">第一步</text>
        </view>
        <view class="detail-list column">
            <b class="b-check-step">用手机二维码软件扫描箱码，查询该箱产品真伪信息以及箱内的每盒酒的防伪信息</b>
            <black wx:for="{{detailimg}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
    </view>
    <view class="detail-box">
        <view class="detail-title row-center check-step">
            <text class="primary lg">第二步</text>
        </view>
        <view class="detail-list column">
            <b class="b-check-step">用手机二维码软件扫描盒码，查询该瓶产品真伪信息以及盒内瓶身防伪码</b>
            <black wx:for="{{stepsecend}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
    </view>
    <view class="detail-box">
        <view class="detail-title row-center check-step">
            <text class="primary lg">第三步</text>
        </view>
        <view class="detail-list column">
            <b class="b-check-step">如果需要进一步验证，点击进一步验证，打开酒盒输入瓶身上的喷码第二排后4位，进行对比验证（比如生产日期、生产单号是否和瓶身上的喷码一致）。</b>
            <black wx:for="{{stepthird}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
    </view>
    <view class="detail-box">
        <view class="detail-title row-center check-style">
            <text class="primary lg">查询方式</text>
        </view>
        <view class="detail-list column">
            <b class="b-check-style">方式一：电话查询</b>
            <black wx:for="{{checkstyleone}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
        <view class="detail-list column">
            <b class="b-check-style">方式二：短信查询</b>
            <black wx:for="{{checkstyletwo}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
        <view class="detail-list column">
            <b class="b-check-style">方式三：网页查询</b>
            <black wx:for="{{checkstylethree}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
        <view class="detail-list column">
            <b style="text-align:left;font-size:14px;color:#ffffff;font-weight:blod;background-color:#06b4f9;height:60rpx;line-height:60rpx">方式四：微信扫描查询,进一步验证产品的真伪</b>
            <black wx:for="{{checkstylefour}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
    </view>
    <view class="detail-box">
        <view class="detail-title row-center check-style">
            <text class="primary lg">举报入口</text>
        </view>
        <view class="detail-list column">
            <black wx:for="{{reportenter}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" data-imageurl="{{image}}" lazyLoad="lazy-load" mode="widthFix" src="{{image}}"></image>
            </black>
        </view>
    </view>
</view>
<CouponPickPanel></CouponPickPanel>
