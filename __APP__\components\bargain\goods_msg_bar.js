Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=r(require("./../../npm/wepy/lib/wepy.js")),t=r(require("./../../mixins/router.js")),o=r(require("./../../mixins/countdown.js"));function r(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(r){function u(){var e,r,a;n(this,u);for(var s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];return r=a=i(this,(e=u.__proto__||Object.getPrototypeOf(u)).call.apply(e,[this].concat(c))),a.props={rule:{},saveText:{default:"砍价立省￥"},saveNum:{default:0}},a.data={},a.methods={},a.watch={rule:function(e){e.floorPrice&&e.goods.maxPrice&&(this.saveNum=(1*e.goods.maxPrice-1*e.floorPrice).toFixed(2))}},a.mixins=[t.default,o.default],i(a,r)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,e.default.component),u}();exports.default=u;