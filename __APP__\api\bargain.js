Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),r=s(require("./base.js")),t=s(require("./group.js")),n=s(require("./../npm/wepy/lib/wepy.js")),i=s(require("./order.js")),o=s(require("./../utils/Page.js"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function a(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var l=function(s){function l(){return u(this,l),a(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(l,r.default),e(l,null,[{key:"rule",value:function(e){var r=this.baseUrl+"/goods_bargain/rules/"+e;return this.get(r).then((function(e){return t.default._processGoodsDetail(e)}))}},{key:"GoodsBargain",value:function(e,r,t){var n=this.baseUrl+"/goods_bargain",i={ruleId:e,id:r,sku:t};return this.post(n,i)}},{key:"bargain",value:function(e){var r=this,t=this.baseUrl+"/goods_bargain/"+e;return this.get(t).then((function(e){return r._processBargainGoods(e)}))}},{key:"order",value:function(e,r){var t=this.baseUrl+"/goods_bargain/order";i.default._processOrderAddress(e,r);var n={ruleId:e.ruleId,order:e,id:e.id};return this.post(t,n)}},{key:"list",value:function(e){var r=this,t=this.baseUrl+"/goods_bargain/rules?status="+e;return new o.default(t,(function(e){r._processBargainListItem(e)}))}},{key:"_processBargainGoods",value:function(e){return t.default._processGoodsPreview(e.rule),this._processDetail(e),this._processBargainUser(e),this._processPrice(e),e}},{key:"_processBargainListItem",value:function(e){t.default._processGoodsPreview(e.rule),e.rule.skuDetail=e.rule.skuDetails.find((function(r){return r.sku===e.sku})),this._processPrice(e)}},{key:"_processDetail",value:function(e){e.rule.skuDetail=e.rule.skuDetails.find((function(r){return r.sku===e.sku})),null!=e.createTime&&(e.createTime=e.createTime.replace(/-/g,"/"))}},{key:"_processBargainUser",value:function(e){var r=n.default.getStorageSync("user").id,t=e.details;e.header=t[0],e.isHead=r===e.header.customer.id;var i=t.find((function(e){return e.customer.id===r}));e.isHelp=null!=i,console.info("[bargain] userId="+r+", self="),e.isHelp&&(e.reducePrice=i.reducePrice)}},{key:"_processPrice",value:function(e){e.allPrice=e.details.reduce((function(e,r){return e+r.reducePrice}),0).toFixed(2);var r=e.rule.skuDetail.price;e.balance=(1*r-1*e.allPrice).toFixed(2),e.bargainRate=e.balance/r*100,e.isFloor=e.balance==e.rule.floorPrice}}]),l}();exports.default=l;