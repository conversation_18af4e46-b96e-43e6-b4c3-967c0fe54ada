<view class="container" wx:if="{{init}}">
    <view class="body">
        <view class="product">
            <image bindtap="previewImage" data-imageurl="{{detail.product_url}}" mode="aspectFit" src="{{detail.product_url}}" style="height:{{productImageHeight}}"></image>
        </view>
        <view class="title">
            <view class="btn">
                <text>产品详情</text>
            </view>
        </view>
        <view class="goods-info-box column">
            <view class="row mt10" wx:if="{{detail.net_content}}">
                <text class="goodsinfo">净含量</text>
                <text class="goodsmsg">{{detail.net_content}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.degree}}">
                <text class="goodsinfo">酒精度数</text>
                <text class="goodsmsg">{{detail.degree}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.odor_type}}">
                <text class="goodsinfo">香型</text>
                <text class="goodsmsg">{{detail.odor_type}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.addiitive}}">
                <text class="goodsinfo">食品添加剂</text>
                <text class="goodsmsg">{{detail.addiitive}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.carton}}">
                <text class="goodsinfo">箱规</text>
                <text class="goodsmsg">{{detail.carton}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.meterial}}">
                <text class="goodsinfo">原料</text>
                <text class="goodsmsg">{{detail.meterial}}</text>
            </view>
            <view class="row mt10" wx:if="{{detail.store_condition}}">
                <text class="goodsinfo">贮存条件</text>
                <text class="goodsmsg">{{detail.store_condition}}</text>
            </view>
            <view wx:if="{{detail.strain!='特许经营'}}">
                <view class="row mt10" wx:if="{{detail.factory_phone}}">
                    <text class="goodsinfo">服务热线</text>
                    <text class="goodsmsg">{{detail.factory_phone}}</text>
                </view>
                <view class="row mt10" wx:if="{{detail.factory_addr}}">
                    <text class="goodsinfo">地址（产地）</text>
                    <text class="goodsmsg">{{detail.factory_addr}}</text>
                </view>
            </view>
            <view wx:else>
                <view class="row mt10">
                    <text class="goodsinfo">客户名称</text>
                    <text class="goodsmsg" wx:if="{{detail.custname!=null}}">{{detail.custname}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">公司网页</text>
                    <text class="goodsmsg" wx:if="{{detail.custhttp!=null}}">{{detail.custhttp}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">联系人</text>
                    <text class="goodsmsg" wx:if="{{detail.custlinker!=null}}">{{detail.custlinker}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">客户电话</text>
                    <text class="goodsmsg" wx:if="{{detail.custtelnum!=null}}">{{detail.custtelnum}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">规格</text>
                    <text class="goodsmsg" wx:if="{{detail.custspec!=null}}">{{detail.custspec}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">生产商名</text>
                    <text class="goodsmsg" wx:if="{{detail.custproducer!=null}}">{{detail.custproducer}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">产品产地</text>
                    <text class="goodsmsg" wx:if="{{detail.custorigine!=null}}">{{detail.custorigine}}</text>
                </view>
                <view class="row mt10">
                    <text class="goodsinfo">附加信息</text>
                    <text class="goodsmsg" wx:if="{{detail.custnote!=null}}">{{detail.custnote}}</text>
                </view>
            </view>
        </view>
    </view>
</view>
