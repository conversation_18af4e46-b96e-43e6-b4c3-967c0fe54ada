Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=u(require("./base.js")),r=u(require("./../npm/wepy/lib/wepy.js")),n=u(require("./../store/utils.js")),a=u(require("./../utils/WxUtils.js"));function u(e){return e&&e.__esModule?e:{default:e}}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(a,u){try{var s=t[a](u),i=s.value}catch(e){return void r(e)}if(!s.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=function(u){function c(){return i(this,c),o(this,(c.__proto__||Object.getPrototypeOf(c)).apply(this,arguments))}var l,f,h,p,g,d,v,b,k,y;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(c,t.default),e(c,null,[{key:"login",value:(y=s(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null==(t=this.getConfig("login_code"))||""==t){e.next=16;break}return e.prev=2,e.next=5,this.checkLoginCode(t);case 5:return e.abrupt("return",e.sent);case 8:return e.prev=8,e.t0=e.catch(2),console.warn("check login code fial",t),e.next=13,this.doLogin();case 13:return e.abrupt("return",e.sent);case 14:e.next=20;break;case 16:return console.warn("login code not exists",t),e.next=19,this.doLogin();case 19:return e.abrupt("return",e.sent);case 20:case"end":return e.stop()}}),e,this,[[2,8]])}))),function(){return y.apply(this,arguments)})},{key:"user",value:(k=s(regeneratorRuntime.mark((function e(){var t,r,u,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{block:!1,redirect:!1},i=arguments[1];return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!this.hasConfig("user")){e.next=4;break}return n.default.save("user",this.getConfig("user")),e.abrupt("return",!0);case 4:if(console.info("[auth] user check fail"),null!=i){e.next=7;break}throw new Error("未获取用户信息");case 7:return e.next=9,this.register(i);case 9:return t=e.sent,r=t.user,e.next=13,this.setConfig("user",r);case 13:return n.default.save("user",r),e.next=16,this.doLogin();case 16:return e.abrupt("return",!0);case 19:return e.prev=19,e.t0=e.catch(0),console.error("[auth] 授权失败",e.t0),s.block&&(u="/pages/auth/login?redirect="+s.redirect,s.redirect?a.default.backOrRedirect(u):a.default.backOrNavigate(u)),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,this,[[0,19]])}))),function(){return k.apply(this,arguments)})},{key:"checkUserInfo",value:(b=s(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/auth/check_userinfo",n={rawData:t.rawData,signature:t.signature,thirdSession:this.getConfig("third_session"),app_code:this.getShopCode()},e.next=4,this.get(r,n);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e,this)}))),function(e){return b.apply(this,arguments)})},{key:"register",value:(v=s(regeneratorRuntime.mark((function e(t){var n,a,u,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.default.login();case 2:return n=e.sent,a=n.code,u=this.baseUrl+"/auth/register",s={encryptedData:t.encryptedData,iv:t.iv,code:a},e.next=8,this.get(u,s);case 8:return e.abrupt("return",e.sent);case 9:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"doLogin",value:(d=s(regeneratorRuntime.mark((function e(){var t,n,u,s,i,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,r.default.login();case 3:return t=e.sent,n=t.code,e.next=7,this.session(n);case 7:return u=e.sent,s=u.third_session,i=u.login_code,e.next=12,this.setConfig("login_code",i);case 12:return e.next=14,this.setConfig("third_session",s);case 14:return e.next=16,this.login();case 16:return e.abrupt("return",e.sent);case 19:if(e.prev=19,e.t0=e.catch(0),console.log("doLogin catch",e.t0),o=e.t0.serverData.third_session,e.t0.serverCode!=r.default.$instance.globalData.http_code.unregister.value){e.next=29;break}return e.next=27,this.setConfig("third_session",o);case 27:a.default.backOrNavigate("/pages/auth/login?redirect=false");case 29:case"end":return e.stop()}}),e,this,[[0,19]])}))),function(){return d.apply(this,arguments)})},{key:"session",value:(g=s(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/auth/session?code="+t,e.next=3,this.get(r);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)}))),function(e){return g.apply(this,arguments)})},{key:"checkLoginCode",value:(p=s(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=2;break}return e.abrupt("return",!0);case 2:return r=this.baseUrl+"/auth/checkSession?loginCode="+t,e.next=5,this.get(r);case 5:return n=e.sent,e.abrupt("return",n.result);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"getShopCode",value:function(){return r.default.$instance.globalData.appCode}},{key:"getConfig",value:function(e){return r.default.$instance.globalData.auth[e]}},{key:"hasConfig",value:function(e){var t=this.getConfig(e);return null!=t&&""!=t}},{key:"setConfig",value:(h=s(regeneratorRuntime.mark((function e(t,n){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.default.setStorage({key:t,data:n});case 2:r.default.$instance.globalData.auth[t]=n;case 3:case"end":return e.stop()}}),e,this)}))),function(e,t){return h.apply(this,arguments)})},{key:"removeConfig",value:(f=s(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.info("[auth] clear auth config ["+t+"]"),r.default.$instance.globalData.auth[t]=null,e.next=4,r.default.removeStorage({key:t});case 4:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"checkCheat",value:(l=s(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/auth/getCheatVersion",e.next=3,this.get(t);case 3:if(n=e.sent,console.log("getCheatVersion",n),n.check_version!=r.default.$instance.globalData.version){e.next=7;break}return e.abrupt("return",!0);case 7:return e.abrupt("return",!1);case 8:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})}]),c}();exports.default=c;