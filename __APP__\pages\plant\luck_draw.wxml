<view class="_a43b9ca">
    <view class="column-center loading-wrap _a43b9ca" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter _a43b9ca" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <view class="content _a43b9ca" wx:if="{{init}}">
        <view class="_a43b9ca" style="width:40%;height:60rpx;line-height:60rpx;padding-left:20rpx;position:fixed;top:10rpx;color:#fff;font-weight:bold;left:5%">参与抽奖人数：{{lotteryAmount[0][0].lotteryamount}}</view>
        <view class="_a43b9ca" style="width:40%;height:60rpx;line-height:60rpx;padding-left:20rpx;position:fixed;top:10rpx;color:#fff;font-weight:bold;left:52.5%">今日参与人数：{{lotteryAmount[1][0].todaylotteryamount}}</view>
        <view class="displayGroup _a43b9ca">
            <view class="dmGroup top _a43b9ca" style="animation:dmAnimation2 110s linear infinite;">
                <view class="dmItem _a43b9ca" wx:if="{{index<dmData.length/2}}" wx:for="{{dmData}}" wx:key="{{item.id}}">
                    <view class="dm _a43b9ca">
                        <view class="avatarBox _a43b9ca">
                            <image class="avatar _a43b9ca" mode="aspectFit" src="{{item.head_imgurl}}"></image>
                        </view>
                        <text class="contents _a43b9ca">{{item.nick_name}} {{item.formatCreateDate}} 抽中{{item.prize_name}}</text>
                    </view>
                </view>
            </view>
            <view class="dmGroup mid _a43b9ca" style="animation:dmAnimation2 125s linear 1s infinite;">
                <view class="dmItem _a43b9ca" wx:if="{{index>dmData.length/2-1}}" wx:for="{{dmData}}" wx:key="{{item.id}}">
                    <view class="dm _a43b9ca">
                        <view class="avatarBox _a43b9ca">
                            <image class="avatar _a43b9ca" mode="aspectFit" src="{{item.head_imgurl}}"></image>
                        </view>
                        <text class="contents _a43b9ca">{{item.nick_name}} {{item.formatCreateDate}} 抽中{{item.prize_name}}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="machine _a43b9ca">
            <view bindtap="start" class="start _a43b9ca" hoverClass="scale"></view>
            <image class="random showit _a43b9ca" src="http://wap.exijiu.cn/Public/MemberClub/images/yuebing-{{random}}.png" wx:if="{{end}}"></image>
            <view class="lottry_rule _a43b9ca" wx:else>抽1次消耗100积分，您当前共有<text class="_a43b9ca" style="font-weight:bold;color:#43a3a8;">{{userInfo.integration>9999?'9999+':userInfo.integration}}</text>积分</view>
        </view>
        <view class="balls _a43b9ca">
            <image class="ball ball_{{index+1}} {{start?move+index+1:''}} _a43b9ca" src="http://wap.exijiu.cn/Public/MemberClub/images/yuebing-{{item}}.png" wx:for="{{ballList}}" wx:key="index"></image>
        </view>
        <view class="rules _a43b9ca">
            <view bindtap="showModal_task2" class="btnL _a43b9ca" data-wpyshowmodal_task2-a="1" hoverClass="scale"></view>
            <view bindtap="showModal_task" class="btnR _a43b9ca" data-wpyshowmodal_task-a="1" hoverClass="scale"></view>
        </view>
        <view bindtap="miss" class="modal _a43b9ca" wx:if="{{showModal}}">
            <view class="kuang _a43b9ca">
                <image class="prize _a43b9ca" src="{{prize.prize_image}}"></image>
                <view class="pname _a43b9ca">{{prize.prize_level}}</view>
                <view class="pname _a43b9ca">{{prize.prize_name}}</view>
                <view class="pbtn-box _a43b9ca">
                    <view bindtap="gotoOrderDetail" class="pbtn _a43b9ca" data-url="/pages/plant/index" wx:if="{{prize.prize_type==3}}">前往庄园</view>
                    <view bindtap="gotoOrderDetail" class="pbtn _a43b9ca" data-url="/pages/customer/goToUmall" wx:if="{{prize.prize_type==2}}">前往商城兑换</view>
                    <view bindtap="showModal_task" class="pbtn _a43b9ca">确认</view>
                </view>
            </view>
        </view>
        <view animation="{{animationData}}" class="commodity_attr_box_task _a43b9ca" wx:if="{{showModalStatus2}}">
            <view class="myprize _a43b9ca">抽奖规则</view>
            <scroll-view scrollY class="box _a43b9ca">
                <rich-text class="_a43b9ca" nodes="{{detail.intro}}"></rich-text>
                <view class="itemprize _a43b9ca" wx:for="{{detail.prizes}}" wx:key="index">
                    <view class="_a43b9ca" style="margin-top:40rpx;">
                        <view class="_a43b9ca" style="font-weight:bold">{{item.level}}</view>
                        <view class="_a43b9ca" style="font-size:24rpx">奖品:{{item.name}}</view>
                    </view>
                    <view class="prizePic _a43b9ca">
                        <image alt="" class="_a43b9ca" src="{{item.image}}"></image>
                    </view>
                </view>
            </scroll-view>
        </view>
        <view animation="{{animationData}}" class="commodity_attr_box_task _a43b9ca" wx:if="{{showModalStatus}}">
            <view class="myprize _a43b9ca">我的奖品</view>
            <scroll-view scrollY bindscrolltolower="listShow" class="box _a43b9ca">
                <view class="itemprize _a43b9ca" wx:for="{{lotteryRecord}}" wx:key="index">
                    <view class="_a43b9ca" style="margin-top:40rpx;">
                        <view class="_a43b9ca" style="font-weight:bold">{{item.prize_name}}</view>
                        <view class="_a43b9ca" style="font-size:24rpx">中奖时间:{{item.create_time}}</view>
                    </view>
                    <view bindtap="gotoOrderDetail" class="seePics _a43b9ca" data-url="/pages/plant/order" wx:if="{{item.detail_id==0&&item.prize.type==1}}">填写地址</view>
                    <view bindtap="gotoOrderDetail" class="seePics _a43b9ca" data-url="/pages/plant/order" wx:if="{{item.detail_id!=0&&item.prize.type==1}}">查看订单</view>
                </view>
            </scroll-view>
        </view>
        <view bindtap="hideModal" catchtouchmove="throttle" class="commodity_screen _a43b9ca" wx:if="{{showModalStatus||showModalStatus2}}"></view>
    </view>
</view>
