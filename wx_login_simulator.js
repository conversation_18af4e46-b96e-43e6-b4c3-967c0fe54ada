/**
 * 微信登录流程模拟器
 * 尝试模拟完整的微信登录流程来获取authtoken
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class WxLoginSimulator {
    constructor() {
        // 已知信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.appId = 'wx489f950decfeb93e';
        this.unionId = 'oA4oD1fddsj8tqw_UU2Z52eWTSpg';
        this.memberId = 6865357;
        
        // API域名
        this.apiDomains = [
            'https://wap.exijiu.com/index.php/API',
            'https://apiforum.exijiu.com/api',
            'https://apimallwm.exijiu.com/api'
        ];
        
        console.log('🔧 微信登录流程模拟器初始化完成');
    }

    /**
     * 模拟微信登录流程
     */
    async simulateWxLogin() {
        console.log('\n🚀 开始模拟微信登录流程...');
        
        // 步骤1: 尝试直接用login_code换取authtoken
        console.log('\n📱 步骤1: 尝试login_code直接换取authtoken...');
        let authToken = await this.tryDirectExchange();
        if (authToken) return authToken;
        
        // 步骤2: 模拟wx.login()获取新code
        console.log('\n📱 步骤2: 模拟wx.login()流程...');
        authToken = await this.simulateWxLoginCode();
        if (authToken) return authToken;
        
        // 步骤3: 尝试用户信息授权流程
        console.log('\n📱 步骤3: 尝试用户信息授权流程...');
        authToken = await this.simulateUserInfoAuth();
        if (authToken) return authToken;
        
        // 步骤4: 尝试静默登录
        console.log('\n📱 步骤4: 尝试静默登录...');
        authToken = await this.trySilentLogin();
        if (authToken) return authToken;
        
        console.log('\n❌ 所有登录流程模拟都失败了');
        return null;
    }

    /**
     * 尝试直接用login_code换取authtoken
     */
    async tryDirectExchange() {
        const exchangeAPIs = [
            { path: '/garden/wechat/login', method: 'GET', params: { code: this.loginCode } },
            { path: '/garden/wechat/login', method: 'POST', data: { code: this.loginCode } },
            { path: '/wechat/auth', method: 'POST', data: { login_code: this.loginCode } },
            { path: '/auth/token', method: 'POST', data: { login_code: this.loginCode, unionid: this.unionId } },
            { path: '/garden/member/login', method: 'POST', data: { login_code: this.loginCode } }
        ];
        
        for (const api of exchangeAPIs) {
            try {
                console.log(`🧪 测试: ${api.method} ${api.path}`);
                const result = await this.makeRequest(api.path, api.method, api.data || null, api.params || null);
                
                if (result.success && result.data) {
                    const token = this.extractTokenFromResponse(result.data);
                    if (token) {
                        console.log('✅ 直接换取成功!');
                        return token;
                    }
                }
            } catch (error) {
                console.log(`❌ ${api.path} 失败`);
            }
        }
        
        return null;
    }

    /**
     * 模拟wx.login()流程
     */
    async simulateWxLoginCode() {
        // 生成模拟的微信登录code
        const mockCodes = [
            this.generateMockWxCode(),
            this.generateMockWxCode('js_code'),
            this.generateMockWxCode('auth_code'),
            this.loginCode // 也尝试现有的login_code
        ];
        
        for (const code of mockCodes) {
            try {
                console.log(`🧪 测试模拟code: ${code.substring(0, 20)}...`);
                
                const result = await this.makeRequest('/garden/wechat/login', 'GET', null, { code: code });
                
                if (result.success && result.data) {
                    const token = this.extractTokenFromResponse(result.data);
                    if (token) {
                        console.log('✅ 模拟wx.login()成功!');
                        return token;
                    }
                }
            } catch (error) {
                console.log(`❌ 模拟code失败`);
            }
        }
        
        return null;
    }

    /**
     * 模拟用户信息授权流程
     */
    async simulateUserInfoAuth() {
        const authData = {
            unionid: this.unionId,
            openid: this.generateMockOpenId(),
            nickname: '用户' + this.memberId,
            avatar: 'https://wx.qlogo.cn/mmopen/vi_32/default.png',
            login_code: this.loginCode
        };
        
        const authAPIs = [
            { path: '/garden/member/auth', data: authData },
            { path: '/garden/user/register', data: authData },
            { path: '/auth/userinfo', data: authData }
        ];
        
        for (const api of authAPIs) {
            try {
                console.log(`🧪 测试用户授权: ${api.path}`);
                const result = await this.makeRequest(api.path, 'POST', api.data);
                
                if (result.success && result.data) {
                    const token = this.extractTokenFromResponse(result.data);
                    if (token) {
                        console.log('✅ 用户授权成功!');
                        return token;
                    }
                }
            } catch (error) {
                console.log(`❌ ${api.path} 失败`);
            }
        }
        
        return null;
    }

    /**
     * 尝试静默登录
     */
    async trySilentLogin() {
        const silentAPIs = [
            { path: '/garden/member/silent', data: { unionid: this.unionId } },
            { path: '/auth/silent', data: { login_code: this.loginCode } },
            { path: '/garden/auth/refresh', data: { login_code: this.loginCode } }
        ];
        
        for (const api of silentAPIs) {
            try {
                console.log(`🧪 测试静默登录: ${api.path}`);
                const result = await this.makeRequest(api.path, 'POST', api.data);
                
                if (result.success && result.data) {
                    const token = this.extractTokenFromResponse(result.data);
                    if (token) {
                        console.log('✅ 静默登录成功!');
                        return token;
                    }
                }
            } catch (error) {
                console.log(`❌ ${api.path} 失败`);
            }
        }
        
        return null;
    }

    /**
     * 从响应中提取Token
     */
    extractTokenFromResponse(data) {
        const tokenFields = [
            'authorized_token',
            'auth_token',
            'token',
            'access_token',
            'jwt_token',
            'member_token'
        ];
        
        for (const field of tokenFields) {
            if (data[field]) {
                return data[field];
            }
        }
        
        return null;
    }

    /**
     * 生成模拟的微信code
     */
    generateMockWxCode(prefix = 'wx_code') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `${prefix}_${timestamp}_${random}`;
    }

    /**
     * 生成模拟的OpenID
     */
    generateMockOpenId() {
        return 'o' + this.appId.substring(2) + '_' + crypto.randomBytes(16).toString('hex');
    }

    /**
     * 发起API请求
     */
    async makeRequest(path, method = 'GET', data = null, params = null) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
            'login_code': this.loginCode,
            'X-WX-AppId': this.appId
        };
        
        // 构建URL
        let fullPath = path;
        if (params) {
            const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
            fullPath += '?' + queryString;
        }
        
        for (const baseUrl of this.apiDomains) {
            try {
                const result = await this.tryRequest(baseUrl, fullPath, method, data, headers);
                if (result.status === 200) {
                    return result;
                }
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('所有API域名都无法访问');
    }

    /**
     * 尝试单个域名的请求
     */
    async tryRequest(baseUrl, path, method, data, headers) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }
}

// 导出类
module.exports = WxLoginSimulator;

// 如果直接运行此文件
if (require.main === module) {
    const simulator = new WxLoginSimulator();
    
    simulator.simulateWxLogin().then(authToken => {
        if (authToken) {
            console.log('\n🎉 成功获取authtoken!');
            console.log('🔑 AuthToken:', authToken);
        } else {
            console.log('\n❌ 无法通过模拟登录获取authtoken');
            console.log('\n💡 最终建议:');
            console.log('1. 使用现有的authtoken (有效期到2025年6月)');
            console.log('2. 配合会话保活器使用: node simple_session_keeper.js');
            console.log('3. 当Token真正过期时，通过抓包分析真实登录流程');
            console.log('4. 或者重新获取login_code和authtoken');
        }
    }).catch(error => {
        console.error('模拟登录失败:', error);
    });
}
