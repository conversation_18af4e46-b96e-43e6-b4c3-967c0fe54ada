Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),o=u(require("./base.js")),n=u(require("./goods.js")),a=u(require("./shop.js")),s=u(require("./member.js"));function u(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var p=(t=e=function(e){function t(){return i(this,t),c(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,o.default),r(t,null,[{key:"layout",value:function(e){var t=this,r=this.baseUrl+"/layout/pages/"+e;return this.get(r).then((function(e){return t._processPage(e.message)}))}},{key:"init",value:function(){var e=this,t=this.baseUrl+"/shops/full";return this.get(t).then((function(e){return{homePageId:e.homePageId,customPageId:e.customPageId,page:e.homePageConfig,card:e.memberCard,member:e.member,campaign:e.campaignCoupon,categories:n.default._createGoodsCategories(e.goodsInnerCategories),notices:a.default._processNotices(e.notices),reduce:a.default._processReduce(e.reduceRules),shop:a.default._processInfo(e.shop),version:a.default._precoessVersion(e.shopChargeLimit),status:a.default._processStatus(e.shopStatusInfo)}})).then((function(t){var r=t.card,o=t.member,n=t.page;return t.discount=e.discount=s.default.processDiscount(r,o),t.page=e._processPage(n),t}))}},{key:"_processPage",value:function(e){if(null==e||""==e)return null;var t=JSON.parse(e),r=this.processComponents(t.components),o=this.processPlugins(t.plugins);return{components:r,plugins:o.plugins,triggers:o.triggers,param:this.processPageParam(t.param)}}},{key:"processPageParam",value:function(e){return null==e||""==e?{}:JSON.parse(e)}},{key:"processPlugins",value:function(e){var t=[],r=[];return e.forEach((function(e){if(e.param){var o=JSON.parse(e.param);Object.assign(e,o),e.param=null}-1!=e.type.indexOf("_TRIGGER")?r.push(e):t.push(e)})),{triggers:r,plugins:t}}},{key:"processComponents",value:function(e){var t=this;return e.map((function(e){if(e.param){var r=JSON.parse(e.param);Object.assign(e,r),e.param=null}return e.data&&(e.data=JSON.parse(e.data)),"GOODS_BOX"==e.type&&e.data.forEach((function(e){n.default._processGoodsDiscount(e,t.discount),n.default._processGoodsData(e)})),"IMAGE_BOX"==e.type&&null==e.padding&&(e.padding="10rpx;"),t.copyParamToData(e)}))}},{key:"copyParamToData",value:function(e){var t=e.data,r=e.type,o=this.fieldsToCopy[r];return null!=o&&t.forEach((function(t){o.forEach((function(r){t[r]=e[r]}))})),e}}]),t}(),e.fieldsToCopy={SWIPER:["height"],IMAGE_BOX:["heigth","width","isTitle"],GOODS_BOX:["isCart","isPrice","isGoodsName","isSales","skuMode","isTips"]},e.discount=null,t);exports.default=p;