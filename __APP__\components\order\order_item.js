Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(r,t,n){return t&&e(r.prototype,t),n&&e(r,n),r}}(),r=u(require("./../../npm/wepy/lib/wepy.js")),t=u(require("./../../api/order.js")),n=u(require("./../../utils/Tips.js")),o=u(require("./../../utils/Event.js")),a=u(require("./../../utils/WxUtils.js")),s=u(require("./order_goods.js"));function u(e){return e&&e.__esModule?e:{default:e}}function i(e){return function(){var r=e.apply(this,arguments);return new Promise((function(e,t){return function n(o,a){try{var s=r[o](a),u=s.value}catch(e){return void t(e)}if(!s.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function c(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function d(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}var f=function(u){function f(){var e,r,t;c(this,f);for(var o=arguments.length,a=Array(o),u=0;u<o;u++)a[u]=arguments[u];return r=t=d(this,(e=f.__proto__||Object.getPrototypeOf(f)).call.apply(e,[this].concat(a))),t.props={order:{orderGoodsInfos:[],orderPriceHistory:[]},action:{default:!0}},t.data={expand:{},shopName:{}},t.methods={action:function(e,r){(console.info("[action]"+e),this[e])?this[e].bind(this)(r):n.default.error("未知错误")},expand:function(e){e=null==e?0:e,this.expand[e]=!this.expand[e]},detail:function(e){0!=this.action&&this.$root.$navigate("/pages/order/detail?orderId="+e)}},t.$repeat={order:{com:"OrderGoods",props:"goods"}},t.$props={OrderGoods:{"xmlns:v-bind":{value:"",for:"order.orderGoodsInfos",item:"item",index:"index",key:"goodsId"},"v-bind:goods.once":{value:"item",type:"item",for:"order.orderGoodsInfos",item:"item",index:"index",key:"goodsId"}}},t.$events={},t.components={OrderGoods:s.default},d(t,r)}var l,p,m,h;return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}(f,r.default.component),e(f,[{key:"onLoad",value:function(){this.shopName=r.default.$instance.globalData.shopName}},{key:"close",value:(h=i(regeneratorRuntime.mark((function e(r){var a=r.orderId;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.default.confirm("确定关闭该订单？");case 2:return e.prev=2,n.default.loading(),e.next=6,t.default.closeOrder(a);case 6:return e.next=8,n.default.success("关闭成功");case 8:e.next=15;break;case 10:return e.prev=10,e.t0=e.catch(2),console.warn(e.t0),e.next=15,n.default.success("关闭失败");case 15:o.default.emit(o.default.ORDER_LIST_UPDATE);case 16:case"end":return e.stop()}}),e,this,[[2,10]])}))),function(e){return h.apply(this,arguments)})},{key:"pay",value:(m=i(regeneratorRuntime.mark((function e(r){var a,s=r.orderId;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n.default.loading(),e.next=4,t.default.prepayOrder(s);case 4:return a=e.sent,n.default.loaded(),e.next=8,t.default.wxpayOrder(a);case 8:return e.next=10,n.default.success("支付成功");case 10:e.next=17;break;case 12:return e.prev=12,e.t0=e.catch(0),console.warn(e.t0),e.next=17,n.default.success("支付取消");case 17:o.default.emit(o.default.ORDER_LIST_UPDATE);case 18:case"end":return e.stop()}}),e,this,[[0,12]])}))),function(e){return m.apply(this,arguments)})},{key:"receive",value:(p=i(regeneratorRuntime.mark((function e(r){var a=r.orderId;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.default.confirm("确认收货？");case 2:return e.prev=2,n.default.loading(),e.next=6,t.default.confirmOrder(a);case 6:return e.next=8,n.default.success("订单已完成");case 8:e.next=15;break;case 10:return e.prev=10,e.t0=e.catch(2),console.warn(e.t0),e.next=15,n.default.success("操作失败");case 15:o.default.emit(o.default.ORDER_LIST_UPDATE);case 16:case"end":return e.stop()}}),e,this,[[2,10]])}))),function(e){return p.apply(this,arguments)})},{key:"again",value:(l=i(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o.default.emit(o.default.CART_LIST_RESET,r.orderGoodsInfos),this.$root.$preload("cart",r.orderGoodsInfos),a.default.backOrRedirect("/pages/shop/index");case 3:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"comment",value:function(e){var r=e.orderGoodsInfos.map((function(r){return{goodsId:r.goodsId,orderId:e.orderId,sku:r.goodsSku,preview:r.imageUrl,star:5,starArr:[1,1,1,1,1],comment:""}}));this.$root.$preload("data",r),this.$root.$navigate("/pages/goods/comment_edit?orderId="+e.orderId)}}]),f}();exports.default=f;