Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),o=require("./../../npm/wepy/lib/wepy.js"),r=(e=o)&&e.__esModule?e:{default:e};function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function o(){var e,t,r;n(this,o);for(var u=arguments.length,i=Array(u),l=0;l<u;l++)i[l]=arguments[l];return t=r=a(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(i))),r.props={display:{default:"true",twoWay:!0},backgroundColor:{default:"#F8F8F8"},header:{default:"true"},minHeight:{default:0},btn:{default:"false"}},r.data={},r.methods={close:function(){this.display="false"}},r.events={},a(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,r.default.component),t(o,[{key:"onLoad",value:function(){}}]),o}();exports.default=u;