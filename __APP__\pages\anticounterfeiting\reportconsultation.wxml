<view class="container" wx:if="{{init}}">
    <view>
        <swiper autoplay="true" class="goods-swiper" indicatorDots="true" interval="5000">
            <swiper-item wx:for="{{imageUrl}}" wx:for-item="image" wx:key="item">
                <image bindtap="previewImage" class="slide-image" data-imageurl="{{image}}" mode="aspectFit" src="{{image}}"></image>
            </swiper-item>
        </swiper>
        <form bindsubmit="confirm">
            <view class="form-background">
                <view class="form-area">
                    <input class="weui-input" name="qrCode" style="display:none" type="hidden" value="{{feedback.qrCode}}"></input>
                    <view class="row">
                        <text class="form-label">举报电话</text>
                        <text bindtap="feedbackphone" class="form-phone" style="">{{feedbackphone}}</text>
                    </view>
                    <view class="tel-bar"></view>
                    <view class="row">
                        <text class="form-label">反馈内容</text>
                        <textarea autoFocus="true" name="desc" placeholder="请描述假冒伪劣品简述或咨询内容..." style="height:3em" value="{{feedback.desc}}"></textarea>
                    </view>
                    <view class="row">
                        <text class="form-label">姓名</text>
                        <input class="weui-input" maxlength="5" name="name" placeholder="请输入姓名" value="{{feedback.name}}"></input>
                    </view>
                    <view class="row">
                        <text class="form-label">联系电话</text>
                        <input class="weui-input" maxlength="11" name="phone" placeholder="请输入电话" type="number" value="{{feedback.phone}}"></input>
                    </view>
                </view>
            </view>
            <view class="btn-area">
                <button class="btn" formType="submit" plain="true">
                    <text>提交</text>
                </button>
            </view>
        </form>
    </view>
</view>
