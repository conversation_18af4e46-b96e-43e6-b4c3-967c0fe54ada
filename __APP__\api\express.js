Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./base.js"),n=(e=r)&&e.__esModule?e:{default:e};function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(e){function r(){return u(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}var s,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default),t(r,null,[{key:"company",value:(s=regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/express/company",e.next=3,this.get(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})),a=function(){var e=s.apply(this,arguments);return new Promise((function(t,r){return function n(u,o){try{var s=e[u](o),a=s.value}catch(e){return void r(e)}if(!s.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(a)}("next")}))},function(){return a.apply(this,arguments)})},{key:"createCurrentTrace",value:function(e){if(null!=e){var t=e.expressBases;if(null==t||t.length<1)return{text:"尚未查询到物流信息"};var r=t[0];return{text:r.status,timestape:r.time}}}},{key:"queryCurrentTrace",value:function(e){var t=this;return this._queryExpressInfo(e).then((function(e){return t.createCurrentTrace(e)}))}},{key:"createTrace",value:function(e){if(null!=e){var t=this._createExpressInfo(e);return{steps:this._createTraceSteps(e),info:t}}}},{key:"queryTrace",value:function(e){var t=this;return this._queryExpressInfo(e).then((function(e){return t.createTrace(e)}))}},{key:"createExpressOrderPreview",value:function(e){return{imageUrl:e.orderGoodsInfos[0].imageUrl,goodsCount:e.orderGoodsInfos.length,orderId:e.orderId}}},{key:"_queryExpressInfo",value:function(e){var t=this.baseUrl+"/express",r={order_id:e};return this.get(t,r)}},{key:"_createTraceSteps",value:function(e){if(!e.expressBases)return null;var t=e.expressBases.map(this._processTraceStep),r=t[0];return r.done=!0,r.current=!0,t}},{key:"_processTraceStep",value:function(e){return{text:e.status,timestape:e.time,done:!1,current:!1}}},{key:"_createExpressInfo",value:function(e){return null==e.status&&(e.status="待揽收"),{expTextName:e.expressType,mailNo:e.expressNo,status:e.status,tel:e.telPhone}}}]),r}();exports.default=s;