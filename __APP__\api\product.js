Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=n(require("./base.js")),r=n(require("./../npm/wepy/lib/wepy.js"));n(require("./../store/utils.js")),n(require("./../utils/WxUtils.js"));function n(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(u,a){try{var i=t[u](a),o=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(o).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(o)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var o=function(n){function o(){return a(this,o),i(this,(o.__proto__||Object.getPrototypeOf(o)).apply(this,arguments))}var s,c,p,f,h,l,d,v,g,m,b,y,w,R,k,x,U,P,C,_,j,D;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,t.default),e(o,null,[{key:"info",value:(D=u(regeneratorRuntime.mark((function e(t){var n,u,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.default.getStorageSync("userLocation"),console.log(n),u=this.baseUrl+"/product/info",a={qrCode:t,lat:n.latitude||"",lng:n.longitude||""},e.abrupt("return",this.get(u,a).then((function(e){return e})));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return D.apply(this,arguments)})},{key:"infoWithLocation",value:(j=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/info",e.abrupt("return",this.post(r,t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return j.apply(this,arguments)})},{key:"infoByErpNo",value:(_=u(regeneratorRuntime.mark((function e(t,r){var n,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(t),n=this.baseUrl+"/product/infoByErpNo",u={erpNo:t,productDate:r},e.abrupt("return",this.get(n,u).then((function(e){return e})));case 4:case"end":return e.stop()}}),e,this)}))),function(e,t){return _.apply(this,arguments)})},{key:"save",value:(C=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/feedback",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return C.apply(this,arguments)})},{key:"validateProductNums",value:(P=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/validateProductNum",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return P.apply(this,arguments)})},{key:"loginBind",value:(U=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/bind",n={username:t.username,password:t.password},e.abrupt("return",this.post(r,n));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return U.apply(this,arguments)})},{key:"logisticsTracking",value:(x=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/logisticsTracking",n={code:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return x.apply(this,arguments)})},{key:"boxInfo",value:(k=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/boxInfo",n={boxCode:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return k.apply(this,arguments)})},{key:"orderDetail",value:(R=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/orderDetail",n={orderNo:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return R.apply(this,arguments)})},{key:"getPiHaoDetail",value:(w=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/getPiHaoDetail",n={pihao:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return w.apply(this,arguments)})},{key:"getProducePlanDetail",value:(y=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/inspect/getProducePlanDetail",n={productionErpNo:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"getProductDetail",value:(b=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/Mdinfo/getProductDetail",n={code:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return b.apply(this,arguments)})},{key:"validateAndGetScore",value:(m=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/validateAndGetScore",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"validateBottleInnerCodeAndGetScore",value:(g=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/validateBottleInnerCodeAndGetScore",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return g.apply(this,arguments)})},{key:"validateBottleInnerCodeAndGetChance",value:(v=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/validateBottleInnerCodeAndGetChance",e.abrupt("return",this.post(r,t));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"isRedpacket",value:(d=u(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.wechatRedpacketUrl+"/RedpacketForMiniProgram/isRedpacket",n={bottleCode:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return d.apply(this,arguments)})},{key:"getThisYearWineData",value:(l=u(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.baseUrl+"/product/thisYearOpenProdCount",e.abrupt("return",this.get(t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"getRedBox",value:(h=u(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.baseUrl+"/product/sendRedpacket?id="+t,e.abrupt("return",this.get(r).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return h.apply(this,arguments)})},{key:"confirmDonate",value:(f=u(regeneratorRuntime.mark((function e(t,r){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.baseUrl+"/product/happySeasonDonate?id="+t+"&blessing="+r,e.abrupt("return",this.get(n).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e,t){return f.apply(this,arguments)})},{key:"updateToRedBox",value:(p=u(regeneratorRuntime.mark((function e(t,r){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.baseUrl+"/product/happySeasonIntegralUpgrade?id="+t+"&upgrade="+r,e.abrupt("return",this.get(n).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e,t){return p.apply(this,arguments)})},{key:"upValidateResult",value:(c=u(regeneratorRuntime.mark((function e(t,r,n){var u,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u=this.baseUrl+"/product/upValidateResult",a={id:t,status:r,qrCode:n},e.abrupt("return",this.post(u,a).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return c.apply(this,arguments)})},{key:"getTopProdFromJifenShop",value:(s=u(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/member/getScanCodeRecommendGoods",r={is_top:1},e.abrupt("return",this.get(t,r).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})}]),o}();exports.default=o;