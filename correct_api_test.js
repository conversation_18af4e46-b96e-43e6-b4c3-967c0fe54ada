/**
 * 使用正确API地址的测试脚本
 */

const https = require('https');

// 你的Token信息
const authToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
const loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';

// 正确的API配置
const config = {
    baseUrl: 'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2',
    appId: 'wx489f950decfeb93e'
};

console.log('🔧 使用正确的API配置:');
console.log('Base URL:', config.baseUrl);
console.log('App ID:', config.appId);

// 解析Token
function decodeJWT(token) {
    try {
        const parts = token.split('.');
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        return payload;
    } catch (e) {
        return null;
    }
}

const authPayload = decodeJWT(authToken);
console.log('\n👤 会员信息:');
console.log('会员ID:', authPayload.memberInfo.id);
console.log('Token过期时间:', new Date(authPayload.expireTime * 1000).toLocaleString('zh-CN'));

// 构建请求头
function buildHeaders() {
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN',
        'Referer': `https://servicewechat.com/${config.appId}/devtools/page-frame.html`,
        'X-Requested-With': 'XMLHttpRequest',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'Authorization': authToken,
        'login_code': loginCode,
        'X-WX-AppId': config.appId,
        'X-WX-Version': 'v3.2.6'
    };
}

// 测试API调用
function testAPI(path) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, config.baseUrl);
        
        const options = {
            hostname: url.hostname,
            port: 443,
            path: url.pathname + url.search,
            method: 'GET',
            headers: buildHeaders()
        };

        console.log(`\n🧪 测试API: ${path}`);
        console.log('完整URL:', url.toString());

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                console.log('响应头:', JSON.stringify(res.headers, null, 2));
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log('响应数据:', JSON.stringify(jsonData, null, 2));
                    resolve(jsonData);
                } catch (e) {
                    console.log('原始响应:', data);
                    resolve(data);
                }
            });
        });

        req.on('error', (error) => {
            console.error('请求错误:', error.message);
            reject(error);
        });

        req.setTimeout(10000, () => {
            console.log('请求超时');
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

// 执行测试
async function runTests() {
    try {
        console.log('\n🚀 开始API测试...');
        
        // 测试可能的API端点
        const testEndpoints = [
            '/garden/Gardenmemberinfo/getMemberInfo',
            '/garden/sorghum/index',
            '/garden/sign/dailySign',
            '/member/info',
            '/user/profile',
            '/plant/list'
        ];
        
        for (const endpoint of testEndpoints) {
            try {
                await testAPI(endpoint);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            } catch (error) {
                console.log(`❌ ${endpoint} 测试失败:`, error.message);
            }
        }
        
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

runTests();
