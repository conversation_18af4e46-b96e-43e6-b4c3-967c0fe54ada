Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./../../npm/wepy/lib/wepy.js"),n=(e=r)&&e.__esModule?e:{default:e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function r(){var e,t,n;o(this,r);for(var u=arguments.length,i=Array(u),c=0;c<u;c++)i[c]=arguments[c];return t=n=a(this,(e=r.__proto__||Object.getPrototypeOf(r)).call.apply(e,[this].concat(i))),n.data={url:""},n.methods={},n.config={},n.components={},a(n,t)}var u,i;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default.page),t(r,[{key:"onLoad",value:(u=regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.url=decodeURIComponent(t.url);case 1:case"end":return e.stop()}}),e,this)})),i=function(){var e=u.apply(this,arguments);return new Promise((function(t,r){return function n(o,a){try{var u=e[o](a),i=u.value}catch(e){return void r(e)}if(!u.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(i)}("next")}))},function(e){return i.apply(this,arguments)})}]),r}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(u,"pages/basearea/order"));