Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),t=r(require("./../../npm/wepy/lib/wepy.js")),n=r(require("./../../mixins/base.js")),o=r(require("./../../mixins/pagination.js")),a=(r(require("./placeholder.js")),r(require("./../../api/identify.js")));function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function o(a,r){try{var i=t[a](r),s=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(s).then((function(e){o("next",e)}),(function(e){o("throw",e)}));e(s)}("next")}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=function(r){function c(){var e,t,r;u(this,c);for(var p=arguments.length,f=Array(p),h=0;h<p;h++)f[h]=arguments[h];return t=r=l(this,(e=c.__proto__||Object.getPrototypeOf(c)).call.apply(e,[this].concat(f))),r.options={addGlobalClass:!0},r.props={pageName:String,bgColor:String,iconColor:String,fontColor:String,showNav:Boolean,showHome:{type:Boolean,value:!0},isShowsearch:{type:Boolean,value:!0}},r.data={showPop:!1,navTop:"",windowHeight:"",navHeight:"",inputVale:"",page:{list:[]}},r.lifetimes={attached:function(){}},r.mixins=[n.default,o.default],r.methods=i({goto:function(e){var t=e.currentTarget.dataset.url;this.$navigate(t)},getInputValue:function(e){this.inputVale=e.detail.value,console.log(this.inputVale),this.$apply()},searchPostList:function(e){var t=this;return s(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return console.log("搜索值",e),n.next=3,a.default.postList(e);case 3:return t.page=n.sent,console.log(t.page),n.next=7,t.next();case 7:t.$apply();case 8:case"end":return n.stop()}}),n,t)})))()},navBack:function(){console.log("返回按钮"),wx.navigateBack({delta:1})},toIndex:function(){console.log("打开tab页面"),this.$apply()},goHome:function(){this.showPop=!this.showPop,this.$apply()},onClickHide:function(){this.showPop=!1,this.$apply()}},"goto",(function(e){var t=e.currentTarget.dataset.page;"index"===t?(wx.reLaunch({url:"/pages/index/index"}),this.onClickHide()):"cart"===t?(wx.reLaunch({url:"/pages/cart/cart"}),this.onClickHide()):"my"===t?(wx.reLaunch({url:"/pages/my/my"}),this.onClickHide()):"class"===t?(wx.reLaunch({url:"/pages/class/class"}),this.onClickHide()):"footprint"===t&&(wx.navigateTo({url:"/pages/my/footprint/footprint"}),this.onClickHide())})),l(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(c,t.default.component),e(c,[{key:"onLoad",value:function(){this.navHeight=t.default.$instance.globalData.navHeight,console.log("gao",this.navHeight),this.navTop=t.default.$instance.globalData.navTop,this.windowHeight=t.default.$instance.globalData.windowHeight,this.$apply()}}]),c}();exports.default=c;