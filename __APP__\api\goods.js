Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,o){for(var r=0;r<o.length;r++){var t=o[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}return function(o,r,t){return r&&e(o.prototype,r),t&&e(o,t),o}}(),o=t(require("./base.js")),r=t(require("./../utils/Page.js"));function t(e){return e&&e.__esModule?e:{default:e}}function s(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}function i(e,o){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!o||"object"!=typeof o&&"function"!=typeof o?e:o}var n=function(t){function n(){return s(this,n),i(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function, not "+typeof o);e.prototype=Object.create(o&&o.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(e,o):e.__proto__=o)}(n,o.default),e(n,null,[{key:"recommend",value:function(){var e=this,o=this.baseUrl+"/goods/recommend";return new r.default(o,(function(o){e._processGoodsData(o)}))}},{key:"list",value:function(e){var o=this,t=this.baseUrl+"/goods/list";return new r.default(t,(function(r){o._processGoodsDiscount(r,e),o._processGoodsData(r)}))}},{key:"stock",value:function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=this.baseUrl+"/goods/"+e+"/stock?sku="+o;return this.get(r).then((function(e){return e.stock}))}},{key:"categories",value:function(){var e=this,o=this.baseUrl+"/goods/inner_category";return this.get(o).then((function(o){return e._createGoodsCategories(o)}))}},{key:"getInfo",value:function(e,o){var r=this,t=this.baseUrl+"/goods/"+e;return this.get(t,{}).then((function(e){return r._processGoodsDiscount(e,o),r._processGoodsDetail(e)}))}},{key:"_createGoodsCategories",value:function(e){var o=[];return null!=e&&o.push.apply(o,function(e){if(Array.isArray(e)){for(var o=0,r=Array(e.length);o<e.length;o++)r[o]=e[o];return r}return Array.from(e)}(e.map((function(e){return{id:e.id,title:e.name}})))),{list:o,selectedId:o.length>0?o[0].id:null,scroll:!1}}},{key:"_processGoodsDetail",value:function(e){return this._processGoodsPreview(e),this._processSkuLable(e),this._processGoodsPriceRange(e),this._processGoodsPriceLabel(e),this._processGoodsPostFeeText(e),e}},{key:"_processGoodsDiscount",value:function(e,o){if(null!=o&&o.categories.some((function(o){return o==e.innerCid}))){var r=o.rate/100;if(e.goodsSkuInfo)e.goodsSkuInfo.goodsSkuDetails.forEach((function(e){var o=e.goodsSkuDetailBase,t=o.price;(null==e.originalPrice||t<e.originalPrice)&&(e.originalPrice=t),o.originalPrice=t,o.price=(t*r).toFixed(2)}));else e.originalPrice=e.sellPrice,e.sellPrice=(e.sellPrice*r).toFixed(2);e.discountRate=o.rate/10+"折",e.discountText="会员折扣",e.discount=!0}}},{key:"_processGoodsPostFeeText",value:function(e){var o=e.postFee,r="";r=o&&0!=o?"同城配送：￥"+o+" (支持自提)":"配送：免运费",e.feeText=r}},{key:"_processGoodsPriceRange",value:function(e){if(e.goodsSkuInfo&&e.goodsSkuInfo.goodsSkuDetails){var o=e.goodsSkuInfo.goodsSkuDetails,r=0,t=Number.MAX_VALUE;for(var s in o){var i=o[s].goodsSkuDetailBase;r=Math.max(i.price,r),t=Math.min(i.price,t)}e.maxPrice=r,e.minPrice=t}}},{key:"_processGoodsPriceLabel",value:function(e){var o=e.sellPrice;e.maxPrice&&e.minPrice&&(o=e.minPrice),e.priceLable=isNaN(e.priceLable)?o:o.toFixed(2)}},{key:"_processSkuLable",value:function(e){var o=e.goodsSkuInfo;if(o){for(var r=[],t=1;t<=3;t++){var s=o["prop"+t],i=o["value"+t];if(!s||!i)break;var n={key:s,value:i.split(",")};r.push(n)}e.labels=r}}},{key:"_processGoodsData",value:function(e){var o=e.name,r=e.sellPrice,t=e.originalPrice;o.length>12&&(e.simple_name=o.substring(0,12)+"..."),o.length>30&&(e.name=o.substring(0,30)+"..."),null!=t&&0!=t||(e.originalPrice=r),this._processGoodsPreview(e),this._processSkuLable(e),this._processGoodsPriceRange(e),this._processGoodsPriceLabel(e),this._processGoodsQuantity(e)}},{key:"_processGoodsQuantity",value:function(e){e.num=0}},{key:"_processGoodsPreview",value:function(e){var o=e.images;null==o||o.length<1||null==o[0].url?e.imageUrl="/images/icons/broken.png":e.imageUrl=o[0].url+"/medium"}}]),n}();exports.default=n;