Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=c(require("./../../npm/wepy/lib/wepy.js")),r=c(require("./../../api/auth.js")),n=c(require("./../../mixins/base.js")),a=(c(require("./../../api/map.js")),c(require("./../../mixins/input.js"))),o=c(require("./../../components/weui/tips.js")),i=c(require("./../../api/member.js")),u=(c(require("./../../components/weui/vcode.js")),c(require("./../../utils/Tips.js"))),s=c(require("./../../components/common/loading.js"));function c(e){return e&&e.__esModule?e:{default:e}}function f(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(a,o){try{var i=t[a](o),u=i.value}catch(e){return void r(e)}if(!i.done)return Promise.resolve(u).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(u)}("next")}))}}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(c){function d(){var e,r,c;l(this,d);for(var h=arguments.length,m=Array(h),v=0;v<h;v++)m[v]=arguments[v];return r=c=p(this,(e=d.__proto__||Object.getPrototypeOf(d)).call.apply(e,[this].concat(m))),c.data={init:!1,userInfo:{},endDate:""},c.methods={bindDateChange:function(e){this.date=e.detail.value,this.userInfo.birthday=this.date,console.log("this.userInfo.birthday ",this.userInfo.birthday),this.$apply()},radioChange:function(e){var t=e.detail.value;this.userInfo.sex=t},inputgetName:function(e){var t=e.detail.value;this.userInfo.realname=t},updateUserInfo:function(e){var r=this;e.detail;return f(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.validate()){e.next=2;break}return e.abrupt("return");case 2:return u.default.loading(),e.prev=3,e.next=6,i.default.saveYonyouMemberInfo(r.userInfo);case 6:n=e.sent,r.userInfo.ts=n.ts,r.input.sex=n.sex,e.next=17;break;case 11:return e.prev=11,e.t0=e.catch(3),console.log(e.t0),e.next=16,u.default.error("修改失败");case 16:return e.abrupt("return");case 17:return e.next=19,u.default.success("保存成功",3e3);case 19:t.default.navigateBack();case 20:case"end":return e.stop()}}),e,r,[[3,11]])})))()}},c.$repeat={},c.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},c.$events={},c.components={Tips:o.default,Loading:s.default},c.mixins=[n.default,a.default],c.config={navigationBarTitleText:"个人信息"},p(c,r)}var h,m;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,t.default.page),e(d,[{key:"onLoad",value:(m=f(regeneratorRuntime.mark((function e(){var r,n,a,o,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.default.setNavigationBarColor({backgroundColor:"#299192",frontColor:"#ffffff"}),r=Date.parse(new Date),n=new Date(r),a=n.getFullYear(),o=n.getDate()<10?"0"+n.getDate():n.getDate(),u=n.getMonth()+1<10?"0"+(n.getMonth()+1):n.getMonth()+1,this.endDate=a+"-"+u+"-"+o,e.next=9,i.default.getYonyouMemberInfo();case 9:this.input=e.sent,this.userInfo=this.input,this.loaded();case 12:case"end":return e.stop()}}),e,this)}))),function(){return m.apply(this,arguments)})},{key:"onShow",value:(h=f(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,r.default.login();case 3:if(e.sent){e.next=6;break}return e.abrupt("return");case 6:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"validate",value:function(){var e=[{value:this.userInfo.realname,method:"required",message:"请输入您的姓名"},{value:this.userInfo.phone,method:"required",message:"请输入联系电话"},{value:this.userInfo.phone,method:"tel",message:"请输入合法手机号码"}];return this.check(e)}}]),d}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(d,"pages/customer/registe_member"));