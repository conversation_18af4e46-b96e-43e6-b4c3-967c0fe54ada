Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),n=u(require("./../../npm/wepy/lib/wepy.js")),o=require("./../../npm/wepy-redux/lib/index.js"),r=u(require("./../../store/utils.js")),i=u(require("./../shop/notice.js"));function u(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var f=(0,o.connect)({notices:r.default.get("notices")})(e=function(e){function o(){var e,t,n;c(this,o);for(var r=arguments.length,u=Array(r),f=0;f<r;f++)u[f]=arguments[f];return t=n=a(this,(e=o.__proto__||Object.getPrototypeOf(o)).call.apply(e,[this].concat(u))),n.props={},n.data={},n.methods={open:function(e){this.$invoke("NoticeDialog","open",{content:e.content})}},n.components={NoticeDialog:i.default},n.events={},a(n,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(o,n.default.component),t(o,[{key:"onLoad",value:function(){}}]),o}())||e;exports.default=f;