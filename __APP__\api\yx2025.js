Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),r=require("./base.js"),n=(e=r)&&e.__esModule?e:{default:e};function i(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(i,u){try{var o=t[i](u),a=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(e){function r(){return u(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}var a,c,s,f,p;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(r,n.default),t(r,null,[{key:"getActiveInfo",value:(p=i(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/activity/yx2025/getActiveInfo",n={areaCode:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"addSign",value:(f=i(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/activity/yx2025/addSign",e.abrupt("return",this.post(r,t).then((function(e){return e})));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"getSignInfo",value:(s=i(regeneratorRuntime.mark((function e(t){var r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.jifenShopApiUrl+"/activity/yx2025/getSignInfo",n={id:t},e.abrupt("return",this.get(r,n).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"getSignList",value:(c=i(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.jifenShopApiUrl+"/activity/yx2025/getSignList",r={},e.abrupt("return",this.get(t,r).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"getAddressByLL",value:(a=i(regeneratorRuntime.mark((function e(t,r){var n,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.jifenShopApiUrl+"/activity/yx2025/getAddressByLL",i={lat:t,lng:r},e.abrupt("return",this.get(n,i).then((function(e){return e})));case 3:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})}]),r}();exports.default=a;