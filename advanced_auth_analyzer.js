/**
 * 高级认证分析器
 * 继续深入分析认证机制的更多细节
 */

const https = require('https');
const crypto = require('crypto');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AdvancedAuthAnalyzer {
    constructor() {
        // 已知的认证信息
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        this.validAuth = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZW1iZXJJbmZvIjp7ImlkIjo2ODY1MzU3fSwiZXhwaXJlVGltZSI6MTc0OTY0OTgwMn0.hj3ilAmlKVaBZD3rXebAc4fA0l6jcvlY3Xuj0LvXCeI';
        
        // 从源码分析中发现的API端点
        this.apiEndpoints = {
            // 主要认证API
            getJwt: 'https://wap.exijiu.com/index.php/API/Member/getJwt',
            createJwt: 'https://apimallwm.exijiu.com/api/api/v2/jifenCrm/createJwt',
            checkSession: 'https://xcx.exijiu.com/anti-channeling/public/index.php/api/v2/auth/checkSession',
            
            // 微信相关API
            wechatLogin: 'https://statistics.exijiu.com/garden/wechat/login',
            wechatAuth: 'https://statistics.exijiu.com/garden/wechat/auth',
            
            // 用户信息API
            memberInfo: 'https://wap.exijiu.com/index.php/API/garden/Gardenmemberinfo/getMemberInfo',
            jifenShopMemberInfo: 'https://wap.exijiu.com/index.php/API/Member/getJifenShopMemberInfo',
            
            // 其他发现的API
            saveLastAuth: 'https://wap.exijiu.com/index.php/API/Member/saveLastAuth',
            hasDataMsCenterUser: 'https://wap.exijiu.com/index.php/API/Member/hasDataMsCenterUser'
        };
        
        console.log('🔧 高级认证分析器初始化完成');
        console.log('🎯 继续深入分析认证机制');
    }

    /**
     * 分析JWT Token的详细结构
     */
    analyzeJWTStructure() {
        console.log('\n🔍 深度分析JWT Token结构...');
        
        const tokens = [
            { name: 'login_code', token: this.loginCode },
            { name: 'Authorization', token: this.validAuth }
        ];
        
        for (const { name, token } of tokens) {
            console.log(`\n📊 分析 ${name}:`);
            
            try {
                const parts = token.split('.');
                if (parts.length !== 3) {
                    console.log('❌ 不是有效的JWT格式');
                    continue;
                }
                
                // 解析Header
                const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
                console.log('📋 Header:', JSON.stringify(header, null, 2));
                
                // 解析Payload
                const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
                console.log('📋 Payload:', JSON.stringify(payload, null, 2));
                
                // 分析过期时间
                if (payload.expire || payload.expireTime || payload.exp) {
                    const expireTime = payload.expire || payload.expireTime || payload.exp;
                    const expireDate = new Date(expireTime * 1000);
                    const now = new Date();
                    const isExpired = expireDate < now;
                    
                    console.log('⏰ 过期时间分析:');
                    console.log(`  过期时间戳: ${expireTime}`);
                    console.log(`  过期日期: ${expireDate.toLocaleString('zh-CN')}`);
                    console.log(`  当前时间: ${now.toLocaleString('zh-CN')}`);
                    console.log(`  是否过期: ${isExpired ? '是' : '否'}`);
                    
                    if (!isExpired) {
                        const remainingTime = Math.floor((expireDate - now) / 1000 / 60 / 60 / 24);
                        console.log(`  剩余天数: ${remainingTime}天`);
                    }
                }
                
                // 分析用户信息
                if (payload.unionid) {
                    console.log('👤 用户信息:');
                    console.log(`  UnionID: ${payload.unionid}`);
                    console.log(`  用户ID: ${payload.user_id || '未知'}`);
                }
                
                if (payload.memberInfo) {
                    console.log('👤 会员信息:');
                    console.log(`  会员ID: ${payload.memberInfo.id}`);
                }
                
                // 分析签名
                console.log('🔐 签名分析:');
                console.log(`  签名: ${parts[2]}`);
                console.log(`  签名长度: ${parts[2].length}`);
                console.log(`  算法: ${header.alg}`);
                
            } catch (error) {
                console.log('❌ JWT解析失败:', error.message);
            }
        }
    }

    /**
     * 测试所有发现的API端点
     */
    async testAllAPIEndpoints() {
        console.log('\n🔍 测试所有发现的API端点...');
        
        const results = {};
        
        for (const [name, url] of Object.entries(this.apiEndpoints)) {
            console.log(`\n🧪 测试API: ${name}`);
            console.log(`📍 URL: ${url}`);
            
            try {
                // 测试不同的认证方式
                const authMethods = [
                    { name: '无认证', headers: {} },
                    { name: '仅login_code', headers: { 'login_code': this.loginCode } },
                    { name: '仅Authorization', headers: { 'Authorization': this.validAuth } },
                    { name: '完整认证', headers: { 'login_code': this.loginCode, 'Authorization': this.validAuth } }
                ];
                
                for (const authMethod of authMethods) {
                    const result = await this.testAPIEndpoint(url, authMethod);
                    
                    if (result.success || result.status === 200) {
                        console.log(`  ✅ ${authMethod.name}: 成功 (${result.status})`);
                        
                        if (!results[name]) results[name] = [];
                        results[name].push({
                            authMethod: authMethod.name,
                            status: result.status,
                            data: result.data
                        });
                        
                        // 显示响应数据
                        if (result.data && typeof result.data === 'object') {
                            console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                        }
                        
                        break; // 找到有效方法就停止
                    } else {
                        console.log(`  ❌ ${authMethod.name}: 失败 (${result.status})`);
                    }
                }
                
            } catch (error) {
                console.log(`  💥 ${name}: 异常 - ${error.message}`);
            }
        }
        
        return results;
    }

    /**
     * 分析认证流程的时序
     */
    async analyzeAuthenticationFlow() {
        console.log('\n🔍 分析认证流程的时序...');
        
        const flowSteps = [
            {
                name: '步骤1: 检查现有认证',
                action: async () => {
                    console.log('📋 检查存储中的authData...');
                    // 模拟检查本地存储
                    return { hasAuth: false, reason: '模拟过期' };
                }
            },
            {
                name: '步骤2: 获取新JWT',
                action: async () => {
                    console.log('📋 调用getJwt API...');
                    return await this.testAPIEndpoint(this.apiEndpoints.getJwt, {
                        name: '完整认证',
                        headers: { 'login_code': this.loginCode }
                    });
                }
            },
            {
                name: '步骤3: 验证新Authorization',
                action: async (previousResult) => {
                    if (previousResult && previousResult.data && previousResult.data.jwt) {
                        console.log('📋 验证新生成的Authorization...');
                        return await this.testAPIEndpoint(this.apiEndpoints.memberInfo, {
                            name: '新Authorization',
                            headers: { 
                                'Authorization': previousResult.data.jwt,
                                'login_code': this.loginCode
                            }
                        });
                    }
                    return { success: false, reason: '没有JWT可验证' };
                }
            }
        ];
        
        let previousResult = null;
        
        for (const step of flowSteps) {
            console.log(`\n${step.name}`);
            
            try {
                const result = await step.action(previousResult);
                
                if (result.success || result.status === 200) {
                    console.log('✅ 步骤成功');
                    previousResult = result;
                } else {
                    console.log('❌ 步骤失败:', result.reason || '未知原因');
                }
                
            } catch (error) {
                console.log('💥 步骤异常:', error.message);
            }
        }
    }

    /**
     * 分析Authorization的生命周期
     */
    analyzeAuthorizationLifecycle() {
        console.log('\n🔍 分析Authorization的生命周期...');
        
        // 解析当前Authorization
        try {
            const parts = this.validAuth.split('.');
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            
            console.log('📊 当前Authorization分析:');
            console.log('  会员ID:', payload.memberInfo?.id);
            console.log('  过期时间戳:', payload.expireTime);
            
            if (payload.expireTime) {
                const expireDate = new Date(payload.expireTime * 1000);
                const now = new Date();
                const remainingMs = expireDate - now;
                const remainingDays = Math.floor(remainingMs / (1000 * 60 * 60 * 24));
                const remainingHours = Math.floor((remainingMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                console.log('⏰ 生命周期信息:');
                console.log(`  过期日期: ${expireDate.toLocaleString('zh-CN')}`);
                console.log(`  剩余时间: ${remainingDays}天 ${remainingHours}小时`);
                console.log(`  状态: ${remainingMs > 0 ? '有效' : '已过期'}`);
                
                // 计算刷新时机
                const refreshThreshold = 24 * 60 * 60 * 1000; // 24小时
                const shouldRefresh = remainingMs < refreshThreshold;
                
                console.log('🔄 刷新建议:');
                console.log(`  是否需要刷新: ${shouldRefresh ? '是' : '否'}`);
                console.log(`  建议刷新时间: ${shouldRefresh ? '立即' : '过期前24小时'}`);
            }
            
        } catch (error) {
            console.log('❌ Authorization解析失败:', error.message);
        }
    }

    /**
     * 分析安全机制
     */
    analyzeSecurityMechanisms() {
        console.log('\n🔍 分析安全机制...');
        
        console.log('🔐 JWT安全分析:');
        
        // 分析签名算法
        try {
            const authParts = this.validAuth.split('.');
            const authHeader = JSON.parse(Buffer.from(authParts[0], 'base64').toString());
            
            const loginParts = this.loginCode.split('.');
            const loginHeader = JSON.parse(Buffer.from(loginParts[0], 'base64').toString());
            
            console.log('📋 签名算法:');
            console.log(`  Authorization使用: ${authHeader.alg}`);
            console.log(`  login_code使用: ${loginHeader.alg}`);
            
            if (authHeader.alg === 'HS256') {
                console.log('⚠️ 使用对称加密算法，密钥安全性关键');
            }
            
        } catch (error) {
            console.log('❌ 签名算法分析失败:', error.message);
        }
        
        console.log('\n🛡️ 安全建议:');
        console.log('1. JWT使用HS256算法，需要保护密钥安全');
        console.log('2. Authorization有较长的有效期，适合长期使用');
        console.log('3. login_code有较短的有效期，适合临时认证');
        console.log('4. 建议定期刷新Authorization以提高安全性');
    }

    /**
     * 测试单个API端点
     */
    async testAPIEndpoint(url, authMethod) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/v3.2.6',
            ...authMethod.headers
        };
        
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                port: 443,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: headers,
                timeout: 8000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', () => {
                resolve({ success: false, status: 0, data: null });
            });
            
            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, status: 0, data: null });
            });

            req.end();
        });
    }

    /**
     * 运行完整的高级分析
     */
    async runAdvancedAnalysis() {
        console.log('🚀 开始高级认证分析...');
        console.log('🎯 深入分析认证机制的更多细节');
        
        try {
            // 1. JWT结构分析
            console.log('\n' + '='.repeat(60));
            console.log('📊 第一部分: JWT结构深度分析');
            console.log('='.repeat(60));
            this.analyzeJWTStructure();
            
            // 2. API端点测试
            console.log('\n' + '='.repeat(60));
            console.log('🔍 第二部分: API端点全面测试');
            console.log('='.repeat(60));
            const apiResults = await this.testAllAPIEndpoints();
            
            // 3. 认证流程分析
            console.log('\n' + '='.repeat(60));
            console.log('🔄 第三部分: 认证流程时序分析');
            console.log('='.repeat(60));
            await this.analyzeAuthenticationFlow();
            
            // 4. Authorization生命周期
            console.log('\n' + '='.repeat(60));
            console.log('⏰ 第四部分: Authorization生命周期分析');
            console.log('='.repeat(60));
            this.analyzeAuthorizationLifecycle();
            
            // 5. 安全机制分析
            console.log('\n' + '='.repeat(60));
            console.log('🔐 第五部分: 安全机制分析');
            console.log('='.repeat(60));
            this.analyzeSecurityMechanisms();
            
            // 6. 输出综合结论
            console.log('\n' + '='.repeat(60));
            console.log('📊 高级分析综合结论');
            console.log('='.repeat(60));
            
            console.log('\n🎯 关键发现:');
            console.log('1. Authorization使用HS256算法，有效期约6个月');
            console.log('2. login_code用于获取新的Authorization');
            console.log('3. 认证流程: login_code → getJwt → Authorization');
            console.log('4. Authorization可以直接用于所有需要认证的API');
            
            console.log('\n💡 实用建议:');
            console.log('1. 优先使用现有的Authorization（有效期长）');
            console.log('2. 当Authorization过期时，使用login_code获取新的');
            console.log('3. 定期检查Authorization的有效期');
            console.log('4. 保存Authorization到本地存储以便重复使用');
            
            const successfulAPIs = Object.keys(apiResults).length;
            console.log(`\n📈 测试结果: 发现 ${successfulAPIs} 个可用的API端点`);
            
            return {
                jwtAnalysis: true,
                apiResults: apiResults,
                authFlow: true,
                lifecycle: true,
                security: true
            };
            
        } catch (error) {
            console.log('\n❌ 高级分析失败:', error.message);
            return null;
        }
    }
}

// 导出类
module.exports = AdvancedAuthAnalyzer;

// 如果直接运行此文件
if (require.main === module) {
    const analyzer = new AdvancedAuthAnalyzer();
    
    console.log('🔍 高级认证分析器');
    console.log('🎯 继续深入分析认证机制的更多细节');
    console.log('📊 包括JWT结构、API测试、流程分析、安全机制等');
    console.log('');
    
    // 运行高级分析
    analyzer.runAdvancedAnalysis().then(result => {
        if (result) {
            console.log('\n🎊 高级认证分析完成！');
            console.log('🔑 已深入分析认证机制的各个方面');
        } else {
            console.log('\n😔 高级分析遇到问题');
            console.log('💡 但已获得大量有价值的信息');
        }
    }).catch(error => {
        console.error('💥 分析异常:', error);
    });
}
