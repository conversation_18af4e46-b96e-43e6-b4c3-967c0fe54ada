<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="outter" wx:if="{{$TipsModal$showMsg}}">
        <view class="your_gift">
            <view class="hide_title">提示</view>
            <view class="hide_content">
                <view bindtap="$TipsModal$closeIt" class="close">X</view>
                <view class="msg">{{$TipsModal$tipMsg}}</view>
                <view bindtap="$TipsModal$closeIt" class="gotIt">确定</view>
            </view>
        </view>
    </view>
    <view class="improve-out" wx:if="{{init}}">
        <view class="impCont" style="height:auto;align-items:center;">
            <view class="leftcont">头像</view>
            <view bindtap="goUpdateAvatarAndNick" class="rightcont avatar-info">
                <view class="avatar">
                    <image class="image" mode="aspectFill" src="{{personage.head_imgurl||'http://wap.exijiu.cn/Public/MemberClub/forum/none-headimg.jpg'}}"></image>
                </view>
                <view style="font-size:24rpx;color:#999;padding:0 0 0 10rpx;">点击更新头像</view>
            </view>
        </view>
        <view class="impCont">
            <view class="leftcont">选择性别</view>
            <view class="rightcont">
                <radio-group bindchange="election" class="group">
                    <radio checked="{{personage.sex==1?true:false}}" id="1" value="男"></radio>
                    <label class="label-2-text" for="男">
                        <text>男</text>
                    </label>
                    <radio checked="{{personage.sex==2?true:false}}" id="2" value="女"></radio>
                    <label class="label-2-text" for="女">
                        <text>女</text>
                    </label>
                </radio-group>
            </view>
        </view>
        <view class="impCont">
            <view class="leftcont">姓名</view>
            <input bindinput="bangding1" class="choseArea" data-name="{{personage.real_name}}" placeholder="请输入姓名" value="{{personage.real_name}}"></input>
        </view>
        <view class="impCont">
            <view class="leftcont">联系电话</view>
            <input disabled bindinput="bangding2" class="choseArea" data-phone="{{personage.phone_no}}" placeholder="请输入电话号码" style="width:320rpx;" value="{{personage.phone_no}}"></input>
            <button bindgetphonenumber="getPhoneNumber" class="phone-btn" openType="getPhoneNumber" wx:if="{{personage.phone_no.length!=11}}">
                <text>修改手机号</text>
            </button>
        </view>
        <view class="impCont" wx:if="{{personage.is_make_update==1&&personage.id_card!=''}}">
            <view class="leftcont">身份证号</view>
            <input disabled bindblur="getbirth" bindinput="bangding3" class="choseArea" data-num="{{personage.id_card}}" placeholder="请输入身份证号码" value="{{personage.id_card}}"></input>
        </view>
        <view class="impCont" wx:else>
            <view class="leftcont">身份证号</view>
            <input bindblur="getbirth" bindinput="bangding3" class="choseArea" data-num="{{idcards}}" placeholder="请输入身份证号码" value="{{idcards}}"></input>
        </view>
        <view class="impCont">
            <view class="leftcont">生日</view>
            <input disabled class="choseArea" placeholder="输入身份证自动获取" value="{{personage.birthday||personage.birthday}}"></input>
        </view>
        <view class="impCont" wx:if="{{personage.is_make_update==1&&personage.id_card!=''}}">
            <view class="leftcont">所在地区</view>
            <view class="item flex">
                <input disabled class="choseArea" placeholder="请选择所在地区" value="{{Addressv||where}}"></input>
            </view>
        </view>
        <view class="impCont" wx:else>
            <view class="leftcont">所在地区</view>
            <view class="item flex">
                <input bindtap="bindPickerChange" class="choseArea" placeholder="请选择所在地区" value="{{Addressv||where}}"></input>
            </view>
        </view>
        <view class="impCont">
            <view class="leftcont">详细地址</view>
            <input bindinput="bangding4" class="rightcont" data-place="{{personage.address}}" disabled="{{personage.is_make_update==1?true:false}}" placeholder="请输入详细地址" value="{{personage.address}}"></input>
        </view>
        <view class="agree">
            <checkbox-group bindchange="checkIt">
                <checkbox checked="{{checked}}">我已阅读并同意</checkbox>
            </checkbox-group>
            <text bindtap="goRules" class="limits" data-wpygorules-a="user">用户服务协议</text>
            <text bindtap="goRules" class="limits" data-wpygorules-a="privacy">隐私保护指引</text>
        </view>
        <view bindtap="preservations" class="save" data-btn-name="个人信息保存" wx:if="{{personage.is_make_update==1&&personage.id_card!=''}}">保存</view>
        <view bindtap="preservation" class="save" data-btn-name="个人信息保存(首次)" wx:else>保存</view>
        <view class="explain">
            <view>完善个人信息说明</view>
            <view>1.输入身份证实名认证可以获得500积分，完善生日信息，就有机会获得生日大礼包。</view>
            <view>2.注销后会员信息及积分将清空，无法恢复。</view>
            <view>3.退出登录后，重新登录需验证手机号。</view>
        </view>
        <view style="width:100%;height:100rpx;"></view>
        <view style="display:flex;flex-direction:row;justify-content:space-between;width:100%;height:100rpx;position:fixed;bottom:0px;">
            <view bindtap="unbindwechat" class="unbindwechat">退出登录</view>
            <view bindtap="cancellation" class="unbindwechat">注销</view>
        </view>
        <view wx:if="{{is_hidden}}">
            <view>
                <view class="pop-alert-box dialog">
                    <view class="alert-content-box">
                        <view class="report_content">请输入身份证验证是否本人操作</view>
                        <view class="alert-content">
                            <form bindsubmit="confirm" style="width:100%">
                                <view class="form-background">
                                    <view class="form-area">
                                        <view class="row">
                                            <text class="form-label">身份证号</text>
                                            <input bindinput="getIdcards" class="weui-input" data-num="{{idcards}}" placeholder="请输入身份证号码" style="color:#fe982c" value="{{idcards}}"></input>
                                        </view>
                                    </view>
                                </view>
                                <view bindtap="goSubmit" class="btn-area">
                                    <view catchtap="closeActivityWindow" style="width:160rpx;height:60rpx;background:#024236;display:flex;align-items:center;justify-content:center;border-radius:10rpx;color:#fff;">取消</view>
                                    <view style="width:160rpx;height:60rpx;background:#fe982c;display:flex;align-items:center;justify-content:center;border-radius:10rpx;color:#fff;">提交</view>
                                </view>
                            </form>
                        </view>
                    </view>
                </view>
            </view>
            <view class="alert_mask"></view>
        </view>
        <view class="mask" wx:if="{{flag}}">
            <view class="maskContent">
                <view class="btnbar">
                    <view bindtap="nocv" class="click" style="font-size:30rpx;">取消</view>
                    <view bindtap="yes" class="click" style="font-size:30rpx;">确定</view>
                </view>
                <view class="deed">
                    <scroll-view class="sss" scrollY="true">
                        <view bindtap="province1" class="ttt" data-index="{{bindex}}" style="{{ID==bindex?'background-color: #ccc;':''}}" wx:for="{{provinces}}" wx:for-index="bindex" wx:key="bindex">{{item.name}}</view>
                    </scroll-view>
                    <scroll-view class="sss" scrollY="true">
                        <view bindtap="city1" class="ttt" data-index="{{bindex}}" style="{{ID1==bindex?'background-color: #ccc;':''}}" wx:for="{{citys}}" wx:for-index="bindex" wx:key="bindex">{{item.name}}</view>
                    </scroll-view>
                    <scroll-view class="sss" scrollY="true">
                        <view bindtap="district1" class="ttt" data-index="{{bindex}}" style="{{ID2==bindex?'background-color: #ccc;':''}}" wx:for="{{areas}}" wx:for-index="bindex" wx:key="bindex">{{item.name}}</view>
                    </scroll-view>
                </view>
            </view>
        </view>
    </view>
</view>
