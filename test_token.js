/**
 * Token测试脚本
 * 快速验证你的Token是否有效
 */

const WxAutoPlantBot = require('./wx_auto_plant_complete.js');

async function testToken() {
    console.log('🔍 开始测试Token有效性...\n');
    
    const bot = new WxAutoPlantBot();
    
    // 检查Token状态
    const isValid = bot.checkTokenStatus();
    if (!isValid) {
        console.log('❌ Token已过期，无法继续测试');
        return;
    }
    
    console.log('\n🧪 开始API测试...');
    
    try {
        // 测试1: 获取用户信息
        console.log('\n1️⃣ 测试获取用户信息...');
        const userInfo = await bot.getUserInfo();
        if (userInfo) {
            console.log('✅ 用户信息获取成功');
        }
        
        // 测试2: 获取土地信息
        console.log('\n2️⃣ 测试获取土地信息...');
        const soilList = await bot.getSoilList();
        if (soilList.length > 0) {
            console.log('✅ 土地信息获取成功');
            console.log(`📊 土地状态统计:`);
            
            const statusCount = {};
            soilList.forEach(soil => {
                statusCount[soil.status] = (statusCount[soil.status] || 0) + 1;
            });
            
            Object.entries(statusCount).forEach(([status, count]) => {
                const statusName = getStatusName(status);
                console.log(`   状态${status}(${statusName}): ${count}块`);
            });
        }
        
        // 测试3: 每日签到
        console.log('\n3️⃣ 测试每日签到...');
        await bot.dailySign();
        
        console.log('\n🎉 所有测试完成！Token有效，可以正常使用自动种植功能。');
        console.log('\n💡 使用建议:');
        console.log('- 运行 node wx_auto_plant_complete.js 启动自动种植');
        console.log('- 脚本会每30分钟自动执行一次种植任务');
        console.log('- 按 Ctrl+C 可以停止脚本');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        if (error.code === 10001 || error.code === 10002) {
            console.log('🔧 建议: Token可能已过期，请获取新的Token');
        } else {
            console.log('🔧 建议: 检查网络连接或稍后重试');
        }
    }
}

function getStatusName(status) {
    const statusMap = {
        '0': '空地',
        '1': '已种植',
        '2': '可收获',
        '10': '需浇水',
        '11': '需施肥'
    };
    return statusMap[status] || '未知';
}

// 运行测试
testToken();
