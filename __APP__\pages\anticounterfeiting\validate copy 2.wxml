<view class="container" wx:if="{{init}}">
    <view wx:if="{{isFwInvisibleCode=='true'}}">
        <view bindtap="showValidateBox" data-wpyshowvalidatebox-a="bottle_body" style="width:90%;margin:20rpx auto;{{type=='bottle_body'?'border: 2px #08625a solid;':''}}">
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/1.jpg" style="width:100%;"></image>
            <form bindsubmit="confirm" wx:if="{{type=='bottle_body'}}">
                <input class="weui-input" name="qrCode" style="display:none" value="{{validatenum.qrCode}}"></input>
                <view class="page-section" style="width:85%;margin:20rpx auto;">
                    <input class="weui-input" id="securitycode" maxlength="4" minlength="4" name="theLast4Digits" placeholder="请输入瓶身防伪码第二排后4位验证" style="padding-left:20rpx;" value="{{validatenum.theLast4Digits}}"></input>
                </view>
                <view class="btn-area">
                    <button class="btn" formType="submit" plain="true" wx:if="{{dataMsCenterUser}}">
                        <text>立即验证</text>
                    </button>
                    <button bindgetphonenumber="getPhoneNumber" class="btn" openType="getPhoneNumber" plain="true" wx:if="{{!dataMsCenterUser}}">
                        <text>立即验证!</text>
                    </button>
                </view>
            </form>
        </view>
        <view bindtap="showValidateBox" data-wpyshowvalidatebox-a="bottle_cap" style="width:90%;margin:20rpx auto;{{type=='bottle_cap'?'border: 2px #08625a solid;':''}}">
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/2.jpg" style="width:100%;"></image>
            <form bindsubmit="confirm" wx:if="{{type=='bottle_cap'}}">
                <input class="weui-input" name="qrCode" style="display:none" value="{{validatenum.qrCode}}"></input>
                <view class="page-section" style="width:85%;margin:20rpx auto;">
                    <input class="weui-input" id="securitycode" maxlength="4" minlength="4" name="theLast4Digits" placeholder="请输入瓶盖内四位数验证码" style="padding-left:20rpx;" value="{{validatenum.theLast4Digits}}"></input>
                </view>
                <view class="btn-area">
                    <button class="btn" formType="submit" plain="true" wx:if="{{dataMsCenterUser}}">
                        <text>立即验证</text>
                    </button>
                    <button bindgetphonenumber="getPhoneNumber" class="btn" openType="getPhoneNumber" plain="true" wx:if="{{!dataMsCenterUser}}">
                        <text>立即验证!</text>
                    </button>
                </view>
            </form>
        </view>
        <view bindtap="showValidateBox" data-wpyshowvalidatebox-a="invisible_code" style="width:90%;margin:20rpx auto;{{type=='invisible_code'?'border: 2px #08625a solid;':''}}">
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/validate/3.jpg" style="width:100%;"></image>
            <form bindsubmit="confirm" wx:if="{{type=='invisible_code'}}">
                <view class="btn-area">
                    <button bindtap="goToValidate" class="btn" plain="true" wx:if="{{isFwInvisibleCode=='true'}}">
                        <text>点击进行标签验证</text>
                    </button>
                </view>
            </form>
        </view>
    </view>
    <view wx:else>
        <view style="width:90%;margin:20rpx auto;">
            <image mode="widthFix" src="http://wap.exijiu.cn/Public/MemberClubV2/images/new/jybyz-banner.png" style="width:100%;"></image>
        </view>
        <form bindsubmit="confirm">
            <input class="weui-input" name="qrCode" style="display:none" value="{{validatenum.qrCode}}"></input>
            <view class="page-section" style="width:85%;margin:20rpx auto;">
                <input class="weui-input" id="securitycode" maxlength="4" minlength="4" name="theLast4Digits" placeholder="请输入瓶身防伪码第二排后4位验证" style="padding-left:20rpx;" value="{{validatenum.theLast4Digits}}"></input>
            </view>
            <view class="btn-area">
                <button class="btn" formType="submit" plain="true" wx:if="{{dataMsCenterUser}}">
                    <text>立即验证</text>
                </button>
                <button bindgetphonenumber="getPhoneNumber" class="btn" openType="getPhoneNumber" plain="true" wx:if="{{!dataMsCenterUser}}">
                    <text>立即验证!</text>
                </button>
                <button bindtap="goToValidate" class="btn" plain="true" wx:if="{{isFwInvisibleCode=='true'}}">
                    <text>点击进行标签验证</text>
                </button>
            </view>
        </form>
    </view>
</view>
