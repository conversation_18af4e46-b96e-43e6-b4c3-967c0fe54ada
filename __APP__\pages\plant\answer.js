Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),t=a(require("./../../npm/wepy/lib/wepy.js")),i=(a(require("./../../api/member.js")),a(require("./../../api/duanwu.js")),a(require("./../../mixins/base.js"))),n=a(require("./../../components/common/loading.js")),s=a(require("./../../utils/Tips.js")),r=a(require("./../../api/garden.js")),o=a(require("./../../components/plant/head/modal.js"));function a(e){return e&&e.__esModule?e:{default:e}}function c(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,i){return function n(s,r){try{var o=t[s](r),a=o.value}catch(e){return void i(e)}if(!o.done)return Promise.resolve(a).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(a)}("next")}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=function(a){function h(){var e,t,a;u(this,h);for(var p=arguments.length,d=Array(p),f=0;f<p;f++)d[f]=arguments[f];return t=a=l(this,(e=h.__proto__||Object.getPrototypeOf(h)).call.apply(e,[this].concat(d))),a.data={chose:"chose",right:"right",wrong:"wrong",indexs:2,subject:["A.细胞核","B.细胞膜","C.细胞壁","D.细胞质"],submited:!1,type:"1",Nowindex:0,amount:"",sub_title:"小张发现水蕴草细胞的形状和软木塞细胞相似,它们都很规则、不易变形,这是因为都有( C )。",answer_analysis:"小张发现水蕴草细胞的形状和软木塞细胞相似,它们都很规则、不易变形,这是因为都有细胞壁及其他的一些解答内容。",rightAnswer:"",questions:[],selected:"",selected_more:[],clickId:"155",isActive:"",questionArr:[],rightWrong:!1,init:!1,righted:!1,tipMsg:"您没有答对不能获得奖励哦！ 请继续答题！",showMsg:!1,answerTitle:""},a.$repeat={},a.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"},TipsModal:{"xmlns:v-on":"","v-bind:tipMsg.sync":"tipMsg","v-bind:showMsg.sync":"showMsg"}},a.$events={TipsModal:{"v-on:closeIt":"closeIt"}},a.components={Loading:n.default,TipsModal:o.default},a.mixins=[i.default],a.methods={closeIt:function(){this.showMsg=!1,this.$apply()},changeWhite:function(e){var t=e.currentTarget.id;this.clickId=t,this.chose="chose",this.selected=this.subject[t].replace(/↵/g,"").replace(/ /g,"").substring(0,1),console.log(this.selected),this.$apply()},changeWhite2:function(e){this.chose="chose";var t=e.currentTarget.dataset.bindex;this.questionArr[t].checked=0==this.questionArr[t].checked;var i=this.questionArr[t].cont.replace(/↵/g,"").replace(/ /g,"").substring(0,1);if(1==this.questionArr[t].checked)this.selected_more.push(i);else{var n=this.selected_more.indexOf(i);this.selected_more.splice(n,1)}this.selected_more.sort(),console.log(this.selected_more),this.$apply()},addmin:function(e){this.submited=!this.submited,1==e&&this.Nowindex>0?(this.Nowindex--,this.$apply()):2==e&&this.Nowindex<this.amount-1?(this.Nowindex++,this.$apply()):(s.default.alert("到头啦!"),this.submited=!0),this.$apply(),this.getQueations()},submit:function(){var e=this;return c(regeneratorRuntime.mark((function t(){var i,n,s;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.chose){t.next=5;break}return e.tipMsg="您还没有选择答案",e.showMsg=!0,e.$apply(),t.abrupt("return");case 5:return e.submited=!e.submited,i=e.selected_more.join(",").replace(/,/g,""),console.log(e.selected_more),n=void 0,n=1==e.type?{answer:[{itemid:e.itemid,selected:""+e.selected}]}:{answer:[{itemid:e.itemid,selected:""+i}]},e.selected_more=[],t.next=13,r.default.answerResults(n);case 13:0!=(s=t.sent).err?(e.rightWrong=!1,e.answerTitle="回答错误"):0!==s.err||e.righted||(e.tipMsg=s.msg,e.showMsg=!0,e.righted=!0),0===s.err&&(e.rightWrong=!0,e.answerTitle="回答正确"),console.log(s),e.$apply();case 18:case"end":return t.stop()}}),t,e)})))()}},a.config={navigationBarBackgroundColor:"#fff",navigationBarTitleText:"每日一答",navigationBarTextStyle:"black"},l(a,t)}var p,d;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(h,t.default.page),e(h,[{key:"onLoad",value:(d=c(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getQueations();case 2:this.loaded();case 3:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"getQueations",value:(p=c(regeneratorRuntime.mark((function e(){var t,i,n,s,o,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.chose="",this.answerTitle="",e.next=4,r.default.Gardenquestiontask();case 4:if(t=e.sent,this.questions=t,this.amount=t.length,this.submited=!1,this.$apply(),console.log(t),i=this.Nowindex,1==t[i].type)n=(n=(n=t[i].options).split(" ")).filter((function(e){return e&&e.trim()})),this.subject=n,t[i].answer,this.sub_title=t[i].title,this.answer_analysis=t[i].answer_analysis,this.rightAnswer=t[i].answer,this.type=t[i].type,this.itemid=t[i].id,this.$apply(),console.log("单选题");else if(2==t[i].type){for(a in s=(s=(s=t[i].options).split(" ")).filter((function(e){return e&&e.trim()})),this.subject=s,t[i].answer,o=[],s)o.push({cont:s[a],checked:!1});console.log(o),this.questionArr=o,this.sub_title=t[i].title,this.answer_analysis=t[i].answer_analysis,this.rightAnswer=t[i].answer,this.type=t[i].type,this.itemid=t[i].id,this.$apply(),console.log("多选题")}this.$apply(),console.log(t);case 14:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})}]),h}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(h,"pages/plant/answer"));