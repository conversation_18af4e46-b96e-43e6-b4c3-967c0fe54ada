<view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
    <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
    <text class="muted mt20 lg">加载中</text>
</view>
<view class="container" wx:if="{{init}}">
    <view class="detail-box" style="background:#56a2cf">
        <view class="detail-title row-center">
            <text class="primary lg" style="height:100rpx;font-size:14px;line-height:100rpx;color:#fff;">发货单号：{{detail.orderHead.billNo}}</text>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb">
        <view class="tr bg-w">
            <view class="title">发货单号</view>
            <view class="value">{{detail.orderHead.billNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">下单日期</view>
            <view class="value">{{detail.orderHead.orderDate}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">发货时间</view>
            <view class="value">{{detail.orderHead.lastScanTime}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">上传时间</view>
            <view class="value">{{detail.orderHead.lastCommittedTime}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">发货单位编号</view>
            <view class="value">{{detail.orderHead.consignerNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">发货单位名称</view>
            <view class="value">{{detail.orderHead.consigner}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">接收单位编号</view>
            <view class="value">{{detail.orderHead.receiverNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">接收单位名称</view>
            <view class="value">{{detail.orderHead.receiver}}</view>
        </view>
    </view>
    <view class="table" style="background:#e4ebeb" wx:for="{{detail.orderBody}}" wx:for-index="key" wx:key="id">
        <view class="tr bg-w">
            <view class="title">产品编码</view>
            <view class="value">{{item.productErpNo}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">产品名称</view>
            <view class="value">{{item.productName}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">包装比例</view>
            <view class="value">{{item.packageRate}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">需求瓶数</view>
            <view class="value">{{item.needBottleQty}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">需求箱数</view>
            <view class="value">{{item.needCartonQty}}</view>
        </view>
        <view class="tr bg-w">
            <view class="title">扫描箱数</view>
            <view class="value">{{item.scanCartonQty}}</view>
        </view>
    </view>
</view>
