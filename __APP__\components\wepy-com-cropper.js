Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),t=o(require("./../npm/wepy/lib/wepy.js")),r=o(require("./../npm/we-cropper/dist/we-cropper.js"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var a=function(o){function a(){var e,t,r;n(this,a);for(var o=arguments.length,u=Array(o),p=0;p<o;p++)u[p]=arguments[p];return t=r=i(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(u))),r.data={$:null,id:"cropper",targetId:"targetCropper"},r.props={options:Object},r.computed={width:function(){return this.options.width},height:function(){return this.options.height},pixelRatio:function(){return this.options.pixelRatio}},r.methods={ts:function(e){this.$.touchStart(e)},tm:function(e){this.$.touchMove(e)},te:function(e){this.$.touchEnd(e)},canvasError:function(e){console.error(e.detail.errMsg)}},i(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,t.default.component),e(a,[{key:"pushOrigin",value:function(e){this.$.pushOrign(e)}},{key:"updateCanvas",value:function(){this.$.updateCanvas()}},{key:"getCropperImage",value:function(){return this.$.getCropperImage()}},{key:"getCropperBase64",value:function(e,t){this.$.getCropperImage(e)}},{key:"onLoad",value:function(){var e=this,t=this.options;t.id=this.id,t.targetId=this.targetId,this.$=new r.default(t).on("ready",(function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];e.$emit.apply(e,["ready"].concat(r))})).on("beforeImageLoad",(function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];e.$emit.apply(e,["beforeImageLoad"].concat(r))})).on("imageLoad",(function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];e.$emit.apply(e,["imageLoad"].concat(r))})).on("beforeDraw",(function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];e.$emit.apply(e,["beforeDraw"].concat(r))}))}}]),a}();exports.default=a;