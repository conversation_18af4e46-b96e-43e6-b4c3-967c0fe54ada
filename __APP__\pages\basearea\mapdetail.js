Object.defineProperty(exports,"__esModule",{value:!0});var e,t=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),n=require("./../../npm/wepy/lib/wepy.js"),o=(e=n)&&e.__esModule?e:{default:e};function r(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function o(r,a){try{var i=t[r](a),s=i.value}catch(e){return void n(e)}if(!i.done)return Promise.resolve(s).then((function(e){o("next",e)}),(function(e){o("throw",e)}));e(s)}("next")}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=null,u=function(e){function n(){var e,t,o;a(this,n);for(var u=arguments.length,c=Array(u),l=0;l<u;l++)c[l]=arguments[l];return t=o=i(this,(e=n.__proto__||Object.getPrototypeOf(n)).call.apply(e,[this].concat(c))),o.data={isShowMore:!1,indicatorDots:!1,autoplay:!0,interval:2e3,audio:{isPlaying:!1,currentTime:0,duration:0,isWarting:!1,currentTimeExp:"00:00",durationExp:"00:00"},zoneId:0,info:{title:"",desc:"",positionName:"",bannerList:[]},mapId:0,orgId:0,token:"",locInterval:null,beaconMap:"",showTimeInterval:null},o.methods={showMoreChange:function(){var e=this;return r(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.isShowMore=!e.isShowMore,e.$apply();case 2:case"end":return t.stop()}}),t,e)})))()},payClick:function(){this.data.audio.isPlaying?s.pause():s.play(),this.setData({"audio.isPlaying":!this.data.audio.isPlaying})},sliderChange:function(e){var t=e.detail.value,n=s.duration*t/100;s.seek(n),this.data.audio.isPlaying&&(console.log("sliderChange"),setTimeout((function(){s.play()}),500))},sliderChanging:function(){console.log("sliderChanging"),s.pause()}},o.config={},o.components={},i(o,t)}var u,c,l,p,d,f,h,g,m,y,v;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,o.default.page),t(n,[{key:"onLoad",value:(v=r(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.zoneId=t.zoneId,this.mapId=t.mapId,this.orgId=t.orgId,this.token=t.token,e.next=6,this.initAudio();case 6:this.beaconMap=new Map,this.token&&(n=this,this.locInterval=setInterval((function(){n.getLoc()}),2e3)),this.$apply();case 9:case"end":return e.stop()}}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"onShow",value:(y=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this,setTimeout((function(){t.scanBluetooth()}),2e3),this.showTimeInterval=setInterval((function(){t.uploadBeacon()}),3e3);case 3:case"end":return e.stop()}}),e,this)}))),function(){return y.apply(this,arguments)})},{key:"initAudio",value:(m=r(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this,s=wx.createInnerAudioContext(),e.next=4,this.getDetail();case 4:s.onPlay((function(){console.info("开始播放")})),s.onTimeUpdate((function(){console.info(s.currentTime,s.duration),t.setData({"audio.currentTime":s.currentTime,"audio.duration":s.duration,"audio.percent":s.currentTime/s.duration*100,"audio.currentTimeExp":n.timeFormat(s.currentTime),"audio.durationExp":n.timeFormat(s.duration)})})),s.onWaiting((function(){console.log("waiting")})),s.onCanplay((function(){console.log("canplay"),s.duration,setTimeout((function(){t.setData({"audio.durationExp":n.timeFormat(s.duration)})}),2e3),setTimeout((function(){n.firstPlay()}),3e3)})),s.onStop((function(){console.info("停止播放")})),s.onEnded((function(){console.info("结束播放"),s.stop(),t.setData({"audio.isPlaying":!1})})),s.onError((function(e){console.log(e.errMsg),console.log(e.errCode)})),this.$apply();case 12:case"end":return e.stop()}}),e,this)}))),function(){return m.apply(this,arguments)})},{key:"onHide",value:(g=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s.stop(),s.destroy(),clearInterval(this.locInterval),this.locInterval=null,this.stopScanBluetooth(),clearInterval(this.showTimeInterval),this.showTimeInterval=null;case 7:case"end":return e.stop()}}),e,this)}))),function(){return g.apply(this,arguments)})},{key:"onUnload",value:(h=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s.stop(),s.destroy(),clearInterval(this.locInterval),this.locInterval=null,this.stopScanBluetooth(),clearInterval(this.showTimeInterval),this.showTimeInterval=null;case 7:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"timeFormat",value:function(e){var t=parseInt(e/3600);e%=3600;var n=parseInt(e/60),o=parseInt(e%60);return t>0?this.timeFormatZero(t+":"+n+":"+o):this.timeFormatZero(n+":"+o)}},{key:"timeFormatZero",value:function(e){for(var t="",n=e.split(":"),o=0;o<n.length-1;o++)t+=1==n[o].length?"0"+n[o]:n[o],t+=":";return t+=1==n[o].length?"0"+n[o]:n[o]}},{key:"firstPlay",value:(f=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=this).data.audio.currentTime<=0&&!t.data.audio.isPlaying&&(s.play(),t.setData({"audio.isPlaying":!0})),this.$apply();case 3:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"getDetail",value:(d=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this,wx.request({url:"https://zhdl-xcx.gzxijiu.com/coolfar/app/appRequest",data:{action:"getPOIStoryList",client:{},data:{zoneId:this.zoneId}},method:"POST",success:function(e){t.setData({"info.title":e.data.data[0].title,"info.desc":e.data.data[0].storyContent,"info.positionName":e.data.data[0].title,"info.bannerList":e.data.data[0].storyImageList}),s.src=e.data.data[0].storyPath}}),this.$apply();case 3:case"end":return e.stop()}}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"getLoc",value:(p=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this,wx.request({url:"https://zhdl-loc.gzxijiu.com/miniprogram/rest/getLoc",data:{token:t.data.token,orgId:t.data.orgId,mapId:t.data.mapId},method:"POST",success:function(e){console.log("GETlOC",JSON.stringify(e)),console.log("GETlOC2",e),console.log("GETlOC3",e.data),e=e.data,console.log("GETlOC5",e),e.ok&&(console.log("ok"),e.data.poiId!=t.zoneId&&(console.log("change"),t.zoneId=e.data.poiId,s.stop(),s.destroy(),t.audio={isPlaying:!1,currentTime:0,duration:0,isWarting:!1,currentTimeExp:"00:00",durationExp:"00:00"},t.initAudio()))}}),this.$apply();case 3:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"scanBluetooth",value:(l=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this,console.log("开始扫描蓝牙"),wx.startBeaconDiscovery({uuids:["FDA50693-A4E2-4FB1-AFCF-C6EB07647825"],success:function(){console.log("开始扫描设备"),wx.onBeaconUpdate((function(e){var n;console.log("onBeaconUpdate-u-u"),console.log(e),console.log(e.beacons.length),console.log("蓝牙设备列表"),e&&e.beacons&&e.beacons.length>0&&(t.beaconMap=null,t.beaconMap=new Map,e.beacons.forEach((function(e){n=e.uuid+e.major+e.minor,t.beaconMap[n]=e})),console.log("beaconMap:"),console.log(t.beaconMap))}))},fail:function(){wx.showToast({title:"打开蓝牙失败",icon:"error",duration:1500})}});case 3:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"uploadBeacon",value:(c=r(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(n in t=[],this.beaconMap)t.push(this.beaconMap[n]);console.log("beaconList-u-u",JSON.stringify(t)),this.beaconMap=null,this.beaconMap=new Map,wx.request({url:"https://zhdl-loc.gzxijiu.com/miniprogram/rest/uploadBeacon",data:{token:this.token,beaconInfoList:t},method:"POST",success:function(e){console.log("uploadBeacon res"),console.log(e)}}),console.log("beaconList:"),console.log(t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"stopScanBluetooth",value:(u=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:wx.stopBeaconDiscovery({success:function(){console.log("停止扫描设备！")}}),wx.offBeaconUpdate((function(){console.log("取消监听 Beacon 设备更新事件")}));case 2:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})}]),n}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(u,"pages/basearea/mapdetail"));