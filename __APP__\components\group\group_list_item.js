Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e=function(){function e(e,o){for(var t=0;t<o.length;t++){var r=o[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(o,t,r){return t&&e(o.prototype,t),r&&e(o,r),o}}(),o=r(require("./../../npm/wepy/lib/wepy.js")),t=r(require("./../order/order_goods.js"));function r(e){return e&&e.__esModule?e:{default:e}}function n(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}function a(e,o){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!o||"object"!=typeof o&&"function"!=typeof o?e:o}var i=function(r){function i(){var e,o,r;n(this,i);for(var u=arguments.length,p=Array(u),s=0;s<u;s++)p[s]=arguments[s];return o=r=a(this,(e=i.__proto__||Object.getPrototypeOf(i)).call.apply(e,[this].concat(p))),r.props={group:{},action:{default:!0},selectedId:{}},r.data={expand:{},shopName:{}},r.methods={expand:function(e){e=null==e?0:e,this.expand[e]=!this.expand[e]},group:function(e,o){this.$root.$navigate("/pages/group/group_detail?groupId="+o)}},r.$repeat={group:{com:"OrderGoods",props:"goods"}},r.$props={OrderGoods:{"xmlns:v-bind":{value:"",for:"group.detail.order.orderGoodsInfos",item:"item",index:"index",key:"goodsId"},"v-bind:goods.once":{value:"item",type:"item",for:"group.detail.order.orderGoodsInfos",item:"item",index:"index",key:"goodsId"}}},r.$events={},r.components={OrderGoods:t.default},a(r,o)}return function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function, not "+typeof o);e.prototype=Object.create(o&&o.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(e,o):e.__proto__=o)}(i,o.default.component),e(i,[{key:"onLoad",value:function(){this.shopName=o.default.$instance.globalData.shopName}}]),i}();exports.default=i;