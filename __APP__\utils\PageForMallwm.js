Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,r,i){return r&&t(e.prototype,r),i&&t(e,i),e}}(),r=require("./Http.js"),i=(t=r)&&t.__esModule?t:{default:t};var n=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.url=e,this.list=[],this.offset=0,this.limit=10,this.processFunc=r,this.loading=!1,this.params=[],this.reachBottom=!1,this.empty=!0,this.toClear=!1}var r,n;return e(t,[{key:"next",value:(r=regeneratorRuntime.mark((function t(e){var r,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r={offset:this.offset,limit:this.limit},!this.loading){t.next=4;break}return console.warn("page loading!"),t.abrupt("return",this);case 4:return this.loading=!0,t.prev=5,Object.assign(r,e),t.next=9,i.default.get(this.url,r);case 9:if(!(null===(n=t.sent)||n.length<1)){t.next=13;break}return this.toClear?this.clear():this.reachBottom=!0,t.abrupt("return",this);case 13:return this.empty=!1,this._processData(n),this.toClear?(this.list=n,this.toClear=!1):this.list=this.list.concat(n),this.offset+=this.limit,n.length<this.limit&&(this.reachBottom=!0),t.abrupt("return",this);case 19:return t.prev=19,this.loading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[5,,19,22]])})),n=function(){var t=r.apply(this,arguments);return new Promise((function(e,r){return function i(n,s){try{var a=t[n](s),o=a.value}catch(t){return void r(t)}if(!a.done)return Promise.resolve(o).then((function(t){i("next",t)}),(function(t){i("throw",t)}));e(o)}("next")}))},function(t){return n.apply(this,arguments)})},{key:"reset",value:function(){this.empty=!0,this.toClear=!0,this.offset=0,this.reachBottom=!1}},{key:"clear",value:function(){this.toClear=!1,this.offset=0,this.list=[]}},{key:"_processData",value:function(t){if(this.processFunc)for(var e in t){var r=this.processFunc(t[e]);r&&(t[e]=r)}}}]),t}();exports.default=n;