Object.defineProperty(exports,"__esModule",{value:!0});var e=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t=o(require("./../../npm/wepy/lib/wepy.js")),r=(o(require("./../../api/auth.js")),o(require("./../../mixins/base.js"))),n=(o(require("./../../utils/Tips.js")),o(require("./../../api/member.js"))),i=o(require("./../../utils/WxUtils.js")),a=o(require("./../../components/common/loading.js"));o(require("./../../api/riskControl.js"));function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=function(o){function l(){var e,t,n;s(this,l);for(var i=arguments.length,o=Array(i),h=0;h<i;h++)o[h]=arguments[h];return t=n=u(this,(e=l.__proto__||Object.getPrototypeOf(l)).call.apply(e,[this].concat(o))),n.data={init:!1,url:"",userInfo:{},shareTitle:"",shareImg:"",sharePath:null},n.methods={},n.events={},n.$repeat={},n.$props={Loading:{"xmlns:v-bind":"","v-bind:init.sync":"init"}},n.$events={},n.components={Loading:a.default},n.mixins=[r.default],n.config={navigationBarTitleText:""},u(n,t)}var h,f;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(l,t.default.page),e(l,[{key:"onLoad",value:(h=regeneratorRuntime.mark((function e(r){var a,o,s,u,l,h,f,c,p=r.url,d=r.nbc,g=r.nfc,b=r.nbt,m=r.need_login,v=r.is_share,y=r.share_title,_=r.share_img,w=r.share_path;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s=b,(o=g)||(o="#ffffff"),(a=d)&&t.default.setNavigationBarColor({backgroundColor:a,frontColor:o}),s&&t.default.setNavigationBarTitle({title:s}),t.default.showShareMenu(),y&&(this.shareTitle=y),_&&(this.shareImg=_),w)console.log("share_path",w),this.sharePath=decodeURIComponent(w);else{for(h in l={},u={url:p,nbc:d,nfc:g,nbt:b,need_login:m,is_share:v,share_title:y,share_img:_})null!=(f=u[h])&&""!==f&&(l[h]=u[h]);c=i.default.httpBuildQuery(l),this.sharePath="pages/web/webviewForOutsourcing?"+c}if(1!=m){e.next=13;break}return e.next=13,n.default.isJifenLogined();case 13:this.userInfo=t.default.getStorageSync("userInfo"),this.url=decodeURIComponent(p),this.loaded();case 16:case"end":return e.stop()}}),e,this)})),f=function(){var e=h.apply(this,arguments);return new Promise((function(t,r){return function n(i,a){try{var o=e[i](a),s=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(s).then((function(e){n("next",e)}),(function(e){n("throw",e)}));t(s)}("next")}))},function(e){return f.apply(this,arguments)})},{key:"handleGetMessage",value:function(e){var t=e.detail.data[0];t.share_title&&(this.shareTitle=t.share_title),t.share_img&&(this.shareImg=t.share_img),t.share_path&&(this.sharePath=decodeURIComponent(t.share_path))}},{key:"onShareAppMessage",value:function(e){return{title:this.shareTitle,path:this.sharePath,imageUrl:this.shareImg}}}]),l}();Page(require("./../../npm/wepy/lib/wepy.js").default.$createPage(l,"pages/web/webviewForOutsourcing"));