<view>
    <view class="column-center loading-wrap" wx:if="{{!$Loading$init}}">
        <image class="loading-icon" src="/images/svg/audio.svg" style="margin-top:100rpx;"></image>
        <text class="muted mt20 lg">加载中</text>
    </view>
    <view class="ranking-out" wx:if="{{init}}">
        <view class="hot-activity">
            <view class="title">
                <view bindtap="current" class="nav {{current==1?'active':''}}" data-wpycurrent-a="1">全国活动</view>
                <view bindtap="current" class="nav {{current==2?'active':''}}" data-wpycurrent-a="2">片区活动</view>
            </view>
            <view class="activity-ul">
                <view bindtap="toDetail" class="li" data-id="{{item.id}}" data-type="{{item.type}}" data-url="{{item.navagate_url}}" wx:for="{{page.list}}" wx:key="index">
                    <image mode="aspectFill" src="{{item.image}}"></image>
                    <view class="cover" wx:if="{{item.is_overdue}}">
                        <view class="timeout"></view>
                    </view>
                </view>
                <view class="weui-loadmore" wx:if="{{$Loadmore$page.loading}}">
                    <view class="weui-loading"></view>
                    <view class="weui-loadmore__tips">正在加载</view>
                </view>
                <view class="weui-loadmore weui-loadmore_line weui-loadmore_dot" wx:if="{{!$Loadmore$page.empty&&$Loadmore$page.reachBottom}}">
                    <view class="weui-loadmore__tips weui-loadmore__tips_in-line weui-loadmore__tips_in-dot ">没有更多了</view>
                </view>
                <view class="weui-loadmore weui-loadmore_line" wx:if="{{!$Loadmore$page.loading&&$Loadmore$page.empty&&$Loadmore$emptyText}}">
                    <view class="weui-loadmore__tips weui-loadmore__tips_in-line ">{{$Loadmore$emptyText}}</view>
                </view>
            </view>
        </view>
    </view>
</view>
