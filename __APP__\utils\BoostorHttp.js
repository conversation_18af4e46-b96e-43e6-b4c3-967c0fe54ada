Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var e,t,r=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),n=o(require("./../npm/wepy/lib/wepy.js")),a=o(require("./Tips.js")),u=o(require("./Http.js"));function o(e){return e&&e.__esModule?e:{default:e}}function i(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,r){return function n(a,u){try{var o=t[a](u),i=o.value}catch(e){return void r(e)}if(!o.done)return Promise.resolve(i).then((function(e){n("next",e)}),(function(e){n("throw",e)}));e(i)}("next")}))}}var s=(t=e=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}var o,s;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,u.default),r(t,null,[{key:"request",value:(s=i(regeneratorRuntime.mark((function e(t,r,a){var u,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u={url:""+this.baseUrl+r,method:t,data:a},e.next=3,n.default.request(u);case 3:return o=e.sent,e.abrupt("return",this.interceptor(o));case 5:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return s.apply(this,arguments)})},{key:"interceptor",value:(o=i(regeneratorRuntime.mark((function e(t,r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isSuccess(t)){e.next=4;break}return e.abrupt("return",t.data.data);case 4:if(!t.data.err){e.next=24;break}if(5008!==t.data.err){e.next=8;break}return n.default.$instance.globalData.showSlideValidate=!0,e.abrupt("return");case 8:if(4013!==t.data.err){e.next=17;break}return e.next=11,a.default.modal(t.data.msg);case 11:return e.next=13,n.default.removeStorageSync("Authorization");case 13:return e.next=15,n.default.removeStorageSync("authData");case 15:throw wx.reLaunch({url:"/pages/plant/index"}),this.requestException(t);case 17:if(e.t0=!r,!e.t0){e.next=21;break}return e.next=21,wx.showToast({title:t.data.msg,icon:"none"});case 21:case 24:throw this.requestException(t);case 25:case"end":return e.stop()}}),e,this)}))),function(e,t){return o.apply(this,arguments)})},{key:"requestException",value:function(e){var t={};t.statusCode=e.statusCode;var r=e.data,n=r.data;return t.serverCode=r.code,t.message=r.msg,t.serverData=n,t}},{key:"custom",value:function(e,t){var r=this;return n.default.request(e).then((function(e){return r.interceptor(e,t),e}))}}]),t}(),e.baseUrl=n.default.$instance.globalData.jifenShopApiUrl,t);exports.default=s;