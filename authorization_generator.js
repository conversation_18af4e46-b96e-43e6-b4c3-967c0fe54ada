/**
 * Authorization生成器
 * 基于源码分析发现的完整生成逻辑
 */

const https = require('https');

// 忽略SSL证书验证
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

class AuthorizationGenerator {
    constructor() {
        // 基于源码发现的配置
        this.loginCode = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1bmlvbmlkIjoib0E0b0QxZmRkc2o4dHF3X1VVMlo1MmVXVFNwZyIsInVzZXJfaWQiOjAsImV4cGlyZSI6MTcyNjk0OTUzNX0.mY3I_Mfy1sMDPN2xRnfzKII1VQvLy38G0-N-YSI-PfY';
        
        // 从源码中发现的API配置
        this.baseUrl = 'https://wap.exijiu.com/index.php/API';
        this.jwtAPI = '/Member/getJwt';  // 关键API路径

        // 从搜索结果中发现的其他可能的JWT API
        this.alternativeAPIs = [
            '/Member/getJwt',
            '/Member/getJifenShopJwt',
            '/api/v2/jifenCrm/createJwt'
        ];
        
        // 小程序配置
        this.appConfig = {
            appId: 'wx489f950decfeb93e',
            version: 'v3.2.6'
        };
        
        console.log('🔧 Authorization生成器初始化完成');
        console.log('🎯 基于源码分析的完整生成逻辑');
        console.log('🔑 API路径:', this.jwtAPI);
    }

    /**
     * 构建请求头 (基于源码分析)
     */
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`,
            'Referer': `https://servicewechat.com/${this.appConfig.appId}/${this.appConfig.version}/page-frame.html`,
            'Origin': 'https://servicewechat.com',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            
            // 关键认证头部
            'login_code': this.loginCode
        };
    }

    /**
     * 调用getJifenShopJwt API (基于源码逻辑)
     */
    async getJifenShopJwt() {
        console.log('\n🔍 尝试调用JWT API...');

        // 尝试所有可能的API路径
        for (const apiPath of this.alternativeAPIs) {
            console.log(`\n📍 尝试API路径: ${apiPath}`);
            console.log('🌐 完整URL:', this.baseUrl + apiPath);

            try {
                const result = await this.makeRequest(apiPath, 'GET');

                if (result.success) {
                    console.log(`✅ ${apiPath} 调用成功!`);
                    console.log('📊 响应数据:', JSON.stringify(result.data, null, 2));
                    return result.data;
                } else {
                    console.log(`❌ ${apiPath} 调用失败 (状态: ${result.status})`);
                    console.log('📄 错误信息:', result.data?.msg || result.data?.message || '未知错误');
                }

            } catch (error) {
                console.log(`❌ ${apiPath} 请求异常: ${error.message}`);
            }
        }

        console.log('❌ 所有JWT API都调用失败');
        return null;
    }

    /**
     * 模拟getAuthData方法 (基于源码逻辑)
     */
    async getAuthData() {
        console.log('\n🔄 模拟getAuthData方法...');
        console.log('📋 基于源码发现的完整逻辑');
        
        try {
            // 步骤1: 模拟检查存储中的authData (假设过期)
            console.log('📋 步骤1: 检查存储中的authData (模拟为过期)');
            
            // 步骤2: 调用getJifenShopJwt
            console.log('📋 步骤2: 调用getJifenShopJwt...');
            const jwtResponse = await this.getJifenShopJwt();
            
            if (!jwtResponse) {
                throw new Error('getJifenShopJwt调用失败');
            }
            
            // 步骤3: 提取jwt字段
            console.log('📋 步骤3: 提取jwt字段...');
            const jwt = jwtResponse.jwt;
            
            if (!jwt) {
                console.log('❌ 响应中没有jwt字段');
                console.log('📊 可用字段:', Object.keys(jwtResponse));
                throw new Error('响应中没有jwt字段');
            }
            
            console.log('🔑 提取到jwt:', jwt);
            
            // 步骤4: 构建authData (基于源码逻辑)
            console.log('📋 步骤4: 构建authData...');
            const authData = {
                expire_time: jwtResponse.expire_time || (Math.floor(Date.now() / 1000) + 3600), // 1小时后过期
                authorized_token: jwt
            };
            
            console.log('📊 构建的authData:', JSON.stringify(authData, null, 2));
            
            // 步骤5: 模拟设置globalData.auth.Authorization
            console.log('📋 步骤5: 设置Authorization...');
            const authorization = authData.authorized_token;
            
            console.log('🎉 Authorization生成成功!');
            console.log('🔑 Authorization:', authorization);
            
            return {
                authData: authData,
                authorization: authorization
            };
            
        } catch (error) {
            console.log('❌ getAuthData模拟失败:', error.message);
            return null;
        }
    }

    /**
     * 验证生成的Authorization
     */
    async validateAuthorization(authorization) {
        console.log('\n🔍 验证生成的Authorization...');
        
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': authorization,
                'login_code': this.loginCode,
                'User-Agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN miniProgram/${this.appConfig.version}`
            };
            
            const result = await this.makeRequest('/garden/Gardenmemberinfo/getMemberInfo', 'GET', null, headers);
            
            if (result.success) {
                console.log('✅ Authorization验证成功!');
                console.log('👤 用户信息:', result.data.nick_name || '未知用户');
                return true;
            } else {
                console.log('❌ Authorization验证失败');
                console.log('📄 错误信息:', result.data?.msg || '未知错误');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Authorization验证异常:', error.message);
            return false;
        }
    }

    /**
     * HTTP请求
     */
    async makeRequest(path, method = 'GET', data = null, customHeaders = null) {
        const headers = customHeaders || this.buildHeaders();
        
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: 443,
                path: url.pathname + url.search,
                method: method,
                headers: headers,
                timeout: 10000,
                rejectUnauthorized: false
            };

            const req = https.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(responseData);
                        resolve({ 
                            success: res.statusCode === 200 && (jsonData.err === 0 || jsonData.code === 0),
                            data: jsonData,
                            status: res.statusCode
                        });
                    } catch (e) {
                        resolve({ 
                            success: false, 
                            data: responseData, 
                            status: res.statusCode
                        });
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (data && method === 'POST') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    /**
     * 生成新的Authorization
     */
    async generateAuthorization() {
        console.log('🚀 开始生成新的Authorization...');
        console.log('🎯 基于源码分析的完整流程');
        
        try {
            // 执行getAuthData逻辑
            const result = await this.getAuthData();
            
            if (!result) {
                console.log('❌ Authorization生成失败');
                return null;
            }
            
            const { authorization } = result;
            
            // 验证生成的Authorization
            const isValid = await this.validateAuthorization(authorization);
            
            if (isValid) {
                console.log('\n🎉 Authorization生成并验证成功!');
                console.log('🔑 新的Authorization:', authorization);
                console.log('\n💡 使用方法:');
                console.log('1. 将此Authorization设置为请求头');
                console.log('2. 配合login_code一起使用');
                console.log('3. 可以访问所有需要认证的API');
                
                return authorization;
            } else {
                console.log('❌ 生成的Authorization验证失败');
                return null;
            }
            
        } catch (error) {
            console.log('❌ Authorization生成异常:', error.message);
            return null;
        }
    }
}

// 导出类
module.exports = AuthorizationGenerator;

// 如果直接运行此文件
if (require.main === module) {
    const generator = new AuthorizationGenerator();
    
    console.log('🔑 Authorization生成器');
    console.log('🎯 基于源码分析的完整生成逻辑');
    console.log('📋 API路径: /Member/getJwt');
    console.log('');
    
    // 生成Authorization
    generator.generateAuthorization().then(authorization => {
        if (authorization) {
            console.log('\n🎊 Authorization生成成功!');
            console.log('🔑 你现在有了生成Authorization的完整方法!');
            console.log('');
            console.log('📋 生成流程总结:');
            console.log('1. 调用 GET /Member/getJwt (需要login_code头部)');
            console.log('2. 从响应的jwt字段获取token');
            console.log('3. 将jwt作为Authorization使用');
            console.log('4. 配合login_code访问所有API');
        } else {
            console.log('\n😔 Authorization生成失败');
            console.log('💡 可能的原因:');
            console.log('1. login_code可能已过期');
            console.log('2. API可能需要额外的认证参数');
            console.log('3. 请求头可能需要调整');
        }
    }).catch(error => {
        console.error('💥 生成异常:', error);
    });
}
