exports.__esModule=!0;var r=Object.assign||function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])}return r};exports.default=function(e){var o=e.dispatch;return function(e){return function(a){return t.isFSA(a)?n(a.payload)?a.payload.then((function(t){return o(r({},a,{payload:t}))}),(function(t){return o(r({},a,{payload:t,error:!0}))})):e(a):n(a)?a.then(o):e(a)}}};var t=require("./../../flux-standard-action/lib/index.js");function n(r){return r&&"function"==typeof r.then}module.exports=exports.default;