Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var t,e=function(){function t(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}return function(e,r,a){return r&&t(e.prototype,r),a&&t(e,a),e}}(),r=require("./ForumHttp.js"),a=(t=r)&&t.__esModule?t:{default:t};var i=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.url=e,this.list=[],this.rawData=[],this.pageIndex=1,this.pageSize=10,this.processFunc=r,this.loading=!1,this.params=[],this.reachBottom=!1,this.empty=!0,this.toClear=!1}var r,i;return e(t,[{key:"replayPostNext",value:(r=regeneratorRuntime.mark((function t(e){var r,i,n,s;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r={pageIndex:this.pageIndex,pageSize:this.pageSize},!this.loading){t.next=4;break}return console.warn("page loading!"),t.abrupt("return",this);case 4:return this.loading=!0,t.prev=5,Object.assign(r,e),console.log("====",this.url,r),t.next=10,a.default.post(this.url,r);case 10:if(i=t.sent,n=i.data.records,s=i.data,!(null===n||n.length<1)){t.next=16;break}return this.toClear?this.clear():this.reachBottom=!0,t.abrupt("return",this);case 16:return this.empty=!1,this._processData(n),this.toClear?(this.list=n,this.rawData=s,this.toClear=!1):(this.list=this.list.concat(n),this.rawData=this.rawData.concat(s)),this.pageIndex+=1,n.length<this.pageSize&&(this.reachBottom=!0),t.abrupt("return",this);case 22:return t.prev=22,this.loading=!1,t.finish(22);case 25:case"end":return t.stop()}}),t,this,[[5,,22,25]])})),i=function(){var t=r.apply(this,arguments);return new Promise((function(e,r){return function a(i,n){try{var s=t[i](n),o=s.value}catch(t){return void r(t)}if(!s.done)return Promise.resolve(o).then((function(t){a("next",t)}),(function(t){a("throw",t)}));e(o)}("next")}))},function(t){return i.apply(this,arguments)})},{key:"reset",value:function(){this.empty=!0,this.toClear=!0,this.pageIndex=0,this.reachBottom=!1}},{key:"clear",value:function(){this.toClear=!1,this.pageIndex=1,this.list=[],this.rawData=[]}},{key:"_processData",value:function(t){if(this.processFunc)for(var e in t){var r=this.processFunc(t[e]);r&&(t[e]=r)}}}]),t}();exports.default=i;